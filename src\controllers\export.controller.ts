import { FastifyReply } from 'fastify';
import { AuthenticatedRequest, UserRole } from '../types';
import { ExportService } from '../services/export.service';
import { logger } from '../config/logger';

export const exportUsers = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {

    if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
      return reply.status(403).send({
        success: false,
        message: 'admin access required'
      });
    }

    const {
      format = 'csv',
      dateFrom,
      dateTo,
      status,
      role,
      kycStatus
    } = request.query as any;

    const filters: any = {};
    if (status) filters.account_status = status;
    if (role) filters.role = role;
    if (kycStatus) filters.kyc_status = kycStatus;

    const result = await ExportService.exportUsers({
      format,
      dateFrom,
      dateTo,
      filters
    });

    if (!result.success) {
      return reply.status(500).send(result);
    }

    logger.info('Users export initiated', {
      adminId: request.user.id,
      format,
      filters,
      recordCount: result.data?.recordCount
    });

    return reply.status(200).send({
      success: true,
      message: result.message,
      data: result.data,
      metadata: {
        exportType: 'users',
        initiatedBy: request.user.id,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error exporting users:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to export users data'
    });
  }
};

export const exportTransactions = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {

    if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
      return reply.status(403).send({
        success: false,
        message: 'admin access required'
      });
    }

    const {
      format = 'csv',
      dateFrom,
      dateTo,
      status,
      type,
      currency,
      minAmount,
      maxAmount
    } = request.query as any;

    const filters: any = {};
    if (status) filters.status = status;
    if (type) filters.type = type;
    if (currency) filters.currency = currency;
    if (minAmount || maxAmount) {
      filters.amount = {};
      if (minAmount) filters.amount.$gte = parseFloat(minAmount);
      if (maxAmount) filters.amount.$lte = parseFloat(maxAmount);
    }

    const result = await ExportService.exportTransactions({
      format,
      dateFrom,
      dateTo,
      filters
    });

    if (!result.success) {
      return reply.status(500).send(result);
    }

    logger.info('Transactions export initiated', {
      adminId: request.user.id,
      format,
      filters,
      recordCount: result.data?.recordCount
    });

    return reply.status(200).send({
      success: true,
      message: result.message,
      data: result.data,
      metadata: {
        exportType: 'transactions',
        initiatedBy: request.user.id,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error exporting transactions:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to export transactions data'
    });
  }
};

export const exportAgents = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {

    if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
      return reply.status(403).send({
        success: false,
        message: 'admin access required'
      });
    }

    const {
      format = 'csv',
      dateFrom,
      dateTo,
      status,
      businessType,
      kycStatus,
      city,
      state
    } = request.query as any;

    const filters: any = {};
    if (status) {
      if (status === 'active') {
        filters.is_active = true;
      } else if (status === 'inactive') {
        filters.is_active = false;
      } else {
        filters.agent_status = status;
      }
    }
    if (businessType) filters.business_type = businessType;
    if (kycStatus) filters.kyc_status = kycStatus;
    if (city) filters['location.city'] = { $regex: city, $options: 'i' };
    if (state) filters['location.state'] = { $regex: state, $options: 'i' };

    const result = await ExportService.exportAgents({
      format,
      dateFrom,
      dateTo,
      filters
    });

    if (!result.success) {
      return reply.status(500).send(result);
    }

    logger.info('Agents export initiated', {
      adminId: request.user.id,
      format,
      filters,
      recordCount: result.data?.recordCount
    });

    return reply.status(200).send({
      success: true,
      message: result.message,
      data: result.data,
      metadata: {
        exportType: 'agents',
        initiatedBy: request.user.id,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error exporting agents:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to export agents data'
    });
  }
};


export const downloadExport = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {

    if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
      return reply.status(403).send({
        success: false,
        message: 'admin access required'
      });
    }

    const { filename } = request.params as any;

    if (!filename) {
      return reply.status(400).send({
        success: false,
        message: 'filename is required'
      });
    }

    const result = await ExportService.getExportFile(filename);

    if (!result.success) {
      return reply.status(404).send(result);
    }

    logger.info('Export file downloaded', {
      adminId: request.user.id,
      filename,
      fileSize: result.data?.size
    });

    //  headers
    reply.header('Content-Type', result.data?.mimeType);
    reply.header('Content-Disposition', `attachment; filename="${filename}"`);
    reply.header('Content-Length', result.data?.size);

    return reply.send(result.data?.content);

  } catch (error: any) {
    logger.error('Error downloading export file:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to download export file'
    });
  }
};


export const generateAdminReport = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {
    // check admin permissions
    if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
      return reply.status(403).send({
        success: false,
        message: 'admin access required'
      });
    }

    const {
      reportType = 'summary',
      format = 'csv',
      dateFrom,
      dateTo
    } = request.query as any;

    let result;

    switch (reportType) {
      case 'users':
        result = await ExportService.exportUsers({ format, dateFrom, dateTo });
        break;
      case 'transactions':
        result = await ExportService.exportTransactions({ format, dateFrom, dateTo });
        break;
      case 'agents':
        result = await ExportService.exportAgents({ format, dateFrom, dateTo });
        break;
      case 'summary':
      default:

      const [usersResult, transactionsResult, agentsResult] = await Promise.all([
          ExportService.exportUsers({ format, dateFrom, dateTo }),
          ExportService.exportTransactions({ format, dateFrom, dateTo }),
          ExportService.exportAgents({ format, dateFrom, dateTo })
        ]);

        result = {
          success: true,
          data: {
            users: usersResult.data,
            transactions: transactionsResult.data,
            agents: agentsResult.data,
            summary: {
              totalUsers: usersResult.data?.recordCount || 0,
              totalTransactions: transactionsResult.data?.recordCount || 0,
              totalAgents: agentsResult.data?.recordCount || 0
            }
          },
          message: 'comprehensive admin report generated successfully'
        };
        break;
    }

    if (!result.success) {
      return reply.status(500).send(result);
    }

    logger.info('Admin report generated', {
      adminId: request.user.id,
      reportType,
      format,
      dateFrom,
      dateTo
    });

    return reply.status(200).send({
      success: true,
      message: result.message,
      data: result.data,
      metadata: {
        reportType,
        format,
        generatedBy: request.user.id,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error generating admin report:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to generate admin report'
    });
  }
};
