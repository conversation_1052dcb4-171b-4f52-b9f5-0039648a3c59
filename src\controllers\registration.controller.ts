import { FastifyRequest, FastifyReply } from 'fastify';
import { User } from '../models/user.model';
import { VerificationService } from '../services/verification.service';
import { ValidationService } from '../services/validation.service';
// import * as UploadService from '../services/upload.service';
import { CryptoUtils } from '../utils/crypto';
import { AuthMiddleware } from '../middleware/auth.middleware';
import { logger, securityLogger } from '../config/logger';
import {
  RegistrationStep,
  Platform,
  VerificationStatus
} from '../types';

// step 1
export const initiateRegistration = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const validation = await ValidationService.validateData(request.body, ValidationService.registrationInitSchema);

    if (!validation.isValid) {
      return reply.status(400).send({
        success: false,
        message: 'validation failed',
        errors: validation.errors
      });
    }

    const { phone, email, platform } = validation.data;
    const ipAddress = request.ip;
    const userAgent = request.headers['user-agent'];

    const existingUser = await User.findOne({
      $or: [{ phone }, { email }]
    });

    if (existingUser) {
      securityLogger.warn('Registration attempt with existing credentials', {
        phone,
        email,
        platform,
        ipAddress,
        existingUserId: existingUser._id
      });

      return reply.status(409).send({
        success: false,
        message: 'user already exists with this phone or email'
      });
    }

    const user = new User({
      phone,
      email,
      registration_step: RegistrationStep.PHONE_EMAIL,
      registration_completed: false,
      phone_verified: false,
      email_verified: false
    });

    await user.save();

    const phoneVerification = await VerificationService.sendPhoneVerification(phone, platform, ipAddress);

    if (!phoneVerification.success) {
      return reply.status(400).send({
        success: false,
        message: phoneVerification.message,
        retryAfter: phoneVerification.retryAfter
      });
    }

    logger.info('Registration initiated', {
      userId: user._id,
      phone,
      email,
      platform,
      ipAddress,
      userAgent
    });

    return reply.status(201).send({
      success: true,
      message: 'registration initiated successfully',
      data: {
        userId: user._id.toString(),
        phone,
        email,
        currentStep: user.registration_step,
        nextStep: RegistrationStep.PHONE_VERIFICATION,
        verificationSent: true,
        registrationProgress: {
          currentStep: user.registration_step,
          phoneVerified: user.phone_verified,
          emailVerified: user.email_verified
        }
      }
    });

  } catch (error: any) {
    logger.error('Registration initiation error:', error);
    return reply.status(500).send({
      success: false,
      message: 'registration failed'
    });
  }
};

// step 2
export const verifyPhone = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const validation = await ValidationService.validateData(request.body, ValidationService.phoneVerificationSchema);

    if (!validation.isValid) {
      return reply.status(400).send({
        success: false,
        message: 'validation failed',
        errors: validation.errors
      });
    }

    const { phone, code, platform } = validation.data;
    const ipAddress = request.ip;

    const verification = await VerificationService.verifyPhoneCode(phone, code, platform, ipAddress);

    if (!verification.success) {
      return reply.status(400).send({
        success: false,
        message: verification.message
      });
    }

    const user = await User.findOne({ phone });
    if (!user) {
      return reply.status(404).send({
        success: false,
        message: 'user not found'
      });
    }

    user.registration_step = RegistrationStep.PHONE_VERIFICATION;
    await user.save();

    const emailVerification = await VerificationService.sendEmailVerification(user.email, platform, ipAddress);

    logger.info('Phone verification successful', {
      userId: user._id,
      phone,
      platform,
      ipAddress
    });

    return reply.status(200).send({
      success: true,
      message: 'phone verified successfully',
      data: {
        userId: user._id.toString(),
        phoneVerified: true,
        currentStep: user.registration_step,
        nextStep: RegistrationStep.EMAIL_VERIFICATION,
        emailVerificationSent: emailVerification.success,
        registrationProgress: {
          currentStep: user.registration_step,
          phoneVerified: user.phone_verified,
          emailVerified: user.email_verified
        }
      }
    });

  } catch (error: any) {
    logger.error('Phone verification error:', error);
    return reply.status(500).send({
      success: false,
      message: 'verification failed'
    });
  }
};

// step 3
export const verifyEmail = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const validation = await ValidationService.validateData(request.body, ValidationService.emailVerificationSchema);

    if (!validation.isValid) {
      return reply.status(400).send({
        success: false,
        message: 'validation failed',
        errors: validation.errors
      });
    }

    const { email, code, platform } = validation.data;
    const ipAddress = request.ip;

    const verification = await VerificationService.verifyEmailCode(email, code, platform, ipAddress);

    if (!verification.success) {
      return reply.status(400).send({
        success: false,
        message: verification.message
      });
    }

    const user = await User.findOne({ email });
    if (!user) {
      return reply.status(404).send({
        success: false,
        message: 'user not found'
      });
    }

    // update registration step
    user.registration_step = RegistrationStep.EMAIL_VERIFICATION;
    await user.save();

    logger.info('Email verification successful', {
      userId: user._id,
      email,
      platform,
      ipAddress
    });

    return reply.status(200).send({
      success: true,
      message: 'email verified successfully',
      data: {
        userId: user._id.toString(),
        emailVerified: true,
        currentStep: user.registration_step,
        nextStep: RegistrationStep.PERSONAL_INFO,
        registrationProgress: {
          currentStep: user.registration_step,
          phoneVerified: user.phone_verified,
          emailVerified: user.email_verified
        }
      }
    });

  } catch (error: any) {
    logger.error('Email verification error:', error);
    return reply.status(500).send({
      success: false,
      message: 'verification failed'
    });
  }
};

// step 4
export const completePersonalInfo = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const validation = await ValidationService.validateData(request.body, ValidationService.personalInfoSchema);

    if (!validation.isValid) {
      return reply.status(400).send({
        success: false,
        message: 'validation failed',
        errors: validation.errors
      });
    }

    const { firstName, lastName, dateOfBirth, password, platform, phone } = validation.data;
    const ipAddress = request.ip;

    if (password.length < 8) {
      return reply.status(400).send({
        success: false,
        message: 'password must be at least 8 characters long'
      });
    }

    const authHeader = request.headers.authorization;
    if (!authHeader) {
      return reply.status(401).send({
        success: false,
        message: 'authentication required'
      });
    }

    const user = await User.findOne({ phone });

    if (!user) {
      return reply.status(404).send({
        success: false,
        message: 'user not found'
      });
    }

    if (!user.phone_verified || !user.email_verified) {
      return reply.status(400).send({
        success: false,
        message: 'phone and email verification required first'
      });
    }

    const hashedPassword = await CryptoUtils.hashPassword(password);

    user.first_name = firstName;
    user.last_name = lastName;
    user.date_of_birth = new Date(dateOfBirth);
    user.password = hashedPassword;
    
    user.registration_step = RegistrationStep.PERSONAL_INFO;
    await user.save();

    logger.info('Personal information completed', {
      userId: user._id,
      platform,
      ipAddress
    });

    return reply.status(200).send({
      success: true,
      message: 'personal information saved successfully',
      data: {
        userId: user._id.toString(),
        firstName: user.first_name,
        lastName: user.last_name,
        currentStep: user.registration_step,
        nextStep: RegistrationStep.IDENTITY_VERIFICATION,
        registrationProgress: {
          currentStep: user.registration_step,
          phoneVerified: user.phone_verified,
          emailVerified: user.email_verified
        }
      }
    });

  } catch (error: any) {
    logger.error('Personal info completion error:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to save personal information'
    });
  }
};

// step 5
export const submitIdentityVerification = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const validation = await ValidationService.validateData(request.body, ValidationService.identityVerificationSchema);

    if (!validation.isValid) {
      return reply.status(400).send({
        success: false,
        message: 'validation failed',
        errors: validation.errors
      });
    }

    const { idType, idNumber, idDocumentFront, idDocumentBack, selfiePhoto, platform, phone } = validation.data;
    const ipAddress = request.ip;

    const user = await User.findOne({ phone });

    if (!user) {
      return reply.status(404).send({
        success: false,
        message: 'user not found'
      });
    }

    if (!user.phone_verified || !user.email_verified || !user.first_name || !user.password) {
      return reply.status(400).send({
        success: false,
        message: 'previous registration steps must be completed first'
      });
    }

    user.identity_verification.id_type = idType;
    user.identity_verification.id_number = idNumber;
    user.identity_verification.id_document_front = idDocumentFront || '';
    user.identity_verification.id_document_back = idDocumentBack || '';
    user.identity_verification.selfie_photo = selfiePhoto || '';
    user.identity_verification.verification_status = VerificationStatus.PENDING;

    user.registration_step = RegistrationStep.IDENTITY_VERIFICATION;
    await user.save();

    logger.info('Identity verification submitted', {
      userId: user._id,
      idType: idType,
      platform,
      ipAddress
    });

    return reply.status(200).send({
      success: true,
      message: 'identity verification submitted successfully',
      data: {
        userId: user._id.toString(),
        verificationStatus: user.identity_verification.verification_status,
        currentStep: user.registration_step,
        nextStep: RegistrationStep.TRANSACTION_PIN,
        registrationProgress: {
          currentStep: user.registration_step,
          phoneVerified: user.phone_verified,
          emailVerified: user.email_verified
        },
        estimatedProcessingTime: '24-48 hours',
        note: 'Your documents will be reviewed manually by our team'
      }
    });

  } catch (error: any) {
    logger.error('Identity verification error:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to submit identity verification'
    });
  }
};

// step 6
export const setTransactionPin = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const validation = await ValidationService.validateData(request.body, ValidationService.transactionPinSchema);

    if (!validation.isValid) {
      return reply.status(400).send({
        success: false,
        message: 'validation failed',
        errors: validation.errors
      });
    }

    const { pin, confirmPin, platform, phone } = validation.data;
    const ipAddress = request.ip;

    if (pin !== confirmPin) {
      return reply.status(400).send({
        success: false,
        message: 'pin and confirmPin do not match'
      });
    }

    if (pin.length !== 4 || !/^\d{4}$/.test(pin)) {
      return reply.status(400).send({
        success: false,
        message: 'pin must be exactly 4 digits'
      });
    }

    const user = await User.findOne({ phone });

    if (!user) {
      return reply.status(404).send({
        success: false,
        message: 'user not found'
      });
    }

    if (user.identity_verification.verification_status !== VerificationStatus.VERIFIED) {
      return reply.status(400).send({
        success: false,
        message: 'identity verification must be completed first'
      });
    }

    const hashedPin = await CryptoUtils.hashPassword(pin);
    user.transaction_pin = hashedPin;
    user.transaction_pin_set = true;

    user.registration_step = RegistrationStep.TRANSACTION_PIN;
    await user.save();

    logger.info('Transaction PIN set', {
      userId: user._id,
      platform,
      ipAddress
    });

    return reply.status(200).send({
      success: true,
      message: 'transaction PIN set successfully',
      data: {
        userId: user._id.toString(),
        pinSet: true,
        currentStep: user.registration_step,
        nextStep: RegistrationStep.BIOMETRIC_ENROLLMENT,
        registrationProgress: {
          currentStep: user.registration_step,
          phoneVerified: user.phone_verified,
          emailVerified: user.email_verified
        }
      }
    });

  } catch (error: any) {
    logger.error('Transaction PIN setup error:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to set transaction PIN'
    });
  }
};

// step 7 (optional)
export const setBiometricEnrollment = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const validation = await ValidationService.validateData(request.body, ValidationService.biometricEnrollmentSchema);

    if (!validation.isValid) {
      return reply.status(400).send({
        success: false,
        message: 'validation failed',
        errors: validation.errors
      });
    }

    const { biometricEnabled, platform, phone } = validation.data;
    const ipAddress = request.ip;

    const user = await User.findOne({ phone });

    if (!user) {
      return reply.status(404).send({
        success: false,
        message: 'user not found'
      });
    }

    if (!user.transaction_pin_set) {
      return reply.status(400).send({
        success: false,
        message: 'transaction PIN must be set first'
      });
    }

    user.biometric_enabled = biometricEnabled;
    if (biometricEnabled) {
      user.biometric_enrolled_at = new Date();
    }

    user.registration_step = RegistrationStep.COMPLETED;
    user.registration_completed = true;
    await user.save();

    const userPayload = {
      id: user._id.toString(),
      email: user.email,
      phone: user.phone,
      role: user.role,
      isVerified: user.is_verified,
      kycStatus: user.kyc_status
    };

    const accessToken = AuthMiddleware.generateToken(userPayload);
    const refreshToken = AuthMiddleware.generateRefreshToken(userPayload);

    // set cook
    reply.setCookie('token', accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 15 * 60 * 1000 // 15 minutes
    });

    logger.info('Registration completed', {
      userId: user._id,
      biometricEnabled: biometricEnabled,
      platform,
      ipAddress
    });

    return reply.status(200).send({
      success: true,
      message: 'registration completed successfully',
      data: {
        authSession: {
          accessToken,
          refreshToken,
          tokenType: "Bearer",
          expiresIn: "15m"
        },
        userInfo: {
          userId: user._id.toString(),
          phone: user.phone,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          biometricEnabled: user.biometric_enabled,
          registrationCompleted: true,
          currentStep: user.registration_step,
          accountStatus: user.account_status,
          kycStatus: user.kyc_status
        },
        registrationProgress: {
          currentStep: user.registration_step,
          phoneVerified: user.phone_verified,
          emailVerified: user.email_verified,
          registrationCompleted: user.registration_completed
        },
        welcomeMessage: `Welcome to AeTrust, ${user.first_name}!`
      }
    });

  } catch (error: any) {
    logger.error('Biometric enrollment error:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to complete biometric enrollment'
    });
  }
};

// resend verification codes
export const resendVerification = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const { type, identifier, platform } = request.body as { type: 'phone' | 'email'; identifier: string; platform: Platform };
    const ipAddress = request.ip;

    if (!type || !identifier || !platform) {
      return reply.status(400).send({
        success: false,
        message: 'type, identifier and platform are required'
      });
    }

    let result;
    if (type === 'phone') {
      result = await VerificationService.sendPhoneVerification(identifier, platform, ipAddress);
    } else {
      result = await VerificationService.sendEmailVerification(identifier, platform, ipAddress);
    }

    if (!result.success) {
      return reply.status(400).send({
        success: false,
        message: result.message,
        retryAfter: result.retryAfter
      });
    }

    return reply.status(200).send({
      success: true,
      message: `verification code resent to your ${type}`
    });

  } catch (error: any) {
    logger.error('Resend verification error:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to resend verification code'
    });
  }
};

// step 8 (for agents)
export const submitBusinessVerification = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    // For now, we'll do basic validation since the schema doesn't exist yet
    const {
      businessName,
      businessType,
      businessRegistrationNumber,
      businessAddress,
      businessDocument,
      taxCertificate,
      phone
    } = request.body as any;

    if (!businessName || !businessType || !phone) {
      return reply.status(400).send({
        success: false,
        message: 'businessName, businessType, and phone are required'
      });
    }
    const ipAddress = request.ip;

    const user = await User.findOne({ phone });

    if (!user) {
      return reply.status(404).send({
        success: false,
        message: 'user not found'
      });
    }

    if (!user.transaction_pin_set) {
      return reply.status(400).send({
        success: false,
        message: 'complete personal registration first'
      });
    }

    // Update agent info
    user.agent_info = {
      is_active: false,
      business_name: businessName,
      business_type: businessType,
      business_registration_number: businessRegistrationNumber,
      business_address: businessAddress,
      business_document: businessDocument || '',
      tax_certificate: taxCertificate || '',
      verification_status: VerificationStatus.PENDING,
      commission_rate: 0.02, // 2% default
      total_commission_earned: 0,
      monthly_commission: 0,
      performance_rating: 0,
      total_transactions: 0,
      successful_transactions: 0
    };

    user.role = 'agent' as any;
    await user.save();

    logger.info('Business verification submitted', {
      userId: user._id,
      businessName,
      businessType,
      ipAddress
    });

    return reply.status(200).send({
      success: true,
      message: 'business verification submitted successfully',
      data: {
        userId: user._id.toString(),
        businessName: user.agent_info?.business_name,
        businessType: user.agent_info?.business_type,
        verificationStatus: user.agent_info?.verification_status,
        role: user.role,
        estimatedProcessingTime: '3-5 business days',
        nextSteps: [
          'Document review by compliance team',
          'Background check verification',
          'Agent onboarding training',
          'Account activation'
        ]
      }
    });

  } catch (error: any) {
    logger.error('Business verification error:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to submit business verification'
    });
  }
};


export const getRegistrationProgress = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const { phone } = request.query as { phone: string };

    if (!phone) {
      return reply.status(400).send({
        success: false,
        message: 'phone number is required'
      });
    }

    const user = await User.findOne({ phone });

    if (!user) {
      return reply.status(404).send({
        success: false,
        message: 'user not found'
      });
    }

    const steps = [
      {
        step: 'phone_email',
        title: 'Phone & Email',
        completed: user.registration_step !== RegistrationStep.PHONE_EMAIL,
        current: user.registration_step === RegistrationStep.PHONE_EMAIL
      },
      {
        step: 'phone_verification',
        title: 'Phone Verification',
        completed: user.phone_verified,
        current: user.registration_step === RegistrationStep.PHONE_VERIFICATION
      },
      {
        step: 'email_verification',
        title: 'Email Verification',
        completed: user.email_verified,
        current: user.registration_step === RegistrationStep.EMAIL_VERIFICATION
      },
      {
        step: 'personal_info',
        title: 'Personal Information',
        completed: user.first_name && user.last_name && user.password,
        current: user.registration_step === RegistrationStep.PERSONAL_INFO
      },
      {
        step: 'identity_verification',
        title: 'Identity Verification',
        completed: user.identity_verification.verification_status === VerificationStatus.VERIFIED,
        current: user.registration_step === RegistrationStep.IDENTITY_VERIFICATION
      },
      {
        step: 'transaction_pin',
        title: 'Transaction PIN',
        completed: user.transaction_pin_set,
        current: user.registration_step === RegistrationStep.TRANSACTION_PIN
      },
      {
        step: 'biometric_enrollment',
        title: 'Biometric Setup',
        completed: user.registration_completed,
        current: user.registration_step === RegistrationStep.BIOMETRIC_ENROLLMENT
      }
    ];

    if (user.role === 'agent' || user.agent_info) {
      steps.push({
        step: 'business_verification',
        title: 'Business Verification',
        completed: user.agent_info?.verification_status === VerificationStatus.VERIFIED,
        current: user.agent_info?.verification_status === VerificationStatus.PENDING
      });
    }

    const completedSteps = steps.filter(step => step.completed).length;
    const totalSteps = steps.length;
    const progressPercentage = Math.round((completedSteps / totalSteps) * 100);

    return reply.status(200).send({
      success: true,
      message: 'registration progress retrieved',
      data: {
        userId: user._id.toString(),
        currentStep: user.registration_step,
        registrationCompleted: user.registration_completed,
        progressPercentage,
        completedSteps,
        totalSteps,
        steps,
        userType: user.role,
        canProceed: true,
        nextAction: user.registration_completed ? 'complete' : 'continue'
      }
    });

  } catch (error: any) {
    logger.error('Get registration progress error:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to get registration progress'
    });
  }
};

// get registration status
export const getRegistrationStatus = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const { phone } = request.query as { phone: string };

    if (!phone) {
      return reply.status(400).send({
        success: false,
        message: 'phone number is required'
      });
    }

    const user = await User.findOne({ phone });

    if (!user) {
      return reply.status(404).send({
        success: false,
        message: 'user not found'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'registration status retrieved',
      data: {
        userId: user._id.toString(),
        currentStep: user.registration_step,
        registrationCompleted: user.registration_completed,
        phoneVerified: user.phone_verified,
        emailVerified: user.email_verified,
        identityVerificationStatus: user.identity_verification.verification_status,
        transactionPinSet: user.transaction_pin_set,
        biometricEnabled: user.biometric_enabled,
        registrationProgress: {
          currentStep: user.registration_step,
          phoneVerified: user.phone_verified,
          emailVerified: user.email_verified,
          registrationCompleted: user.registration_completed
        }
      }
    });

  } catch (error: any) {
    logger.error('Get registration status error:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to get registration status'
    });
  }
};
