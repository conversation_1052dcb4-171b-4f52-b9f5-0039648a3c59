// import { FastifyInstance, FastifyPluginOptions } from 'fastify';
// import * as cardController from '../controllers/card.controller';
// import { AuthMiddleware } from '../middleware/auth.middleware';

export async function cardRoutes(fastify: any, _options: any) {
  // Temporarily commented out - will be implemented later
  // fastify.post('/create', { preHandler: AuthMiddleware.authenticateRequest }, cardController.createCard as any);
  // fastify.get('/my-cards', { preHandler: AuthMiddleware.authenticateRequest }, cardController.getUserCards as any);
  // fastify.get('/my-cards/enhanced', { preHandler: AuthMiddleware.authenticateRequest }, cardController.getUserCardsEnhanced as any);
  // fastify.get('/:cardId', { preHandler: AuthMiddleware.authenticateRequest }, cardController.getCardDetails as any);
  // fastify.get('/:cardId/display', { preHandler: AuthMiddleware.authenticateRequest }, cardController.getCardDisplayData as any);

  // fastify.put('/:cardId/limits', { preHandler: AuthMiddleware.authenticateRequest }, cardController.updateCardLimits as any);
  // fastify.put('/:cardId/settings', { preHandler: AuthMiddleware.authenticateRequest }, cardController.updateCardSettings as any);

  // fastify.post('/:cardId/toggle-balance', { preHandler: AuthMiddleware.authenticateRequest }, cardController.toggleBalanceVisibility as any);
  // fastify.post('/:cardId/copy-data', { preHandler: AuthMiddleware.authenticateRequest }, cardController.getCopyableCardData as any);
  // fastify.put('/:cardId/theme', { preHandler: AuthMiddleware.authenticateRequest }, cardController.updateCardTheme as any);

  // fastify.post('/export', { preHandler: AuthMiddleware.authenticateRequest }, cardController.exportUserCardData as any);
  // fastify.get('/export/download/:filename', { preHandler: AuthMiddleware.authenticateRequest }, cardController.downloadCardExport as any);
  // fastify.get('/summary', { preHandler: AuthMiddleware.authenticateRequest }, cardController.getUserCardSummary as any);

  // fastify.post('/:cardId/lock', { preHandler: AuthMiddleware.authenticateRequest }, cardController.lockCard as any);
  // fastify.post('/:cardId/unlock', { preHandler: AuthMiddleware.authenticateRequest }, cardController.unlockCard as any);
  // fastify.post('/:cardId/block', { preHandler: AuthMiddleware.authenticateRequest }, cardController.blockCard as any);
  // fastify.put('/:cardId/pin', { preHandler: AuthMiddleware.authenticateRequest }, cardController.changeCardPin as any);

  fastify.get('/health', async () => {
    return {
      success: true,
      message: 'card service is running',
      data: { status: 'ok' }
    };
  });
}
