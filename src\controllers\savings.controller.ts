import { FastifyReply } from 'fastify';
import { AuthenticatedRequest } from '../types';
import { SavingsService } from '../services/savings.service';
import { logger } from '../config/logger';

export const createSavingsAccount = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required',
      errorCode: 'AUTH_REQUIRED'
    });
  }

  try {
    const data = request.body as any;

    const savings = await SavingsService.createSavingsAccount({
      userId: request.user.id,
      ...data
    });

    return reply.status(201).send({
      success: true,
      message: 'savings account created successfully',
      data: {
        savingsAccount: {
          savingsId: savings._id.toString(),
          accountNumber: savings.account_number,
          accountName: savings.account_name,
          savingsType: savings.savings_type,
          currency: savings.currency,
          currentBalance: savings.current_balance,
          interestRate: `${(savings.interest_rate * 100).toFixed(2)}%`,
          accountStatus: savings.status,
          createdAt: savings.created_at
        },
        goalInfo: savings.goal_settings ? {
          goalName: savings.goal_settings.goal_name,
          goalDescription: savings.goal_settings.goal_description,
          targetAmount: savings.goal_settings.target_amount,
          targetDate: savings.goal_settings.target_date,
          progressPercentage: savings.goal_settings.progress_percentage
        } : null,
        autoSaveInfo: savings.auto_save_settings?.enabled ? {
          autoSaveEnabled: true,
          autoSaveAmount: savings.auto_save_settings.amount,
          frequency: savings.auto_save_settings.frequency,
          nextDeductionDate: savings.auto_save_settings.next_deduction_date
        } : null,
        projectedEarnings: {
          annualInterest: (savings as any).projected_annual_interest,
          maturityDate: savings.maturity_date
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        timestamp: new Date().toISOString(),
        serviceType: "savings_account_creation"
      }
    });
  } catch (error: any) {
    logger.error('savings account creation error:', error);
    
    const errorMessages: Record<string, string> = {
      'user not found': 'user account not found'
    };

    const message = errorMessages[error.message] || 'savings account creation failed';
    
    return reply.status(400).send({
      success: false,
      message,
      errorCode: 'SAVINGS_CREATION_FAILED',
      errorDetails: {
        reason: error.message,
        timestamp: new Date().toISOString()
      }
    });
  }
};

export const depositToSavings = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { savingsId } = request.params as any;
    const { amount, description } = request.body as any;

    const result = await SavingsService.deposit(savingsId, amount, description);

    return reply.status(200).send({
      success: true,
      message: 'deposit to savings completed successfully',
      data: {
        depositDetails: {
          savingsId,
          depositAmount: amount,
          newBalance: result.newBalance,
          transactionId: result.transactionId,
          processedAt: new Date().toISOString()
        },
        balanceInfo: {
          currentBalance: result.newBalance,
          balanceChange: `+${amount}`
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        processingTimeMs: Date.now() - ((request as any).startTime || Date.now())
      }
    });
  } catch (error: any) {
    logger.error('savings deposit error:', error);
    
    const errorMessages: Record<string, string> = {
      'savings account not found': 'savings account not found',
      'savings account is not active': 'savings account is not active',
      'insufficient wallet balance': 'insufficient wallet balance'
    };

    const message = errorMessages[error.message] || 'deposit failed';
    
    return reply.status(400).send({
      success: false,
      message,
      errorCode: 'DEPOSIT_FAILED',
      errorDetails: {
        reason: error.message,
        timestamp: new Date().toISOString()
      }
    });
  }
};

export const withdrawFromSavings = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { savingsId } = request.params as any;
    const { amount, description } = request.body as any;

    const result = await SavingsService.withdraw(savingsId, amount, description);

    return reply.status(200).send({
      success: true,
      message: 'withdrawal from savings completed successfully',
      data: {
        withdrawalDetails: {
          savingsId,
          withdrawalAmount: amount,
          newBalance: result.newBalance,
          transactionId: result.transactionId,
          penalty: result.penalty,
          processedAt: new Date().toISOString()
        },
        balanceInfo: {
          currentBalance: result.newBalance,
          balanceChange: `-${amount}`,
          penaltyApplied: result.penalty || 0
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        processingTimeMs: Date.now() - ((request as any).startTime || Date.now())
      }
    });
  } catch (error: any) {
    logger.error('savings withdrawal error:', error);
    
    const errorMessages: Record<string, string> = {
      'savings account not found': 'savings account not found',
      'savings account is not active': 'savings account is not active',
      'insufficient balance': 'insufficient savings balance',
      'withdrawal would violate minimum balance requirement': 'withdrawal would violate minimum balance requirement'
    };

    const message = errorMessages[error.message] || 'withdrawal failed';
    
    return reply.status(400).send({
      success: false,
      message,
      errorCode: 'WITHDRAWAL_FAILED',
      errorDetails: {
        reason: error.message,
        timestamp: new Date().toISOString()
      }
    });
  }
};

export const getUserSavingsAccounts = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { savingsType } = request.query as any;

    const accounts = await SavingsService.getUserSavingsAccounts(request.user.id, savingsType);

    return reply.status(200).send({
      success: true,
      message: 'savings accounts retrieved successfully',
      data: {
        savingsAccounts: accounts.map(account => ({
          savingsId: account._id.toString(),
          accountNumber: account.account_number,
          accountName: account.account_name,
          savingsType: account.savings_type,
          currency: account.currency,
          currentBalance: account.current_balance,
          interestRate: `${(account.interest_rate * 100).toFixed(2)}%`,
          interestEarned: account.interest_earned,
          accountStatus: account.status,
          goalProgress: account.goal_settings ? {
            goalName: account.goal_settings.goal_name,
            targetAmount: account.goal_settings.target_amount,
            progressPercentage: account.goal_settings.progress_percentage,
            goalStatus: (account as any).goal_status
          } : null,
          autoSaveEnabled: account.auto_save_settings?.enabled || false,
          maturityDate: account.maturity_date,
          daysToMaturity: (account as any).days_to_maturity,
          createdAt: account.created_at
        })),
        summary: {
          totalAccounts: accounts.length,
          totalBalance: accounts.reduce((sum, acc) => sum + acc.current_balance, 0),
          totalInterestEarned: accounts.reduce((sum, acc) => sum + acc.interest_earned, 0),
          activeAccounts: accounts.filter(acc => acc.status === 'active').length
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        filterApplied: savingsType || 'all'
      }
    });
  } catch (error: any) {
    logger.error('get user savings accounts error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve savings accounts'
    });
  }
};

export const getSavingsAccountDetails = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { savingsId } = request.params as any;

    const savings = await SavingsService.getSavingsAccountById(savingsId);
    
    if (!savings) {
      return reply.status(404).send({
        success: false,
        message: 'savings account not found',
        errorCode: 'SAVINGS_NOT_FOUND'
      });
    }

    // check if savings belongs to user
    if (savings.user_id.toString() !== request.user.id) {
      return reply.status(403).send({
        success: false,
        message: 'access denied',
        errorCode: 'ACCESS_DENIED'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'savings account details retrieved successfully',
      data: {
        accountDetails: {
          savingsId: savings._id.toString(),
          accountNumber: savings.account_number,
          accountName: savings.account_name,
          savingsType: savings.savings_type,
          currency: savings.currency,
          currentBalance: savings.current_balance,
          interestRate: `${(savings.interest_rate * 100).toFixed(2)}%`,
          interestEarned: savings.interest_earned,
          accountStatus: savings.status,
          createdAt: savings.created_at
        },
        goalInformation: savings.goal_settings ? {
          goalName: savings.goal_settings.goal_name,
          goalDescription: savings.goal_settings.goal_description,
          targetAmount: savings.goal_settings.target_amount,
          targetDate: savings.goal_settings.target_date,
          progressPercentage: savings.goal_settings.progress_percentage,
          goalStatus: (savings as any).goal_status,
          remainingAmount: Math.max(0, savings.goal_settings.target_amount - savings.current_balance)
        } : null,
        autoSaveSettings: savings.auto_save_settings?.enabled ? {
          autoSaveEnabled: true,
          autoSaveAmount: savings.auto_save_settings.amount,
          frequency: savings.auto_save_settings.frequency,
          nextDeductionDate: savings.auto_save_settings.next_deduction_date
        } : null,
        withdrawalRestrictions: {
          minimumBalance: savings.withdrawal_restrictions.minimum_balance,
          dailyLimit: savings.withdrawal_restrictions.withdrawal_limit_per_day,
          monthlyLimit: savings.withdrawal_restrictions.withdrawal_limit_per_month,
          earlyWithdrawalPenalty: `${(savings.withdrawal_restrictions.early_withdrawal_penalty * 100).toFixed(2)}%`
        },
        projectedEarnings: {
          annualInterest: (savings as any).projected_annual_interest,
          maturityDate: savings.maturity_date,
          daysToMaturity: (savings as any).days_to_maturity
        },
        recentTransactions: savings.transaction_history.slice(-10).reverse().map(tx => ({
          transactionType: tx.transaction_type,
          amount: tx.amount,
          balanceBefore: tx.balance_before,
          balanceAfter: tx.balance_after,
          description: tx.description,
          transactionDate: tx.created_at
        }))
      }
    });
  } catch (error: any) {
    logger.error('get savings account details error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve savings account details'
    });
  }
};
