import { User } from '../models/user.model';
import { Transaction } from '../models/transaction.model';
import { Agent } from '../models/agent.model';
import { logger } from '../config/logger';
// import mongoose from 'mongoose';

interface SearchFilters {
  query?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
  type?: string;
  amount?: { min?: number; max?: number };
  currency?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export class SearchService {
  
  static async searchUsers(filters: SearchFilters): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      const {
        query = '',
        status,
        dateFrom,
        dateTo,
        page = 1,
        limit = 20,
        sortBy = 'created_at',
        sortOrder = 'desc'
      } = filters;

      const searchQuery: any = {};

      if (query) {
        searchQuery.$or = [
          { first_name: { $regex: query, $options: 'i' } },
          { last_name: { $regex: query, $options: 'i' } },
          { email: { $regex: query, $options: 'i' } },
          { phone: { $regex: query, $options: 'i' } },
          { username: { $regex: query, $options: 'i' } }
        ];
      }

      if (status) {
        if (status === 'verified') {
          searchQuery.is_verified = true;
        } else if (status === 'unverified') {
          searchQuery.is_verified = false;
        } else if (status === 'active') {
          searchQuery.account_status = 'active';
        } else if (status === 'suspended') {
          searchQuery.account_status = 'suspended';
        } else if (status === 'blocked') {
          searchQuery.account_status = 'blocked';
        }
      }

      if (dateFrom || dateTo) {
        searchQuery.created_at = {};
        if (dateFrom) {
          searchQuery.created_at.$gte = new Date(dateFrom);
        }
        if (dateTo) {
          searchQuery.created_at.$lte = new Date(dateTo);
        }
      }

      const sortObj: any = {};
      sortObj[sortBy] = sortOrder === 'asc' ? 1 : -1;

      const skip = (page - 1) * limit;
      
      const [users, totalCount] = await Promise.all([
        User.find(searchQuery)
          .select('-password -transaction_pin')
          .sort(sortObj)
          .skip(skip)
          .limit(limit)
          .lean(),
        User.countDocuments(searchQuery)
      ]);

      // format user data
      const formattedUsers = users.map(user => ({
        id: user._id.toString(),
        firstName: user.first_name,
        lastName: user.last_name,
        fullName: `${user.first_name} ${user.last_name}`,
        email: user.email,
        phone: user.phone,
        username: user.username,
        role: user.role,
        accountStatus: user.account_status,
        isVerified: user.is_verified,
        kycStatus: user.kyc_status,
        walletBalance: user.wallet_balance || 0,
        createdAt: user.created_at,
        lastLogin: user.last_login,
        profilePicture: user.profile_picture
      }));

      return {
        success: true,
        data: {
          users: formattedUsers,
          pagination: {
            currentPage: page,
            totalPages: Math.ceil(totalCount / limit),
            totalItems: totalCount,
            itemsPerPage: limit,
            hasNextPage: page < Math.ceil(totalCount / limit),
            hasPrevPage: page > 1
          },
          filters: {
            query,
            status,
            dateFrom,
            dateTo,
            sortBy,
            sortOrder
          }
        },
        message: 'users search completed successfully'
      };
    } catch (error: any) {
      logger.error('Error searching users:', error);
      return {
        success: false,
        message: 'failed to search users'
      };
    }
  }

  static async searchTransactions(filters: SearchFilters): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      const {
        query = '',
        status,
        type,
        dateFrom,
        dateTo,
        amount,
        currency,
        page = 1,
        limit = 20,
        sortBy = 'created_at',
        sortOrder = 'desc'
      } = filters;

      const searchQuery: any = {};

      if (query) {
        searchQuery.$or = [
          { description: { $regex: query, $options: 'i' } },
          { external_reference: { $regex: query, $options: 'i' } },
          { 'metadata.customerName': { $regex: query, $options: 'i' } },
          { 'metadata.customerEmail': { $regex: query, $options: 'i' } }
        ];
      }

      if (status) {
        searchQuery.status = status;
      }

      if (type) {
        searchQuery.type = type;
      }

      if (currency) {
        searchQuery.currency = currency;
      }

      if (amount) {
        searchQuery.amount = {};
        if (amount.min !== undefined) {
          searchQuery.amount.$gte = amount.min;
        }
        if (amount.max !== undefined) {
          searchQuery.amount.$lte = amount.max;
        }
      }

      if (dateFrom || dateTo) {
        searchQuery.created_at = {};
        if (dateFrom) {
          searchQuery.created_at.$gte = new Date(dateFrom);
        }
        if (dateTo) {
          searchQuery.created_at.$lte = new Date(dateTo);
        }
      }

      const sortObj: any = {};
      sortObj[sortBy] = sortOrder === 'asc' ? 1 : -1;

      const skip = (page - 1) * limit;
      
      const [transactions, totalCount] = await Promise.all([
        Transaction.find(searchQuery)
          .populate('user_id', 'first_name last_name email')
          .populate('recipient_id', 'first_name last_name email')
          .sort(sortObj)
          .skip(skip)
          .limit(limit)
          .lean(),
        Transaction.countDocuments(searchQuery)
      ]);

      const formattedTransactions = transactions.map(txn => ({
        id: txn._id.toString(),
        type: txn.type,
        amount: Math.round(txn.amount * 100) / 100,
        currency: txn.currency,
        status: txn.status,
        description: txn.description,
        reference: txn.external_reference || txn._id.toString(),
        fee: Math.round((txn.fee || 0) * 100) / 100,
        user: (txn.user_id as any) ? {
          id: (txn.user_id as any)._id.toString(),
          name: `${(txn.user_id as any).first_name} ${(txn.user_id as any).last_name}`,
          email: (txn.user_id as any).email
        } : null,
        recipient: (txn.recipient_id as any) ? {
          id: (txn.recipient_id as any)._id.toString(),
          name: `${(txn.recipient_id as any).first_name} ${(txn.recipient_id as any).last_name}`,
          email: (txn.recipient_id as any).email
        } : null,
        createdAt: txn.created_at,
        updatedAt: txn.updated_at,
        metadata: txn.metadata
      }));

      return {
        success: true,
        data: {
          transactions: formattedTransactions,
          pagination: {
            currentPage: page,
            totalPages: Math.ceil(totalCount / limit),
            totalItems: totalCount,
            itemsPerPage: limit,
            hasNextPage: page < Math.ceil(totalCount / limit),
            hasPrevPage: page > 1
          },
          filters: {
            query,
            status,
            type,
            dateFrom,
            dateTo,
            amount,
            currency,
            sortBy,
            sortOrder
          }
        },
        message: 'transactions search completed successfully'
      };
    } catch (error: any) {
      logger.error('Error searching transactions:', error);
      return {
        success: false,
        message: 'failed to search transactions'
      };
    }
  }

  static async searchAgents(filters: SearchFilters): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      const {
        query = '',
        status,
        dateFrom,
        dateTo,
        page = 1,
        limit = 20,
        sortBy = 'created_at',
        sortOrder = 'desc'
      } = filters;

      const searchQuery: any = {};

      if (query) {
        searchQuery.$or = [
          { business_name: { $regex: query, $options: 'i' } },
          { agent_code: { $regex: query, $options: 'i' } },
          { business_type: { $regex: query, $options: 'i' } },
          { 'contact_info.email': { $regex: query, $options: 'i' } },
          { 'contact_info.phone': { $regex: query, $options: 'i' } },
          { 'location.city': { $regex: query, $options: 'i' } },
          { 'location.state': { $regex: query, $options: 'i' } }
        ];
      }

      if (status) {
        if (status === 'active') {
          searchQuery.is_active = true;
        } else if (status === 'inactive') {
          searchQuery.is_active = false;
        } else {
          searchQuery.agent_status = status;
        }
      }

      if (dateFrom || dateTo) {
        searchQuery.created_at = {};
        if (dateFrom) {
          searchQuery.created_at.$gte = new Date(dateFrom);
        }
        if (dateTo) {
          searchQuery.created_at.$lte = new Date(dateTo);
        }
      }

      const sortObj: any = {};
      sortObj[sortBy] = sortOrder === 'asc' ? 1 : -1;

      const skip = (page - 1) * limit;
      
      const [agents, totalCount] = await Promise.all([
        Agent.find(searchQuery)
          .populate('user_id', 'first_name last_name email')
          .sort(sortObj)
          .skip(skip)
          .limit(limit)
          .lean(),
        Agent.countDocuments(searchQuery)
      ]);

      const formattedAgents = agents.map(agent => ({
        id: agent._id.toString(),
        agentCode: agent.agent_code,
        businessName: agent.business_name,
        businessType: agent.business_type,
        status: agent.agent_status,
        isActive: agent.is_active,
        kycStatus: agent.kyc_status,
        performanceRating: agent.performance_rating || 0,
        location: agent.location,
        contactInfo: agent.contact_info,
        walletInfo: {
          floatBalance: agent.wallet_info.float_balance,
          commissionBalance: agent.wallet_info.commission_balance,
          currency: agent.wallet_info.currency
        },
        statistics: {
          totalTransactions: agent.statistics.total_transactions,
          totalVolume: Math.round(agent.statistics.total_volume * 100) / 100,
          totalCommission: Math.round(agent.statistics.total_commission_earned * 100) / 100
        },
        user: (agent.user_id as any) ? {
          id: (agent.user_id as any)._id.toString(),
          name: `${(agent.user_id as any).first_name} ${(agent.user_id as any).last_name}`,
          email: (agent.user_id as any).email
        } : null,
        createdAt: agent.created_at,
        lastActive: agent.last_active_date
      }));

      return {
        success: true,
        data: {
          agents: formattedAgents,
          pagination: {
            currentPage: page,
            totalPages: Math.ceil(totalCount / limit),
            totalItems: totalCount,
            itemsPerPage: limit,
            hasNextPage: page < Math.ceil(totalCount / limit),
            hasPrevPage: page > 1
          },
          filters: {
            query,
            status,
            dateFrom,
            dateTo,
            sortBy,
            sortOrder
          }
        },
        message: 'agents search completed successfully'
      };
    } catch (error: any) {
      logger.error('Error searching agents:', error);
      return {
        success: false,
        message: 'failed to search agents'
      };
    }
  }
}
