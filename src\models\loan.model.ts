import mongoose, { Schema, Document } from 'mongoose';
import { LoanType, LoanStatus, RepaymentStatus, Currency } from '../types';

export interface ILoan extends Document {
  _id: mongoose.Types.ObjectId;
  user_id: mongoose.Types.ObjectId;
  loan_type: LoanType;
  loan_amount: number;
  currency: Currency;
  interest_rate: number;
  loan_term_months: number;
  monthly_payment: number;
  total_amount: number;
  outstanding_balance: number;
  purpose: string;
  status: LoanStatus;
  application_data: {
    monthly_income: number;
    employment_status: string;
    employer_name?: string;
    employment_duration_months?: number;
    other_income?: number;
    existing_loans?: number;
    credit_score?: number;
    collateral_type?: string;
    collateral_value?: number;
  };
  approval_data?: {
    approved_by: mongoose.Types.ObjectId;
    approved_at: Date;
    approval_notes?: string;
    credit_limit?: number;
  };
  disbursement_data?: {
    disbursed_at: Date;
    disbursed_amount: number;
    disbursement_method: string;
    wallet_id?: mongoose.Types.ObjectId;
    bank_details?: any;
  };
  repayment_schedule: {
    payment_number: number;
    due_date: Date;
    principal_amount: number;
    interest_amount: number;
    total_amount: number;
    status: RepaymentStatus;
    paid_date?: Date;
    paid_amount?: number;
    late_fee?: number;
  }[];
  current_balance: number;
  paid_amount: number;
  outstanding_amount: number;
  next_payment_date?: Date;
  days_overdue: number;
  late_fees: number;
  created_at: Date;
  updated_at: Date;
}

const loanSchema = new Schema<ILoan>({
  user_id: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    
  },
  loan_type: {
    type: String,
    enum: Object.values(LoanType),
    required: true,
    
  },
  loan_amount: {
    type: Number,
    required: true,
    min: 100,
    set: (v: number) => Math.round(v * 100) / 100
  },
  currency: {
    type: String,
    enum: Object.values(Currency),
    required: true
  },
  interest_rate: {
    type: Number,
    required: true,
    min: 0,
    max: 1
  },
  loan_term_months: {
    type: Number,
    required: true,
    min: 1,
    max: 60
  },
  monthly_payment: {
    type: Number,
    required: true,
    set: (v: number) => Math.round(v * 100) / 100
  },
  total_amount: {
    type: Number,
    required: true,
    set: (v: number) => Math.round(v * 100) / 100
  },
  outstanding_balance: {
    type: Number,
    required: true,
    default: function() { return this.loan_amount; },
    set: (v: number) => Math.round(v * 100) / 100
  },
  purpose: {
    type: String,
    required: true,
    maxlength: 500
  },
  status: {
    type: String,
    enum: Object.values(LoanStatus),
    default: LoanStatus.PENDING,
    
  },
  application_data: {
    monthly_income: { type: Number, required: true },
    employment_status: { type: String, required: true },
    employer_name: { type: String, default: '' },
    employment_duration_months: { type: Number, default: 0 },
    other_income: { type: Number, default: 0 },
    existing_loans: { type: Number, default: 0 },
    credit_score: { type: Number, min: 300, max: 850 },
    collateral_type: { type: String, default: '' },
    collateral_value: { type: Number, default: 0 }
  },
  approval_data: {
    approved_by: { type: Schema.Types.ObjectId, ref: 'User' },
    approved_at: { type: Date },
    approval_notes: { type: String, default: '' },
    credit_limit: { type: Number, default: 0 }
  },
  disbursement_data: {
    disbursed_at: { type: Date },
    disbursed_amount: { type: Number },
    disbursement_method: { type: String, default: 'wallet' },
    wallet_id: { type: Schema.Types.ObjectId, ref: 'Wallet' },
    bank_details: { type: Schema.Types.Mixed }
  },
  repayment_schedule: [{
    payment_number: { type: Number, required: true },
    due_date: { type: Date, required: true },
    principal_amount: { type: Number, required: true },
    interest_amount: { type: Number, required: true },
    total_amount: { type: Number, required: true },
    status: { 
      type: String, 
      enum: Object.values(RepaymentStatus),
      default: RepaymentStatus.PENDING 
    },
    paid_date: { type: Date },
    paid_amount: { type: Number, default: 0 },
    late_fee: { type: Number, default: 0 }
  }],
  current_balance: {
    type: Number,
    default: 0,
    set: (v: number) => Math.round(v * 100) / 100
  },
  paid_amount: {
    type: Number,
    default: 0,
    set: (v: number) => Math.round(v * 100) / 100
  },
  outstanding_amount: {
    type: Number,
    default: 0,
    set: (v: number) => Math.round(v * 100) / 100
  },
  next_payment_date: {
    type: Date,
    
  },
  days_overdue: {
    type: Number,
    default: 0,
    
  },
  late_fees: {
    type: Number,
    default: 0,
    set: (v: number) => Math.round(v * 100) / 100
  },
  created_at: {
    type: Date,
    default: Date.now,
    
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  toJSON: {
    transform: function(_doc: any, ret: any) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

loanSchema.index({ user_id: 1, status: 1 });
loanSchema.index({ status: 1, next_payment_date: 1 });
loanSchema.index({ days_overdue: 1, status: 1 });
loanSchema.index({ created_at: -1 });

loanSchema.pre('save', function(next) {
  this.updated_at = new Date();
  this.outstanding_amount = this.current_balance + this.late_fees;
  const nextPayment = this.repayment_schedule.find(p => p.status === RepaymentStatus.PENDING);
  if (nextPayment) {
    this.next_payment_date = nextPayment.due_date;
    
    const today = new Date();
    if (nextPayment.due_date < today) {
      this.days_overdue = Math.floor((today.getTime() - nextPayment.due_date.getTime()) / (1000 * 60 * 60 * 24));
    } else {
      this.days_overdue = 0;
    }
  }
  
  next();
});

loanSchema.virtual('repayment_progress').get(function() {
  if (this.total_amount === 0) return 0;
  return (this.paid_amount / this.total_amount) * 100;
});

loanSchema.virtual('payment_status').get(function() {
  if (this.days_overdue > 30) return 'severely_overdue';
  if (this.days_overdue > 7) return 'overdue';
  if (this.days_overdue > 0) return 'late';
  return 'current';
});

loanSchema.virtual('term_months').get(function() {
  return this.loan_term_months;
});

loanSchema.virtual('outstanding_balance').get(function() {
  return this.outstanding_amount;
});

loanSchema.virtual('approved_at').get(function() {
  return this.approval_data?.approved_at;
});

loanSchema.virtual('disbursed_at').get(function() {
  return this.disbursement_data?.disbursed_at;
});

loanSchema.methods.calculateMonthlyPayment = function(): number {
  const monthlyRate = this.interest_rate / 12;
  const numPayments = this.loan_term_months;
  
  if (monthlyRate === 0) {
    return this.loan_amount / numPayments;
  }
  
  const payment = this.loan_amount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                  (Math.pow(1 + monthlyRate, numPayments) - 1);
  
  return Math.round(payment * 100) / 100;
};

loanSchema.methods.generateRepaymentSchedule = function(): void {
  const schedule = [];
  const monthlyPayment = this.calculateMonthlyPayment();
  let remainingBalance = this.loan_amount;
  const monthlyRate = this.interest_rate / 12;
  
  for (let i = 1; i <= this.loan_term_months; i++) {
    const interestAmount = remainingBalance * monthlyRate;
    const principalAmount = monthlyPayment - interestAmount;
    
    const dueDate = new Date();
    dueDate.setMonth(dueDate.getMonth() + i);
    
    schedule.push({
      payment_number: i,
      due_date: dueDate,
      principal_amount: Math.round(principalAmount * 100) / 100,
      interest_amount: Math.round(interestAmount * 100) / 100,
      total_amount: monthlyPayment,
      status: RepaymentStatus.PENDING,
      paid_amount: 0,
      late_fee: 0
    });
    
    remainingBalance -= principalAmount;
  }
  
  this.repayment_schedule = schedule;
  this.monthly_payment = monthlyPayment;
  this.total_amount = monthlyPayment * this.loan_term_months;
  this.current_balance = this.loan_amount;
};

loanSchema.methods.makePayment = function(amount: number): { success: boolean; message: string } {
  if (amount <= 0) {
    return { success: false, message: 'payment amount must be positive' };
  }
  
  if (amount > this.outstanding_amount) {
    return { success: false, message: 'payment amount exceeds outstanding balance' };
  }
  
  const nextPayment = this.repayment_schedule.find((p: any) => p.status === RepaymentStatus.PENDING);
  if (!nextPayment) {
    return { success: false, message: 'no pending payments found' };
  }
  
  let remainingAmount = amount;
  
  if (this.late_fees > 0) {
    const lateFeePayment = Math.min(remainingAmount, this.late_fees);
    this.late_fees -= lateFeePayment;
    remainingAmount -= lateFeePayment;
  }
  
  if (remainingAmount > 0) {
    const paymentAmount = Math.min(remainingAmount, nextPayment.total_amount - nextPayment.paid_amount);
    nextPayment.paid_amount += paymentAmount;
    this.paid_amount += paymentAmount;
    this.current_balance -= paymentAmount;
    
    if (nextPayment.paid_amount >= nextPayment.total_amount) {
      nextPayment.status = RepaymentStatus.PAID;
      nextPayment.paid_date = new Date();
    } else {
      nextPayment.status = RepaymentStatus.PARTIAL;
    }
  }
  
  if (this.current_balance <= 0 && this.late_fees <= 0) {
    this.status = LoanStatus.COMPLETED;
  }
  
  return { success: true, message: 'payment processed successfully' };
};

loanSchema.statics.findUserLoans = function(userId: string, status?: LoanStatus) {
  const query: any = { user_id: userId };
  if (status) query.status = status;
  return this.find(query).sort({ created_at: -1 });
};

loanSchema.statics.findOverdueLoans = function() {
  return this.find({
    status: { $in: [LoanStatus.ACTIVE, LoanStatus.DISBURSED] },
    days_overdue: { $gt: 0 }
  }).sort({ days_overdue: -1 });
};

export const Loan = mongoose.model<ILoan>('Loan', loanSchema);
