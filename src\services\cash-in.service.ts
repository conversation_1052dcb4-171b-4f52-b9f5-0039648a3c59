import { Transaction } from '../models/transaction.model';
import { Wallet } from '../models/wallet.model';
import { User } from '../models/user.model';
import { Agent } from '../models/agent.model';
import { SystemConfig } from '../models/system-config.model';
import { TransactionType, TransactionStatus, Currency, PaymentMethod } from '../types';
import { CryptoUtils } from '../utils/crypto';
import { NotificationService } from './notification.service';
import { logger } from '../config/logger';
import mongoose from 'mongoose';

export interface CashInData {
  userId: string;
  agentId?: string;
  amount: number;
  currency: Currency;
  recipientPhone?: string;
  recipientName?: string;
  description?: string;
  pin: string;
  metadata?: any;
}

export interface CashInFeeCalculation {
  amount: number;
  agentCommission: number;
  agentFlatFees: number;
  totalFees: number;
  netAmount: number;
  breakdown: {
    baseAmount: number;
    commissionRate: number;
    flatFee: number;
    totalCost: number;
  };
}

export class CashInService {

  static async calculateCashInFees(amount: number, agentId?: string): Promise<CashInFeeCalculation> {
    try {
      let commissionRate = await SystemConfig.getConfig('cashInCommissionRate') || 0.03;
      let flatFee = await SystemConfig.getConfig('cashInFlatFee') || 2.00;

      // Get agent-specific rates
      if (agentId) {
        const agent = await Agent.findById(agentId);
        if (agent) {
          commissionRate = agent.commission_rate || commissionRate;
          flatFee = (agent as any).flat_fee || flatFee;
        }
      }

      const agentCommission = amount * commissionRate;
      const totalFees = agentCommission + flatFee;
      const netAmount = amount - totalFees;

      return {
        amount,
        agentCommission: Math.round(agentCommission * 100) / 100,
        agentFlatFees: Math.round(flatFee * 100) / 100,
        totalFees: Math.round(totalFees * 100) / 100,
        netAmount: Math.round(netAmount * 100) / 100,
        breakdown: {
          baseAmount: amount,
          commissionRate,
          flatFee,
          totalCost: Math.round(totalFees * 100) / 100
        }
      };
    } catch (error: any) {
      logger.error('Error calculating cash-in fees:', error);
      throw error;
    }
  }

 
  static async getCashInUIData(userId: string, amount?: number): Promise<{
    quickAmounts: number[];
    selectedAmount?: number;
    feeCalculation?: CashInFeeCalculation;
    recipientInfo: {
      canSelectRecipient: boolean;
      defaultToSelf: boolean;
    };
    paymentMethods: Array<{
      id: string;
      name: string;
      icon: string;
      available: boolean;
      description: string;
    }>;
    nearbyAgents: Array<{
      id: string;
      name: string;
      distance: string;
      rating: number;
      available: boolean;
      location: string;
    }>;
  }> {
    try {
      const quickAmountsConfig = await SystemConfig.getConfig('cashInQuickAmounts') || '50,100,200,500,1000';
      const quickAmounts = quickAmountsConfig.split(',').map((amt: string) => parseInt(amt.trim()));
      const feeCalculation = amount ? await this.calculateCashInFees(amount) : undefined;
      const nearbyAgents = await this.getNearbyAgents(userId);

      const result: any = {
        quickAmounts,
        recipientInfo: {
          canSelectRecipient: true,
          defaultToSelf: true
        },
        paymentMethods: [
          {
            id: 'cash',
            name: 'Cash Payment',
            icon: '💵',
            available: true,
            description: 'Pay with cash at agent location'
          },
          {
            id: 'bank_transfer',
            name: 'Bank Transfer',
            icon: '🏦',
            available: true,
            description: 'Transfer from your bank account'
          },
          {
            id: 'mobile_money',
            name: 'Mobile Money',
            icon: '📱',
            available: true,
            description: 'Pay with mobile money'
          }
        ],
        nearbyAgents
      };

      if (amount !== undefined) {
        result.selectedAmount = amount;
      }

      if (feeCalculation !== undefined) {
        result.feeCalculation = feeCalculation;
      }

      return result;
    } catch (error: any) {
      logger.error('Error getting cash-in UI data:', error);
      throw error;
    }
  }


  static async initiateCashIn(data: CashInData): Promise<{
    success: boolean;
    transactionRef: string;
    amount: number;
    fees: CashInFeeCalculation;
    status: string;
    estimatedCompletion: string;
    agentInfo?: any;
    instructions: string[];
  }> {
    const session = await mongoose.startSession();
    
    try {
      await session.startTransaction();

      const user = await User.findById(data.userId).session(session);
      if (!user) {
        throw new Error('User not found');
      }

      const wallet = await Wallet.findOne({
        user_id: data.userId,
        currency: data.currency,
        wallet_type: 'main'
      }).session(session);

      if (!wallet) {
        throw new Error('Wallet not found');
      }

      const fees = await this.calculateCashInFees(data.amount, data.agentId);

      let agentInfo = null;
      if (data.agentId) {
        const agent = await Agent.findById(data.agentId).session(session);
        if (agent) {
          agentInfo = {
            id: agent._id.toString(),
            businessName: agent.business_name,
            location: agent.location?.address,
            phone: agent.contact_info?.phone,
            operatingHours: agent.operating_hours || '24/7'
          };
        }
      }

      const transactionRef = CryptoUtils.generateTransactionRef('CASHIN');
      const transaction = new Transaction({
        transaction_ref: transactionRef,
        user_id: data.userId,
        wallet_id: wallet._id,
        type: TransactionType.CASH_IN,
        status: TransactionStatus.PENDING,
        amount: data.amount,
        fee: fees.totalFees,
        currency: data.currency,
        description: data.description || 'Cash-in transaction',
        payment_method: PaymentMethod.CASH,
        balance_before: wallet.balance,
        balance_after: wallet.balance, // Will be updated when completed
        metadata: {
          ...data.metadata,
          cashInType: 'agent_assisted',
          agentId: data.agentId,
          recipientPhone: data.recipientPhone,
          recipientName: data.recipientName,
          feeBreakdown: fees,
          instructions: [
            'Visit the selected agent location',
            'Present your transaction reference',
            'Pay the cash amount to the agent',
            'Confirm transaction completion'
          ]
        }
      });

      await transaction.save({ session });

      await NotificationService.createNotification({
        userId: data.userId,
        type: 'in_app' as any,
        title: 'Cash-In Initiated',
        message: `Cash-in of ${data.currency} ${data.amount} has been initiated. Transaction ref: ${transactionRef}`,
        category: 'transaction',
        priority: 'medium',
        data: {
          transactionRef,
          amount: data.amount,
          currency: data.currency,
          type: 'cash_in'
        }
      });

      await session.commitTransaction();

      logger.info('Cash-in transaction initiated', {
        userId: data.userId,
        transactionRef,
        amount: data.amount,
        currency: data.currency,
        agentId: data.agentId
      });

      return {
        success: true,
        transactionRef,
        amount: data.amount,
        fees,
        status: 'pending',
        estimatedCompletion: '15-30 minutes',
        agentInfo,
        instructions: [
          'Visit the selected agent location',
          'Present your transaction reference',
          'Pay the cash amount to the agent',
          'Confirm transaction completion'
        ]
      };

    } catch (error: any) {
      await session.abortTransaction();
      logger.error('Cash-in initiation error:', error);
      throw error;
    } finally {
      await session.endSession();
    }
  }


  static async completeCashIn(transactionRef: string, agentId: string): Promise<{
    success: boolean;
    message: string;
    newBalance: number;
  }> {
    const session = await mongoose.startSession();
    
    try {
      await session.startTransaction();

      const transaction = await Transaction.findOne({
        transaction_ref: transactionRef,
        type: TransactionType.CASH_IN,
        status: TransactionStatus.PENDING
      }).session(session);

      if (!transaction) {
        throw new Error('Transaction not found or already completed');
      }

      const wallet = await Wallet.findById(transaction.wallet_id).session(session);
      if (!wallet) {
        throw new Error('Wallet not found');
      }

      const newBalance = wallet.balance + transaction.amount;
      wallet.balance = newBalance;
      wallet.available_balance = newBalance;
      wallet.metadata.total_credited += transaction.amount;
      wallet.metadata.total_transactions += 1;
      wallet.metadata.last_transaction_date = new Date();

      await wallet.save({ session });

      transaction.status = TransactionStatus.COMPLETED;
      transaction.balance_after = newBalance;
      transaction.processing.completed_at = new Date();

      await transaction.save({ session });

      await NotificationService.sendTransactionNotification({
        userId: transaction.user_id.toString(),
        transactionType: 'Cash-In',
        amount: transaction.amount,
        currency: transaction.currency,
        transactionRef: transaction.transaction_ref,
        balance: newBalance
      });

      await session.commitTransaction();

      logger.info('Cash-in transaction completed', {
        transactionRef,
        agentId,
        amount: transaction.amount,
        newBalance
      });

      return {
        success: true,
        message: 'Cash-in completed successfully',
        newBalance
      };

    } catch (error: any) {
      await session.abortTransaction();
      logger.error('Cash-in completion error:', error);
      throw error;
    } finally {
      await session.endSession();
    }
  }


  static async getCashInStatus(transactionRef: string, userId: string): Promise<{
    status: string;
    amount: number;
    currency: string;
    createdAt: Date;
    completedAt?: Date;
    agentInfo?: any;
    instructions: string[];
  }> {
    try {
      const transaction = await Transaction.findOne({
        transaction_ref: transactionRef,
        user_id: userId,
        type: TransactionType.CASH_IN
      });

      if (!transaction) {
        throw new Error('Transaction not found');
      }

      return {
        status: transaction.status,
        amount: transaction.amount,
        currency: transaction.currency,
        createdAt: transaction.created_at,
        ...(transaction.processing.completed_at && { completedAt: transaction.processing.completed_at }),
        agentInfo: (transaction.metadata as any)?.agentInfo,
        instructions: (transaction.metadata as any)?.instructions || []
      };
    } catch (error: any) {
      logger.error('Error getting cash-in status:', error);
      throw error;
    }
  }


  private static async getNearbyAgents(userId: string): Promise<Array<{
    id: string;
    name: string;
    distance: string;
    rating: number;
    available: boolean;
    location: string;
  }>> {
    try {
      const user = await User.findById(userId).select('address');
      const userLat = (user as any)?.location?.coordinates?.latitude || 0;
      const userLon = (user as any)?.location?.coordinates?.longitude || 0;

      // Find agents within 10km radius
      const agents = await Agent.find({
        is_active: true,
        'location.coordinates': { $exists: true }
      })
      .select('business_name location contact_in operating_hours')
      .limit(10);

      return agents.map(agent => {
        const agentLat = agent.location?.coordinates?.latitude || 0;
        const agentLon = agent.location?.coordinates?.longitude || 0;
        const distance = this.calculateDistance(userLat, userLon, agentLat, agentLon);

        return {
          id: agent._id.toString(),
          name: agent.business_name || 'Agent',
          distance: `${distance.toFixed(1)} km`,
          rating: agent.performance_rating || 4.5,
          available: this.isAgentAvailable(agent.operating_hours),
          location: agent.location?.address || 'Location not specified'
        };
      }).sort((a, b) => parseFloat(a.distance) - parseFloat(b.distance));
    } catch (error: any) {
      logger.error('Error getting nearby agents:', error);
      return [];
    }
  }


  private static calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // Earth's radius in km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }


  private static isAgentAvailable(operatingHours?: any): boolean {
    if (!operatingHours) return true;

    const now = new Date();
    const currentHour = now.getHours();

    if (operatingHours === '24/7' || !operatingHours.start || !operatingHours.end) {
      return true;
    }

    return currentHour >= operatingHours.start && currentHour <= operatingHours.end;
  }
}
