// import { FastifyInstance, FastifyPluginOptions } from 'fastify';
// import { AuthMiddleware } from '../middleware/auth.middleware';
// import * as savingsController from '../controllers/savings.controller';

export async function savingsRoutes(fastify: any, _options: any) {
  // fastify.post('/create', { preHandler: AuthMiddleware.authenticateRequest }, savingsController.createSavingsAccount as any);
  // fastify.get('/my-accounts', { preHandler: AuthMiddleware.authenticateRequest }, savingsController.getUserSavingsAccounts as any);
  // fastify.get('/:savingsId', { preHandler: AuthMiddleware.authenticateRequest }, savingsController.getSavingsAccountDetails as any);
  // Temporarily commented out - will be implemented later
  // fastify.post('/:savingsId/deposit', { preHandler: AuthMiddleware.authenticateRequest }, savingsController.depositToSavings as any);
  // fastify.post('/:savingsId/withdraw', { preHandler: AuthMiddleware.authenticateRequest }, savingsController.withdrawFromSavings as any);
  // Temporarily commented out - will be implemented later
  fastify.get('/health', async () => {
    return {
      success: true,
      message: 'savings service running',
      data: { 
        status: 'ok',
        services: ['account_creation', 'deposits', 'withdrawals', 'goal_tracking', 'auto_save']
      }
    };
  });
}
  // Temporarily commented out - will be implemented later