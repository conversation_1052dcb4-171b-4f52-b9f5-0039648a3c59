import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

const errorRate = new Rate('errors');

export const options = {
  stages: [
    { duration: '2m', target: 10 }, // Ramp up to 10 users
    { duration: '5m', target: 10 }, // Stay at 10 users
    { duration: '2m', target: 20 }, // Ramp up to 20 users
    { duration: '5m', target: 20 }, // Stay at 20 users
    { duration: '2m', target: 0 },  // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests should be below 500ms
    http_req_failed: ['rate<0.1'],    // Error rate should be less than 10%
    errors: ['rate<0.1'],             // Custom error rate should be less than 10%
  },
};

const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'LoadTest123!@#';

function randomString(length) {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

function randomEmail() {
  return `test${randomString(8)}@example.com`;
}

function randomPhone() {
  return `+234${Math.floor(Math.random() * 9000000000) + 1000000000}`;
}

export function setup() {
  const registrationPayload = {
    email: TEST_EMAIL,
    phone: '+2348123456789',
    password: TEST_PASSWORD,
    confirmPassword: TEST_PASSWORD,
    acceptTerms: true
  };

  const registrationResponse = http.post(`${BASE_URL}/registration/initiate`, JSON.stringify(registrationPayload), {
    headers: { 'Content-Type': 'application/json' },
  });

  if (registrationResponse.status === 200) {
    const regData = JSON.parse(registrationResponse.body);
    
    http.post(`${BASE_URL}/registration/complete`, JSON.stringify({
      userId: regData.data.userId
    }), {
      headers: { 'Content-Type': 'application/json' },
    });
  }

  return { testEmail: TEST_EMAIL, testPassword: TEST_PASSWORD };
}

export default function(data) {
  const testScenario = Math.random();

  if (testScenario < 0.3) {
    // 30% - Test user registration
    testUserRegistration();
  } else if (testScenario < 0.6) {
    // 30% - Test user login
    testUserLogin(data);
  } else if (testScenario < 0.8) {
    // 20% - Test authenticated user operations
    testAuthenticatedOperations(data);
  } else {
    // 20% - Test public endpoints
    testPublicEndpoints();
  }

  sleep(1); // Wait 1 second between iterations
}

function testUserRegistration() {
  const payload = {
    email: randomEmail(),
    phone: randomPhone(),
    password: 'Test123!@#',
    confirmPassword: 'Test123!@#',
    acceptTerms: true
  };

  const response = http.post(`${BASE_URL}/registration/initiate`, JSON.stringify(payload), {
    headers: { 'Content-Type': 'application/json' },
  });

  const success = check(response, {
    'registration status is 200': (r) => r.status === 200,
    'registration response time < 500ms': (r) => r.timings.duration < 500,
    'registration returns success': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.success === true;
      } catch {
        return false;
      }
    },
  });

  errorRate.add(!success);
}

function testUserLogin(data) {
  const payload = {
    email: data.testEmail,
    password: data.testPassword
  };

  const response = http.post(`${BASE_URL}/auth/login`, JSON.stringify(payload), {
    headers: { 'Content-Type': 'application/json' },
  });

  const success = check(response, {
    'login status is 200': (r) => r.status === 200,
    'login response time < 300ms': (r) => r.timings.duration < 300,
    'login returns token': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.success === true && body.data && body.data.token;
      } catch {
        return false;
      }
    },
  });

  errorRate.add(!success);

  // If login successful, test token refresh
  if (response.status === 200) {
    try {
      const loginData = JSON.parse(response.body);
      const token = loginData.data.token;

      const refreshResponse = http.post(`${BASE_URL}/auth/refresh`, null, {
        headers: { 
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json' 
        },
      });

      check(refreshResponse, {
        'token refresh status is 200': (r) => r.status === 200,
        'token refresh response time < 200ms': (r) => r.timings.duration < 200,
      });
    } catch (e) {
      errorRate.add(true);
    }
  }
}

function testAuthenticatedOperations(data) {
  const loginPayload = {
    email: data.testEmail,
    password: data.testPassword
  };

  const loginResponse = http.post(`${BASE_URL}/auth/login`, JSON.stringify(loginPayload), {
    headers: { 'Content-Type': 'application/json' },
  });

  if (loginResponse.status !== 200) {
    errorRate.add(true);
    return;
  }

  let token;
  try {
    const loginData = JSON.parse(loginResponse.body);
    token = loginData.data.token;
  } catch {
    errorRate.add(true);
    return;
  }

  const authHeaders = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };

  const profileResponse = http.get(`${BASE_URL}/user/me`, { headers: authHeaders });
  
  const profileSuccess = check(profileResponse, {
    'profile status is 200': (r) => r.status === 200,
    'profile response time < 300ms': (r) => r.timings.duration < 300,
    'profile returns user data': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.success === true && body.data && body.data.email;
      } catch {
        return false;
      }
    },
  });

  errorRate.add(!profileSuccess);

  const searchResponse = http.get(`${BASE_URL}/user/search?q=test`, { headers: authHeaders });
  
  const searchSuccess = check(searchResponse, {
    'search status is 200': (r) => r.status === 200,
    'search response time < 400ms': (r) => r.timings.duration < 400,
  });

  errorRate.add(!searchSuccess);

  const updatePayload = {
    bio: `Updated bio ${randomString(10)}`
  };

  const updateResponse = http.put(`${BASE_URL}/user/profile`, JSON.stringify(updatePayload), { headers: authHeaders });
  
  const updateSuccess = check(updateResponse, {
    'profile update status is 200': (r) => r.status === 200,
    'profile update response time < 400ms': (r) => r.timings.duration < 400,
  });

  errorRate.add(!updateSuccess);
}

function testPublicEndpoints() {

  const healthResponse = http.get(`${BASE_URL}/health`);
  
  const healthSuccess = check(healthResponse, {
    'health status is 200': (r) => r.status === 200,
    'health response time < 100ms': (r) => r.timings.duration < 100,
    'health returns ok status': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.status === 'ok';
      } catch {
        return false;
      }
    },
  });

  errorRate.add(!healthSuccess);

  const userHealthResponse = http.get(`${BASE_URL}/user/health`);
  
  const userHealthSuccess = check(userHealthResponse, {
    'user health status is 200': (r) => r.status === 200,
    'user health response time < 100ms': (r) => r.timings.duration < 100,
  });

  errorRate.add(!userHealthSuccess);

  const authHealthResponse = http.get(`${BASE_URL}/auth/health`);
  
  const authHealthSuccess = check(authHealthResponse, {
    'auth health status is 200': (r) => r.status === 200,
    'auth health response time < 100ms': (r) => r.timings.duration < 100,
  });

  errorRate.add(!authHealthSuccess);
}

export function teardown(_data) {
  console.log('Load test completed');
}
