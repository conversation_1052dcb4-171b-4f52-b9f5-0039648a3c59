import axios, { AxiosInstance } from 'axios';
import { SystemConfig } from '../models/system-config.model';
import { circuitBreakerRegistry } from '../utils/circuit-breaker';
import { withExternalApiRetry } from '../utils/retry';
import { monitoringService } from './monitoring.service';
import { logger } from '../config/logger';

export class ExternalApiService {
  private static httpClient: AxiosInstance;

  static {
    this.httpClient = axios.create({
      timeout: 30000,
      headers: {
        'User-Agent': 'AeTrust',
        'Content-Type': 'application/json'
      }
    });

    // request interceptor
    this.httpClient.interceptors.request.use(
      (config) => {
        (config as any).metadata = { startTime: Date.now() };
        return config;
      },
      (error) => Promise.reject(error)
    );

    // response interceptor
    this.httpClient.interceptors.response.use(
      (response) => {
        const duration = Date.now() - (response.config as any).metadata?.startTime;
        logger.info('external api call', {
          url: response.config.url,
          method: response.config.method,
          status: response.status,
          duration
        });
        return response;
      },
      (error) => {
        const duration = Date.now() - (error.config as any)?.metadata?.startTime;
        logger.error('external api error', {
          url: error.config?.url,
          method: error.config?.method,
          status: error.response?.status,
          duration,
          error: error.message
        });
        return Promise.reject(error);
      }
    );
  }

  static async getExchangeRates(from?: string, to?: string): Promise<any> {
    const circuitBreaker = circuitBreakerRegistry.getOrCreate('exchangeApi', {
      failureThreshold: 3,
      resetTimeout: 30000,
      monitoringPeriod: 10000
    });

    try {
      return await circuitBreaker.execute(async () => {
        return await withExternalApiRetry(async () => {
          const startTime = Date.now();

          try {
            const exchangeApiUrl = await SystemConfig.getConfig('exchangeApiUrl');
            const exchangeApiKey = await SystemConfig.getConfig('exchangeApiKey');

            if (!exchangeApiUrl || !exchangeApiKey) {
              // fallback to default rates if no external API configured
              return this.getDefaultExchangeRates(from, to);
            }

            const params: any = { accessKey: exchangeApiKey };
            if (from) params.from = from;
            if (to) params.to = to;

            const response = await this.httpClient.get(exchangeApiUrl, {
              params,
              timeout: 10000
            });

            const duration = Date.now() - startTime;
            monitoringService.recordExternalApiCall('exchangeApi', exchangeApiUrl, duration, true);

            if (response.data && response.data.success) {
              return {
                rates: response.data.quotes || response.data.rates,
                source: 'externalApi',
                timestamp: new Date().toISOString()
              };
            }

            throw new Error('invalid response from exchange api');
          } catch (error: any) {
            const duration = Date.now() - startTime;
            monitoringService.recordExternalApiCall('exchangeApi', 'exchangeApiUrl', duration, false);
            throw error;
          }
        }, 'exchangeRatesApi');
      });
    } catch (error: any) {
      logger.error('exchange rate api error:', error);
      // fallback to default rates
      return this.getDefaultExchangeRates(from, to);
    }
  }

  private static getDefaultExchangeRates(from?: string, to?: string): any {
    const defaultRates = {
      USD: {
        RWF: 1300.50,
        KES: 150.25,
        EUR: 0.85,
        UGX: 3700.00,
        TZS: 2300.00
      },
      EUR: {
        USD: 1.18,
        RWF: 1534.59,
        KES: 177.30,
        UGX: 4366.00,
        TZS: 2714.00
      },
      RWF: {
        USD: 0.00077,
        EUR: 0.00065,
        KES: 0.115,
        UGX: 2.84,
        TZS: 1.77
      }
    };

    if (from && to) {
      const rate = defaultRates[from as keyof typeof defaultRates]?.[to as keyof typeof defaultRates[keyof typeof defaultRates]];
      return {
        rates: { [from]: { [to]: rate || 1 } },
        source: 'defaultRates',
        timestamp: new Date().toISOString()
      };
    }

    return {
      rates: defaultRates,
      source: 'defaultRates',
      timestamp: new Date().toISOString()
    };
  }

  static async getBillProviders(country?: string, billType?: string): Promise<any> {
    try {
      const billApiUrl = await SystemConfig.getConfig('billProvidersApiUrl');
      const billApiKey = await SystemConfig.getConfig('billProvidersApiKey');

      if (!billApiUrl || !billApiKey) {
        return this.getDefaultBillProviders(country, billType);
      }

      const params: any = { apiKey: billApiKey };
      if (country) params.country = country;
      if (billType) params.type = billType;

      const response = await this.httpClient.get(billApiUrl, {
        params,
        timeout: 15000
      });

      if (response.data && response.data.providers) {
        return {
          providers: response.data.providers,
          source: 'externalApi'
        };
      }

      throw new Error('invalid response from bill providers api');
    } catch (error: any) {
      logger.error('bill providers api error:', error);
      return this.getDefaultBillProviders(country, billType);
    }
  }

  private static getDefaultBillProviders(country?: string, billType?: string): any {
    const allProviders = [
      {
        id: "eucl-001",
        name: "EUCL",
        type: "electricity",
        country: "RW",
        logo: "https://cdn.aetrust.com/providers/eucl.png",
        supportedCurrencies: ["RWF", "USD"],
        isActive: true
      },
      {
        id: "airtel-rw",
        name: "Airtel Rwanda",
        type: "mobile",
        country: "RW",
        logo: "https://cdn.aetrust.com/providers/airtel-rw.png",
        supportedCurrencies: ["RWF"],
        isActive: true
      },
      {
        id: "mtn-rw",
        name: "MTN Rwanda",
        type: "mobile",
        country: "RW",
        logo: "https://cdn.aetrust.com/providers/mtn-rw.png",
        supportedCurrencies: ["RWF"],
        isActive: true
      },
      {
        id: "safaricom-ke",
        name: "Safaricom",
        type: "mobile",
        country: "KE",
        logo: "https://cdn.aetrust.com/providers/safaricom.png",
        supportedCurrencies: ["KES"],
        isActive: true
      },
      {
        id: "kplc-ke",
        name: "Kenya Power",
        type: "electricity",
        country: "KE",
        logo: "https://cdn.aetrust.com/providers/kplc.png",
        supportedCurrencies: ["KES", "USD"],
        isActive: true
      }
    ];

    const filteredProviders = allProviders.filter(p => {
      if (!p.isActive) return false;
      if (country && p.country !== country) return false;
      if (billType && p.type !== billType) return false;
      return true;
    });

    return {
      providers: filteredProviders,
      source: 'defaultProviders'
    };
  }

  static async processPayment(provider: string, data: any): Promise<any> {
    try {
      const paymentApiUrl = await SystemConfig.getConfig(`${provider}PaymentApiUrl`);
      const paymentApiKey = await SystemConfig.getConfig(`${provider}PaymentApiKey`);

      if (!paymentApiUrl || !paymentApiKey) {
        throw new Error(`payment api not configured for provider: ${provider}`);
      }

      const response = await this.httpClient.post(paymentApiUrl, {
        ...data,
        apiKey: paymentApiKey
      }, {
        timeout: 30000,
        headers: {
          'Authorization': `Bearer ${paymentApiKey}`
        }
      });

      if (response.data && response.data.success) {
        return {
          success: true,
          transactionId: response.data.transaction_id,
          reference: response.data.reference,
          status: response.data.status,
          providerResponse: response.data
        };
      }

      throw new Error('payment processing failed');
    } catch (error: any) {
      logger.error('payment processing error:', error);
      throw new Error(`payment failed: ${error.message}`);
    }
  }

  static async validateAccount(provider: string, accountNumber: string): Promise<any> {
    try {
      const validationApiUrl = await SystemConfig.getConfig(`${provider}ValidationApiUrl`);
      const validationApiKey = await SystemConfig.getConfig(`${provider}ValidationApiKey`);

      if (!validationApiUrl || !validationApiKey) {
        throw new Error('Account validation service not configured');
      }

      const response = await this.httpClient.post(validationApiUrl, {
        accountNumber: accountNumber,
        apiKey: validationApiKey
      }, {
        timeout: 10000
      });

      return {
        valid: response.data.valid || false,
        accountName: response.data.account_name,
        accountType: response.data.account_type,
        balance: response.data.balance
      };
    } catch (error: any) {
      logger.error('account validation error:', error);
      return {
        valid: false,
        error: 'validation failed'
      };
    }
  }

  static async getCreditScore(userId: string, data: any): Promise<any> {
    try {
      const creditApiUrl = await SystemConfig.getConfig('creditScoringApiUrl');
      const creditApiKey = await SystemConfig.getConfig('creditScoringApiKey');

      if (!creditApiUrl || !creditApiKey) {
        throw new Error('Credit scoring service not configured');
      }

      const response = await this.httpClient.post(creditApiUrl, {
        userId: userId,
        ...data,
        apiKey: creditApiKey
      }, {
        timeout: 20000
      });

      return {
        score: response.data.score,
        grade: response.data.grade,
        factors: response.data.factors,
        recommendations: response.data.recommendations
      };
    } catch (error: any) {
      logger.error('credit scoring error:', error);
      // return default score
      return {
        score: 650,
        grade: 'C',
        factors: ['insufficientData']
      };
    }
  }
}
