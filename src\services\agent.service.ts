import { Agent, IAgent } from '../models/agent.model';
import { User } from '../models/user.model';
import { WalletService } from './wallet.service';
import { UserService } from './user.service';
import { AgentStatus, KycStatus, Currency, WalletType, TransactionType, UserRole } from '../types';
import { logger } from '../config/logger';
import mongoose from 'mongoose';

export class AgentService {
  static async registerAgent(data: {
    userId: string;
    businessName: string;
    businessType: string;
    businessRegistrationNumber?: string;
    taxId?: string;
    location: {
      address: string;
      city: string;
      state: string;
      country: string;
      coordinates?: { latitude: number; longitude: number };
    };
    contactInfo: {
      phone: string;
      email: string;
      whatsapp?: string;
    };
  }): Promise<IAgent> {
    try {

      const existingAgent = await Agent.findOne({ user_id: data.userId });
      if (existingAgent) {
        throw new Error('user already has an agent account');
      }

      const user = await UserService.getUserById(data.userId);
      if (!user) {
        throw new Error('user not found');
      }

      const agentCode = await this.generateAgentCode(data.location.city);

      const mainWallet = await WalletService.createWallet({
        userId: data.userId,
        currency: Currency.USD, 
        walletType: WalletType.AGENT,
        isDefault: true
      });

      const commissionWallet = await WalletService.createWallet({
        userId: data.userId,
        currency: Currency.USD,
        walletType: WalletType.COMMISSION,
        isDefault: false
      });


      const agent = new Agent({
        user_id: data.userId,
        agent_code: agentCode,
        business_name: data.businessName,
        business_type: data.businessType,
        business_registration_number: data.businessRegistrationNumber,
        tax_id: data.taxId,
        location: data.location,
        contact_info: data.contactInfo,
        wallet_info: {
          main_wallet_id: mainWallet._id,
          commission_wallet_id: commissionWallet._id,
          available_balance: 0,
          commission_balance: 0,
          float_balance: 0
        }
      });

      await agent.save();

      logger.info('agent registered successfully', {
        agentId: agent._id,
        agentCode: agent.agent_code,
        userId: data.userId,
        businessName: data.businessName
      });

      return agent;
    } catch (error: any) {
      logger.error('error registering agent:', error);
      throw error;
    }
  }

  static async approveAgent(
    agentId: string,
    approverId: string,
    notes?: string
  ): Promise<boolean> {
    try {
      const agent = await Agent.findById(agentId);
      if (!agent) {
        throw new Error('agent not found');
      }

      if (agent.agent_status !== AgentStatus.PENDING) {
        throw new Error('agent is not in pending status');
      }

      agent.agent_status = AgentStatus.ACTIVE;
      agent.kyc_status = KycStatus.APPROVED;
      agent.approval_data = {
        approved_by: new mongoose.Types.ObjectId(approverId),
        approved_at: new Date(),
        approval_notes: notes || ''
      };

      await agent.save();

      try {
        const user = await UserService.getUserById(agent.user_id.toString());
        if (user) {
          await UserService.updateProfile(agent.user_id.toString(), {});
          await User.findByIdAndUpdate(
            agent.user_id,
            { $set: { role: UserRole.AGENT } }
          );
        }
      } catch (error) {
        logger.error('Failed to update user role to agent:', error);
        // Don't fail the approval if role update fails
      }

      logger.info('agent approved', {
        agentId,
        agentCode: agent.agent_code,
        approverId
      });

      return true;
    } catch (error: any) {
      logger.error('error approving agent:', error);
      throw error;
    }
  }

  static async performCashIn(data: {
    agentId: string;
    customerId: string;
    amount: number;
    currency: Currency;
    description?: string;
  }): Promise<{
    success: boolean;
    transactionId: string;
    commission: number;
    customerBalance: number;
    agentBalance: number;
  }> {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();

      const agent = await Agent.findById(data.agentId).session(session);
      if (!agent) {
        throw new Error('agent not found');
      }

      const canPerform = (agent as any).canPerformTransaction('cash_in', data.amount);
      if (!canPerform.allowed) {
        throw new Error(canPerform.reason);
      }

      const customerWallet = await WalletService.getDefaultWallet(data.customerId, data.currency);
      if (!customerWallet) {
        throw new Error('customer wallet not found');
      }

      const agentWallet = await WalletService.getWalletById(agent.wallet_info.main_wallet_id.toString());
      if (!agentWallet) {
        throw new Error('agent wallet not found');
      }

      const commission = (agent as any).calculateCommission('cash_in', data.amount);

      const customerResult = await WalletService.creditWallet({
        walletId: customerWallet._id.toString(),
        amount: data.amount,
        description: data.description || `Cash in via agent ${agent.agent_code}`,
        transactionType: TransactionType.CASH_IN,
        metadata: {
          agentId: data.agentId,
          agentCode: agent.agent_code,
          commission,
          transactionType: 'cash_in'
        }
      });

      await WalletService.debitWallet({
        walletId: agentWallet._id.toString(),
        amount: data.amount,
        description: `Cash in for customer ${data.customerId}`,
        transactionType: TransactionType.CASH_IN,
        metadata: {
          customerId: data.customerId,
          commission,
          transactionType: 'cash_in'
        }
      });

      const commissionWallet = await WalletService.getWalletById(agent.wallet_info.commission_wallet_id.toString());
      if (commissionWallet) {
        await WalletService.creditWallet({
          walletId: commissionWallet._id.toString(),
          amount: commission,
          description: `Cash in commission - ${agent.agent_code}`,
          transactionType: TransactionType.COMMISSION,
          metadata: {
            relatedTransaction: customerResult.transaction._id,
            transactionType: 'cash_in'
          }
        });
      }

      (agent as any).updateStatistics('cash_in', data.amount, commission);
      agent.wallet_info.available_balance = agentWallet.balance - data.amount;
      agent.wallet_info.commission_balance += commission;
      await agent.save({ session });

      await session.commitTransaction();

      logger.info('cash in transaction completed', {
        agentId: data.agentId,
        customerId: data.customerId,
        amount: data.amount,
        commission,
        transactionId: customerResult.transaction._id
      });

      return {
        success: true,
        transactionId: customerResult.transaction._id.toString(),
        commission,
        customerBalance: customerWallet.balance + data.amount,
        agentBalance: agentWallet.balance - data.amount
      };
    } catch (error: any) {
      await session.abortTransaction();
      logger.error('cash in transaction failed:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async performCashOut(data: {
    agentId: string;
    customerId: string;
    amount: number;
    currency: Currency;
    description?: string;
  }): Promise<{
    success: boolean;
    transactionId: string;
    commission: number;
    customerBalance: number;
    agentBalance: number;
  }> {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();

      const agent = await Agent.findById(data.agentId).session(session);
      if (!agent) {
        throw new Error('agent not found');
      }

      const canPerform = (agent as any).canPerformTransaction('cash_out', data.amount);
      if (!canPerform.allowed) {
        throw new Error(canPerform.reason);
      }

      const customerWallet = await WalletService.getDefaultWallet(data.customerId, data.currency);
      if (!customerWallet) {
        throw new Error('customer wallet not found');
      }

      if (customerWallet.available_balance < data.amount) {
        throw new Error('insufficient customer balance');
      }

      const agentWallet = await WalletService.getWalletById(agent.wallet_info.main_wallet_id.toString());
      if (!agentWallet) {
        throw new Error('agent wallet not found');
      }

      const commission = (agent as any).calculateCommission('cash_out', data.amount);

      const customerResult = await WalletService.debitWallet({
        walletId: customerWallet._id.toString(),
        amount: data.amount,
        description: data.description || `Cash out via agent ${agent.agent_code}`,
        transactionType: TransactionType.CASH_OUT,
        metadata: {
          agentId: data.agentId,
          agentCode: agent.agent_code,
          commission,
          transactionType: 'cash_out'
        }
      });

      await WalletService.creditWallet({
        walletId: agentWallet._id.toString(),
        amount: data.amount,
        description: `Cash out for customer ${data.customerId}`,
        transactionType: TransactionType.CASH_OUT,
        metadata: {
          customerId: data.customerId,
          commission,
          transactionType: 'cash_out'
        }
      });

      const commissionWallet = await WalletService.getWalletById(agent.wallet_info.commission_wallet_id.toString());
      if (commissionWallet) {
        await WalletService.creditWallet({
          walletId: commissionWallet._id.toString(),
          amount: commission,
          description: `Cash out commission - ${agent.agent_code}`,
          transactionType: TransactionType.COMMISSION,
          metadata: {
            relatedTransaction: customerResult.transaction._id,
            transactionType: 'cash_out'
          }
        });
      }

      (agent as any).updateStatistics('cash_out', data.amount, commission);
      agent.wallet_info.available_balance = agentWallet.balance + data.amount;
      agent.wallet_info.commission_balance += commission;
      await agent.save({ session });

      await session.commitTransaction();

      logger.info('cash out transaction completed', {
        agentId: data.agentId,
        customerId: data.customerId,
        amount: data.amount,
        commission,
        transactionId: customerResult.transaction._id
      });

      return {
        success: true,
        transactionId: customerResult.transaction._id.toString(),
        commission,
        customerBalance: customerWallet.balance - data.amount,
        agentBalance: agentWallet.balance + data.amount
      };
    } catch (error: any) {
      await session.abortTransaction();
      logger.error('cash out transaction failed:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async getAgentById(agentId: string): Promise<IAgent | null> {
    try {
      if (!mongoose.Types.ObjectId.isValid(agentId)) {
        return null;
      }

      const agent = await Agent.findById(agentId)
        .populate('user_id', 'first_name last_name email phone')
        .populate('parent_agent', 'agent_code business_name')
        .populate('sub_agents', 'agent_code business_name agent_status');

      return agent;
    } catch (error: any) {
      logger.error('error getting agent by id:', error);
      return null;
    }
  }

  static async getAgentByCode(agentCode: string): Promise<IAgent | null> {
    try {
      const agent = await Agent.findOne({ agent_code: agentCode.toUpperCase() })
        .populate('user_id', 'first_name last_name email phone');

      return agent;
    } catch (error: any) {
      logger.error('error getting agent by code:', error);
      return null;
    }
  }

  static async searchAgents(query: {
    search?: string;
    status?: AgentStatus;
    kycStatus?: KycStatus;
    city?: string;
    state?: string;
    businessType?: string;
    page?: number;
    limit?: number;
  }): Promise<{
    agents: IAgent[];
    total: number;
    page: number;
    pages: number;
  }> {
    try {
      const page = query.page || 1;
      const limit = Math.min(query.limit || 20, 100);
      const skip = (page - 1) * limit;

      const filter: any = {};

      if (query.search) {
        filter.$or = [
          { agent_code: { $regex: query.search, $options: 'i' } },
          { business_name: { $regex: query.search, $options: 'i' } },
          { 'contact_info.email': { $regex: query.search, $options: 'i' } },
          { 'contact_info.phone': { $regex: query.search, $options: 'i' } }
        ];
      }

      if (query.status) filter.agent_status = query.status;
      if (query.kycStatus) filter.kyc_status = query.kycStatus;
      if (query.city) filter['location.city'] = query.city;
      if (query.state) filter['location.state'] = query.state;
      if (query.businessType) filter.business_type = query.businessType;

      const [agents, total] = await Promise.all([
        Agent.find(filter)
          .sort({ created_at: -1 })
          .skip(skip)
          .limit(limit)
          .populate('user_id', 'first_name last_name email')
          .lean(),
        Agent.countDocuments(filter)
      ]);

      return {
        agents: agents as IAgent[],
        total,
        page,
        pages: Math.ceil(total / limit)
      };
    } catch (error: any) {
      logger.error('error searching agents:', error);
      throw error;
    }
  }

  private static async generateAgentCode(city: string): Promise<string> {
    try {
      const cityPrefix = city.substring(0, 3).toUpperCase();
      let agentCode: string;
      let isUnique = false;
      let attempts = 0;

      do {
        const randomNum = Math.floor(Math.random() * 9000) + 1000;
        agentCode = `${cityPrefix}${randomNum}`;
        
        const existing = await Agent.findOne({ agent_code: agentCode });
        isUnique = !existing;
        attempts++;
      } while (!isUnique && attempts < 10);

      if (!isUnique) {
        // fallback to timestamp-based code
        agentCode = `AGT${Date.now().toString().slice(-6)}`;
      }

      return agentCode;
    } catch (error: any) {
      logger.error('error generating agent code:', error);
      return `AGT${Date.now().toString().slice(-6)}`;
    }
  }

  static async suspendAgent(
    agentId: string,
    suspendedBy: string,
    reason: string,
    notes?: string
  ): Promise<boolean> {
    try {
      const agent = await Agent.findByIdAndUpdate(
        agentId,
        {
          $set: {
            agent_status: AgentStatus.SUSPENDED,
            suspension_data: {
              suspended_by: new mongoose.Types.ObjectId(suspendedBy),
              suspended_at: new Date(),
              suspension_reason: reason,
              suspension_notes: notes || ''
            }
          }
        },
        { new: true }
      );

      if (agent) {
        logger.info('agent suspended', {
          agentId,
          agentCode: agent.agent_code,
          suspendedBy,
          reason
        });
        return true;
      }

      return false;
    } catch (error: any) {
      logger.error('error suspending agent:', error);
      return false;
    }
  }

  static async reactivateAgent(agentId: string): Promise<boolean> {
    try {
      const agent = await Agent.findByIdAndUpdate(
        agentId,
        {
          $set: { agent_status: AgentStatus.ACTIVE },
          $unset: { suspension_data: 1 }
        },
        { new: true }
      );

      if (agent) {
        logger.info('agent reactivated', {
          agentId,
          agentCode: agent.agent_code
        });
        return true;
      }

      return false;
    } catch (error: any) {
      logger.error('error reactivating agent:', error);
      return false;
    }
  }

  static async getAgentDashboard(agentId: string): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      const agent = await Agent.findById(agentId)
        .populate('user_id', 'first_name last_name email phone')
        .populate('main_wallet_id')
        .populate('commission_wallet_id');

      if (!agent) {
        return {
          success: false,
          message: 'agent not found'
        };
      }

      const [
        performanceMetrics,
        commissionData,
        transactionStats,
        recentTransactions,
        floatBalance
      ] = await Promise.all([
        this.getAgentPerformanceMetrics(agentId),
        this.getAgentCommissionData(agentId),
        this.getAgentTransactionStats(agentId),
        this.getAgentRecentTransactions(agentId, 10),
        this.getAgentFloatBalance(agentId)
      ]);

      const dashboardData = {
        agentInfo: {
          id: agent._id.toString(),
          agentCode: agent.agent_code,
          businessName: agent.business_name,
          businessType: agent.business_type,
          status: agent.agent_status,
          kycStatus: agent.kyc_status,
          isActive: agent.is_active,
          performanceRating: agent.performance_rating || 0,
          location: agent.location,
          contactInfo: agent.contact_info
        },

        performance: performanceMetrics,
        commission: commissionData,
        transactions: transactionStats,
        recentTransactions: recentTransactions,
        floatInfo: floatBalance,

        quickActions: [
          { action: 'cash_in', label: 'Cash In', available: agent.is_active },
          { action: 'cash_out', label: 'Cash Out', available: agent.is_active },
          { action: 'float_request', label: 'Request Float', available: agent.is_active },
          { action: 'commission_withdraw', label: 'Withdraw Commission', available: agent.is_active }
        ].filter(action => action.available)
      };

      return {
        success: true,
        data: dashboardData,
        message: 'agent dashboard retrieved successfully'
      };
    } catch (error: any) {
      logger.error('Error getting agent dashboard:', error);
      return {
        success: false,
        message: 'failed to retrieve agent dashboard'
      };
    }
  }

  static async getAgentPerformanceMetrics(agentId: string): Promise<any> {
    try {
      const agent = await Agent.findById(agentId);
      if (!agent) return {};

      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

      const { Transaction } = await import('../models/transaction.model');

      const [
        monthlyStats,
        weeklyStats,
        dailyStats
      ] = await Promise.all([
        Transaction.aggregate([
          {
            $match: {
              'metadata.agentId': agent._id,
              created_at: { $gte: thirtyDaysAgo },
              status: 'completed'
            }
          },
          {
            $group: {
              _id: null,
              totalTransactions: { $sum: 1 },
              totalVolume: { $sum: '$amount' },
              totalCommission: { $sum: '$metadata.agentCommission' },
              avgTransactionSize: { $avg: '$amount' }
            }
          }
        ]),
        Transaction.aggregate([
          {
            $match: {
              'metadata.agentId': agent._id,
              created_at: { $gte: sevenDaysAgo },
              status: 'completed'
            }
          },
          {
            $group: {
              _id: null,
              totalTransactions: { $sum: 1 },
              totalVolume: { $sum: '$amount' },
              totalCommission: { $sum: '$metadata.agentCommission' }
            }
          }
        ]),
        Transaction.aggregate([
          {
            $match: {
              'metadata.agentId': agent._id,
              created_at: { $gte: new Date(new Date().setHours(0, 0, 0, 0)) },
              status: 'completed'
            }
          },
          {
            $group: {
              _id: null,
              totalTransactions: { $sum: 1 },
              totalVolume: { $sum: '$amount' },
              totalCommission: { $sum: '$metadata.agentCommission' }
            }
          }
        ])
      ]);

      const monthly = monthlyStats[0] || { totalTransactions: 0, totalVolume: 0, totalCommission: 0, avgTransactionSize: 0 };
      const weekly = weeklyStats[0] || { totalTransactions: 0, totalVolume: 0, totalCommission: 0 };
      const daily = dailyStats[0] || { totalTransactions: 0, totalVolume: 0, totalCommission: 0 };

      return {
        monthly: {
          transactions: monthly.totalTransactions,
          volume: Math.round(monthly.totalVolume * 100) / 100,
          commission: Math.round(monthly.totalCommission * 100) / 100,
          avgTransactionSize: Math.round(monthly.avgTransactionSize * 100) / 100
        },
        weekly: {
          transactions: weekly.totalTransactions,
          volume: Math.round(weekly.totalVolume * 100) / 100,
          commission: Math.round(weekly.totalCommission * 100) / 100
        },
        daily: {
          transactions: daily.totalTransactions,
          volume: Math.round(daily.totalVolume * 100) / 100,
          commission: Math.round(daily.totalCommission * 100) / 100
        },
        performanceRating: agent.performance_rating || 0,
        totalLifetimeTransactions: agent.statistics.total_transactions,
        totalLifetimeVolume: Math.round(agent.statistics.total_volume * 100) / 100,
        totalLifetimeCommission: Math.round(agent.statistics.total_commission_earned * 100) / 100
      };
    } catch (error: any) {
      logger.error('Error getting agent performance metrics:', error);
      return {};
    }
  }

  static async getAgentCommissionData(agentId: string): Promise<any> {
    try {
      const agent = await Agent.findById(agentId);
      if (!agent) return {};

      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

      const { Transaction } = await import('../models/transaction.model');

      const commissionBreakdown = await Transaction.aggregate([
        {
          $match: {
            'metadata.agentId': agent._id,
            created_at: { $gte: thirtyDaysAgo },
            status: 'completed'
          }
        },
        {
          $group: {
            _id: '$type',
            totalCommission: { $sum: '$metadata.agentCommission' },
            transactionCount: { $sum: 1 }
          }
        },
        { $sort: { totalCommission: -1 } }
      ]);

      const totalCommission = commissionBreakdown.reduce((sum, item) => sum + item.totalCommission, 0);

      return {
        availableBalance: agent.wallet_info.commission_balance,
        totalEarned: Math.round(totalCommission * 100) / 100,
        commissionStructure: agent.commission_structure,
        breakdown: commissionBreakdown.map(item => ({
          type: item._id,
          commission: Math.round(item.totalCommission * 100) / 100,
          transactions: item.transactionCount,
          percentage: totalCommission > 0 ? Math.round((item.totalCommission / totalCommission) * 100) : 0
        })),
        withdrawalHistory: [] 
      };
    } catch (error: any) {
      logger.error('Error getting agent commission data:', error);
      return {};
    }
  }

  static async getAgentTransactionStats(agentId: string): Promise<any> {
    try {
      const agent = await Agent.findById(agentId);
      if (!agent) return {};

      const { Transaction } = await import('../models/transaction.model');

      const stats = await Transaction.aggregate([
        {
          $match: {
            'metadata.agentId': agent._id
          }
        },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
            volume: { $sum: '$amount' }
          }
        }
      ]);

      const totalTransactions = stats.reduce((sum, stat) => sum + stat.count, 0);
      const totalVolume = stats.reduce((sum, stat) => sum + stat.volume, 0);

      return {
        total: totalTransactions,
        completed: stats.find(s => s._id === 'completed')?.count || 0,
        pending: stats.find(s => s._id === 'pending')?.count || 0,
        failed: stats.find(s => s._id === 'failed')?.count || 0,
        totalVolume: Math.round(totalVolume * 100) / 100,
        successRate: totalTransactions > 0 ?
          Math.round(((stats.find(s => s._id === 'completed')?.count || 0) / totalTransactions) * 100) : 0
      };
    } catch (error: any) {
      logger.error('Error getting agent transaction stats:', error);
      return {};
    }
  }

  static async getAgentRecentTransactions(agentId: string, limit: number = 10): Promise<any[]> {
    try {
      const agent = await Agent.findById(agentId);
      if (!agent) return [];

      const { Transaction } = await import('../models/transaction.model');

      const transactions = await Transaction.find({
        'metadata.agentId': agent._id
      })
      .populate('user_id', 'first_name last_name email')
      .sort({ created_at: -1 })
      .limit(limit)
      .lean();

      return transactions.map(txn => ({
        id: txn._id.toString(),
        type: txn.type,
        amount: Math.round(txn.amount * 100) / 100,
        currency: txn.currency,
        status: txn.status,
        customer: (txn.user_id as any) ?
          `${(txn.user_id as any).first_name} ${(txn.user_id as any).last_name}` : 'Unknown',
        customerEmail: (txn.user_id as any)?.email,
        commission: Math.round(((txn.metadata as any)?.agentCommission || 0) * 100) / 100,
        reference: txn.external_reference || txn._id.toString(),
        createdAt: txn.created_at,
        description: txn.description
      }));
    } catch (error: any) {
      logger.error('Error getting agent recent transactions:', error);
      return [];
    }
  }

  static async getAgentFloatBalance(agentId: string): Promise<any> {
    try {
      const agent = await Agent.findById(agentId);
      if (!agent) return {};

      return {
        currentBalance: agent.wallet_info.float_balance,
        currency: agent.wallet_info.currency,
        minimumRequired: agent.float_management.minimum_balance,
        maximumAllowed: agent.float_management.maximum_balance,
        autoTopupEnabled: agent.float_management.auto_topup_enabled,
        autoTopupThreshold: agent.float_management.auto_topup_threshold,
        lastTopup: agent.float_management.last_topup_date,
        status: this.getFloatStatus(agent)
      };
    } catch (error: any) {
      logger.error('Error getting agent float balance:', error);
      return {};
    }
  }

  private static getFloatStatus(agent: any): string {
    const balance = agent.wallet_info.float_balance;
    const minimum = agent.float_management.minimum_balance;
    const maximum = agent.float_management.maximum_balance;

    if (balance < minimum) return 'low';
    if (balance > maximum) return 'high';
    return 'normal';
  }
}
