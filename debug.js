// Simple debug script to test the server
const { spawn } = require('child_process');

console.log('Starting debug process...');

const child = spawn('npx', ['ts-node', 'src/server.ts'], {
  stdio: ['inherit', 'pipe', 'pipe'],
  cwd: process.cwd()
});

child.stdout.on('data', (data) => {
  console.log('STDOUT:', data.toString());
});

child.stderr.on('data', (data) => {
  console.error('STDERR:', data.toString());
});

child.on('close', (code) => {
  console.log(`Process exited with code ${code}`);
});

child.on('error', (error) => {
  console.error('Process error:', error);
});

// Kill after 10 seconds if still running
setTimeout(() => {
  if (!child.killed) {
    console.log('Killing process after timeout...');
    child.kill();
  }
}, 10000);
