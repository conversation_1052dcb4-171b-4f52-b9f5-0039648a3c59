#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

// Set test environment
process.env.NODE_ENV = 'test';

// Load test environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env.test') });

const testType = process.argv[2] || 'all';
const args = process.argv.slice(3);

const testCommands = {
  unit: ['npx', 'jest', '--testPathPattern=unit', ...args],
  integration: ['npx', 'jest', '--testPathPattern=integration', ...args],
  e2e: ['npx', 'jest', '--testPathPattern=e2e', ...args],
  all: ['npx', 'jest', ...args]
};

const command = testCommands[testType];

if (!command) {
  console.error(`Unknown test type: ${testType}`);
  console.error('Available types: unit, integration, e2e, all');
  process.exit(1);
}

console.log(`Running ${testType} tests...`);
console.log(`Command: ${command.join(' ')}`);

const testProcess = spawn(command[0], command.slice(1), {
  stdio: 'inherit',
  cwd: path.join(__dirname, '..'),
  env: {
    ...process.env,
    NODE_ENV: 'test'
  }
});

testProcess.on('close', (code) => {
  console.log(`Test process exited with code ${code}`);
  process.exit(code);
});

testProcess.on('error', (error) => {
  console.error('Failed to start test process:', error);
  process.exit(1);
});
