import mongoose from 'mongoose';
import { logger } from './logger';

interface DbConfig {
  uri: string;
  options: mongoose.ConnectOptions;
}

class DatabaseConnection {
  private static instance: DatabaseConnection;
  private isConnected = false;

  private constructor() {}

  public static getInstance(): DatabaseConnection {
    if (!DatabaseConnection.instance) {
      DatabaseConnection.instance = new DatabaseConnection();
    }
    return DatabaseConnection.instance;
  }

  public async connect(): Promise<void> {
    if (this.isConnected) {
      logger.info('Database already connected');
      return;
    }

    try {
      const config: DbConfig = {
        uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/aetrust',
        options: {
          maxPoolSize: parseInt(process.env.DB_CONNECTION_POOL_SIZE || '10'),
          serverSelectionTimeoutMS: parseInt(process.env.DB_CONNECTION_TIMEOUT || '30000'),
          socketTimeoutMS: 45000,
          retryWrites: true,
          writeConcern: { w: 'majority' },
        }
      };

      await mongoose.connect(config.uri, config.options);
      this.isConnected = true;
      
      logger.info('MongoDB connected successfully');
      
      // Handle connection events
      mongoose.connection.on('error', (error) => {
        logger.error('MongoDB connection error:', error);
        this.isConnected = false;
      });

      mongoose.connection.on('disconnected', () => {
        logger.warn('MongoDB disconnected');
        this.isConnected = false;
      });

      mongoose.connection.on('reconnected', () => {
        logger.info('MongoDB reconnected');
        this.isConnected = true;
      });

    } catch (error) {
      logger.error('Failed to connect to MongoDB:', error);
      this.isConnected = false;
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    if (!this.isConnected) {
      return;
    }

    try {
      await mongoose.disconnect();
      this.isConnected = false;
      logger.info('MongoDB disconnected successfully');
    } catch (error) {
      logger.error('Error disconnecting from MongoDB:', error);
      throw error;
    }
  }

  public getConnectionStatus(): boolean {
    return this.isConnected && mongoose.connection.readyState === 1;
  }

  public async healthCheck(): Promise<{ status: string; details: any }> {
    try {
      const adminDb = mongoose.connection.db?.admin();
      const result = await adminDb?.ping();
      
      return {
        status: 'healthy',
        details: {
          connected: this.isConnected,
          readyState: mongoose.connection.readyState,
          host: mongoose.connection.host,
          port: mongoose.connection.port,
          name: mongoose.connection.name,
          ping: result
        }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          connected: this.isConnected,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      };
    }
  }
}

export const db = DatabaseConnection.getInstance();
