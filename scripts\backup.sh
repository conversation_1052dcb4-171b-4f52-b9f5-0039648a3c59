#!/bin/bash

# AeTrust Backend Backup Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKUP_DIR=${BACKUP_DIR:-./backups}
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-30}
COMPRESS=${COMPRESS_BACKUPS:-true}

echo -e "${BLUE}📦 Starting AeTrust Backend Backup${NC}"
echo -e "${BLUE}Timestamp: $TIMESTAMP${NC}"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# MongoDB Backup
echo -e "${YELLOW}🗄️ Backing up MongoDB...${NC}"
MONGO_BACKUP_DIR="$BACKUP_DIR/mongodb_$TIMESTAMP"
mkdir -p $MONGO_BACKUP_DIR

# Check if MongoDB container is running
if docker ps | grep -q "mongodb"; then
    # Get MongoDB container name
    MONGO_CONTAINER=$(docker ps --format "table {{.Names}}" | grep mongodb | head -1)
    
    # Create MongoDB dump
    docker exec $MONGO_CONTAINER mongodump --out /backups/mongodb_$TIMESTAMP
    
    # Copy backup from container to host
    docker cp $MONGO_CONTAINER:/backups/mongodb_$TIMESTAMP $BACKUP_DIR/
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ MongoDB backup completed${NC}"
    else
        echo -e "${RED}❌ MongoDB backup failed${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}⚠️ MongoDB container not running, skipping database backup${NC}"
fi

# Redis Backup
echo -e "${YELLOW}🔴 Backing up Redis...${NC}"
REDIS_BACKUP_DIR="$BACKUP_DIR/redis_$TIMESTAMP"
mkdir -p $REDIS_BACKUP_DIR

# Check if Redis container is running
if docker ps | grep -q "redis"; then
    # Get Redis container name
    REDIS_CONTAINER=$(docker ps --format "table {{.Names}}" | grep redis | head -1)
    
    # Force Redis to save current state
    docker exec $REDIS_CONTAINER redis-cli BGSAVE
    
    # Wait for background save to complete
    sleep 5
    
    # Copy Redis dump file
    docker cp $REDIS_CONTAINER:/data/dump.rdb $REDIS_BACKUP_DIR/
    
    # Copy AOF file if it exists
    docker cp $REDIS_CONTAINER:/data/appendonly.aof $REDIS_BACKUP_DIR/ 2>/dev/null || true
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Redis backup completed${NC}"
    else
        echo -e "${RED}❌ Redis backup failed${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}⚠️ Redis container not running, skipping Redis backup${NC}"
fi

# Application Files Backup
echo -e "${YELLOW}📁 Backing up application files...${NC}"
APP_BACKUP_DIR="$BACKUP_DIR/app_$TIMESTAMP"
mkdir -p $APP_BACKUP_DIR

# Backup important application files
cp -r logs $APP_BACKUP_DIR/ 2>/dev/null || true
cp -r config $APP_BACKUP_DIR/ 2>/dev/null || true
cp .env* $APP_BACKUP_DIR/ 2>/dev/null || true
cp docker-compose*.yml $APP_BACKUP_DIR/ 2>/dev/null || true

echo -e "${GREEN}✅ Application files backup completed${NC}"

# Compress backups if enabled
if [ "$COMPRESS" = true ]; then
    echo -e "${YELLOW}🗜️ Compressing backups...${NC}"
    
    cd $BACKUP_DIR
    tar -czf "aetrust_backup_$TIMESTAMP.tar.gz" \
        mongodb_$TIMESTAMP \
        redis_$TIMESTAMP \
        app_$TIMESTAMP
    
    # Remove uncompressed directories
    rm -rf mongodb_$TIMESTAMP redis_$TIMESTAMP app_$TIMESTAMP
    
    echo -e "${GREEN}✅ Backup compression completed${NC}"
    BACKUP_FILE="aetrust_backup_$TIMESTAMP.tar.gz"
else
    BACKUP_FILE="backup_$TIMESTAMP"
fi

# Upload to S3 if configured
if [ ! -z "$AWS_S3_BUCKET" ] && [ ! -z "$AWS_ACCESS_KEY_ID" ]; then
    echo -e "${YELLOW}☁️ Uploading backup to S3...${NC}"
    
    if command -v aws &> /dev/null; then
        aws s3 cp "$BACKUP_DIR/$BACKUP_FILE" "s3://$AWS_S3_BUCKET/backups/"
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Backup uploaded to S3${NC}"
        else
            echo -e "${RED}❌ S3 upload failed${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️ AWS CLI not installed, skipping S3 upload${NC}"
    fi
fi

# Clean up old backups
echo -e "${YELLOW}🧹 Cleaning up old backups...${NC}"
find $BACKUP_DIR -name "aetrust_backup_*.tar.gz" -mtime +$RETENTION_DAYS -delete 2>/dev/null || true
find $BACKUP_DIR -name "backup_*" -mtime +$RETENTION_DAYS -exec rm -rf {} \; 2>/dev/null || true

# Clean up old S3 backups if configured
if [ ! -z "$AWS_S3_BUCKET" ] && command -v aws &> /dev/null; then
    aws s3 ls "s3://$AWS_S3_BUCKET/backups/" | \
    awk '{print $4}' | \
    while read file; do
        if [[ $file =~ aetrust_backup_([0-9]{8})_([0-9]{6})\.tar\.gz ]]; then
            file_date="${BASH_REMATCH[1]}"
            cutoff_date=$(date -d "$RETENTION_DAYS days ago" +%Y%m%d)
            if [[ $file_date < $cutoff_date ]]; then
                aws s3 rm "s3://$AWS_S3_BUCKET/backups/$file"
                echo -e "${BLUE}🗑️ Deleted old S3 backup: $file${NC}"
            fi
        fi
    done
fi

# Calculate backup size
if [ "$COMPRESS" = true ]; then
    BACKUP_SIZE=$(du -h "$BACKUP_DIR/$BACKUP_FILE" | cut -f1)
else
    BACKUP_SIZE=$(du -sh "$BACKUP_DIR/$BACKUP_FILE" | cut -f1)
fi

echo -e "${GREEN}🎉 Backup completed successfully!${NC}"
echo -e "${GREEN}Backup file: $BACKUP_FILE${NC}"
echo -e "${GREEN}Backup size: $BACKUP_SIZE${NC}"
echo -e "${GREEN}Location: $BACKUP_DIR${NC}"

# Send notification if configured
if [ ! -z "$SLACK_WEBHOOK_URL" ]; then
    curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"📦 AeTrust Backend backup completed successfully\\nFile: $BACKUP_FILE\\nSize: $BACKUP_SIZE\"}" \
        $SLACK_WEBHOOK_URL
fi

# Create backup manifest
cat > "$BACKUP_DIR/backup_manifest_$TIMESTAMP.json" << EOF
{
    "timestamp": "$TIMESTAMP",
    "backup_file": "$BACKUP_FILE",
    "backup_size": "$BACKUP_SIZE",
    "components": {
        "mongodb": true,
        "redis": true,
        "application_files": true
    },
    "compressed": $COMPRESS,
    "retention_days": $RETENTION_DAYS,
    "created_at": "$(date -Iseconds)"
}
EOF

echo -e "${BLUE}📋 Backup manifest created: backup_manifest_$TIMESTAMP.json${NC}"
