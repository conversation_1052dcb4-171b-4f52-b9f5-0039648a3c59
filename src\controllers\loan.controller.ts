import { FastifyReply } from 'fastify';
import { AuthenticatedRequest, LoanType } from '../types';
import { LoanService } from '../services/loan.service';
import { UserService } from '../services/user.service';
import { ExternalApiService } from '../services/external-api.service';
import { SystemConfig } from '../models/system-config.model';
import { logger } from '../config/logger';

export const applyForLoan = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required',
      errorCode: 'AUTH_REQUIRED'
    });
  }

  try {
    const data = request.body as any;

    const loan = await LoanService.applyForLoan({
      userId: request.user.id,
      ...data
    });

    return reply.status(201).send({
      success: true,
      message: 'loan application submitted successfully',
      data: {
        loanApplication: {
          loanId: loan._id.toString(),
          loanAmount: loan.loan_amount,
          loanType: loan.loan_type,
          currency: loan.currency,
          interestRate: `${(loan.interest_rate * 100).toFixed(2)}%`,
          loanTermMonths: loan.loan_term_months,
          monthlyPayment: loan.monthly_payment,
          totalAmount: loan.total_amount,
          applicationStatus: loan.status,
          submittedAt: loan.created_at
        },
        repaymentSchedule: {
          numberOfPayments: loan.repayment_schedule.length,
          firstPaymentDate: loan.repayment_schedule[0]?.due_date,
          lastPaymentDate: loan.repayment_schedule[loan.repayment_schedule.length - 1]?.due_date,
          monthlyInstallment: loan.monthly_payment
        },
        nextSteps: {
          creditAssessmentPending: true,
          approvalRequired: true,
          estimatedProcessingTime: "24-48 hours",
          documentsRequired: loan.loan_type === 'business' ? ['businessLicense', 'taxReturns'] : []
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        timestamp: new Date().toISOString(),
        serviceType: "loanApplication"
      }
    });
  } catch (error: any) {
    logger.error('loan application error:', error);
    
    const errorMessages: Record<string, string> = {
      'user not found': 'user account not found',
      'maximum active loans limit reached': 'you have reached the maximum number of active loans'
    };

    const message = errorMessages[error.message] || 'loan application failed';
    
    return reply.status(400).send({
      success: false,
      message,
      errorCode: 'LOAN_APPLICATION_FAILED',
      errorDetails: {
        reason: error.message,
        timestamp: new Date().toISOString()
      }
    });
  }
};

export const getUserLoans = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { status } = request.query as any;

    const loans = await LoanService.getUserLoans(request.user.id, status);

    return reply.status(200).send({
      success: true,
      message: 'loans retrieved successfully',
      data: {
        loansList: loans.map(loan => ({
          loanId: loan._id.toString(),
          loanType: loan.loan_type,
          loanAmount: loan.loan_amount,
          currency: loan.currency,
          interestRate: `${(loan.interest_rate * 100).toFixed(2)}%`,
          loanStatus: loan.status,
          currentBalance: loan.current_balance,
          paidAmount: loan.paid_amount,
          outstandingAmount: loan.outstanding_amount,
          nextPaymentDate: loan.next_payment_date,
          daysOverdue: loan.days_overdue,
          repaymentProgress: (loan as any).repayment_progress,
          paymentStatus: (loan as any).payment_status,
          appliedAt: loan.created_at,
          disbursedAt: loan.disbursement_data?.disbursed_at
        })),
        summary: {
          totalLoans: loans.length,
          activeLoans: loans.filter(l => ['active', 'disbursed'].includes(l.status)).length,
          totalOutstanding: loans.reduce((sum, l) => sum + l.outstanding_amount, 0),
          totalPaid: loans.reduce((sum, l) => sum + l.paid_amount, 0)
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        filterApplied: status || 'all'
      }
    });
  } catch (error: any) {
    logger.error('get user loans error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve loans'
    });
  }
};

export const getLoanDetails = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { loanId } = request.params as any;

    const loan = await LoanService.getLoanById(loanId);
    
    if (!loan) {
      return reply.status(404).send({
        success: false,
        message: 'loan not found',
        errorCode: 'LOAN_NOT_FOUND'
      });
    }

    if (loan.user_id.toString() !== request.user.id) {
      return reply.status(403).send({
        success: false,
        message: 'access denied',
        errorCode: 'ACCESS_DENIED'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'loan details retrieved successfully',
      data: {
        loanDetails: {
          loanId: loan._id.toString(),
          loanType: loan.loan_type,
          loanAmount: loan.loan_amount,
          currency: loan.currency,
          interestRate: `${(loan.interest_rate * 100).toFixed(2)}%`,
          loanTermMonths: loan.loan_term_months,
          monthlyPayment: loan.monthly_payment,
          totalAmount: loan.total_amount,
          currentBalance: loan.current_balance,
          paidAmount: loan.paid_amount,
          outstandingAmount: loan.outstanding_amount,
          loanStatus: loan.status,
          purpose: loan.purpose,
          appliedAt: loan.created_at
        },
        repaymentInfo: {
          nextPaymentDate: loan.next_payment_date,
          nextPaymentAmount: loan.repayment_schedule.find(p => p.status === 'pending')?.total_amount || 0,
          daysOverdue: loan.days_overdue,
          lateFees: loan.late_fees,
          paymentStatus: (loan as any).payment_status,
          repaymentProgress: `${(loan as any).repayment_progress.toFixed(1)}%`
        },
        repaymentSchedule: loan.repayment_schedule.map(payment => ({
          paymentNumber: payment.payment_number,
          dueDate: payment.due_date,
          principalAmount: payment.principal_amount,
          interestAmount: payment.interest_amount,
          totalAmount: payment.total_amount,
          paymentStatus: payment.status,
          paidDate: payment.paid_date,
          paidAmount: payment.paid_amount,
          lateFee: payment.late_fee
        })),
        disbursementInfo: loan.disbursement_data ? {
          disbursedAt: loan.disbursement_data.disbursed_at,
          disbursedAmount: loan.disbursement_data.disbursed_amount,
          disbursementMethod: loan.disbursement_data.disbursement_method
        } : null,
        approvalInfo: loan.approval_data ? {
          approvedAt: loan.approval_data.approved_at,
          approvalNotes: loan.approval_data.approval_notes
        } : null
      }
    });
  } catch (error: any) {
    logger.error('get loan details error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve loan details'
    });
  }
};

export const getLoanRepaymentUI = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { loanId } = request.params as { loanId: string };

    const repaymentUIData = await LoanService.getLoanRepaymentUIData(loanId);

    return reply.status(200).send({
      success: true,
      message: 'loan repayment UI data retrieved successfully',
      data: repaymentUIData
    });
  } catch (error: any) {
    logger.error('get loan repayment UI error:', error);

    const errorMessages: Record<string, string> = {
      'Loan not found': 'loan not found'
    };

    return reply.status(error.message === 'Loan not found' ? 404 : 500).send({
      success: false,
      message: errorMessages[error.message] || 'failed to retrieve loan repayment data'
    });
  }
};

export const makeRepayment = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { loanId } = request.params as any;
    const { amount, paymentMethod = 'wallet' } = request.body as any;

    const result = await LoanService.makeRepayment(loanId, amount, paymentMethod);

    return reply.status(200).send({
      success: true,
      message: 'loan repayment processed successfully',
      data: {
        repaymentDetails: {
          loanId,
          paymentAmount: amount,
          paymentMethod,
          remainingBalance: result.remainingBalance,
          nextPaymentDate: result.nextPaymentDate,
          processedAt: new Date().toISOString()
        },
        loanStatus: {
          isFullyPaid: result.remainingBalance <= 0,
          remainingBalance: result.remainingBalance,
          paymentStatus: result.remainingBalance <= 0 ? 'completed' : 'active'
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        processingTimeMs: Date.now() - ((request as any).startTime || Date.now())
      }
    });
  } catch (error: any) {
    logger.error('loan repayment error:', error);
    
    const errorMessages: Record<string, string> = {
      'loan not found': 'loan not found',
      'loan is not active': 'loan is not in active status',
      'payment amount exceeds outstanding balance': 'payment amount is more than what you owe',
      'insufficient balance': 'insufficient wallet balance'
    };

    const message = errorMessages[error.message] || 'repayment failed';
    
    return reply.status(400).send({
      success: false,
      message,
      errorCode: 'REPAYMENT_FAILED',
      errorDetails: {
        reason: error.message,
        timestamp: new Date().toISOString()
      }
    });
  }
};

export const getLoanOffers = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { amount } = request.query as { amount?: string };
    const loanAmount = amount ? parseFloat(amount) : 5000;

    const user = await UserService.getUserById(request.user.id);
    if (!user) {
      return reply.status(404).send({
        success: false,
        message: 'user not found'
      });
    }

    if (!user.is_verified || user.kyc_status !== 'approved') {
      return reply.status(403).send({
        success: false,
        message: 'account verification and KYC approval required for loan offers'
      });
    }

    const loanUIData = await LoanService.getLoanUIData(loanAmount);

    const creditData = await ExternalApiService.getCreditScore(request.user.id, {
      email: user.email,
      phone: user.phone,
      accountAgeMonths: Math.floor((Date.now() - user.created_at.getTime()) / (1000 * 60 * 60 * 24 * 30))
    });

    const allLoans = await LoanService.getUserLoans(request.user.id);
    const activeLoans = allLoans.filter(loan =>
      loan.status === 'active' || loan.status === 'disbursed'
    );
    const hasActiveLoans = activeLoans.length > 0;
    const existingLoanCount = activeLoans.length;

    const offers = [];
    const baseEligibilityScore = Math.min(creditData.score / 10, 100); // convert to 0-100 scale

    if (creditData.score >= 650 && existingLoanCount < 2) {
      const personalLoanRate = await calculateLoanRate(LoanType.PERSONAL, creditData.score, existingLoanCount);
      const maxAmount = Math.min(user.wallet_balance * 10, await SystemConfig.getConfig('loanPersonalMaxAmount') || 5000);

      offers.push({
        loanType: 'personal',
        maxAmount: Math.max(maxAmount, await SystemConfig.getConfig('loanPersonalMinAmount') || 500),
        minAmount: await SystemConfig.getConfig('loanPersonalMinAmount') || 500,
        interestRate: Math.round(personalLoanRate * 100),
        maxTermMonths: await SystemConfig.getConfig('loanPersonalMaxTermMonths') || 24,
        minTermMonths: await SystemConfig.getConfig('loanPersonalMinTermMonths') || 3,
        eligibilityScore: Math.round(baseEligibilityScore * 0.9),
        preApproved: creditData.score >= 750 && !hasActiveLoans
      });
    }

    if (creditData.score >= await SystemConfig.getConfig('loanMicroloanMinCreditScore') || 600) {
      const microloanRate = await calculateLoanRate(LoanType.MICROLOAN, creditData.score, existingLoanCount);
      const maxAmount = Math.min(user.wallet_balance * 5, await SystemConfig.getConfig('loanMicroloanMaxAmount') || 1000);

      offers.push({
        loanType: 'microloan',
        maxAmount: Math.max(maxAmount, await SystemConfig.getConfig('loanMicroloanMinAmount') || 100),
        minAmount: await SystemConfig.getConfig('loanMicroloanMinAmount') || 100,
        interestRate: Math.round(microloanRate * 100),
        maxTermMonths: await SystemConfig.getConfig('loanMicroloanMaxTermMonths') || 12,
        minTermMonths: await SystemConfig.getConfig('loanMicroloanMinTermMonths') || 1,
        eligibilityScore: Math.round(baseEligibilityScore),
        preApproved: creditData.score >= 700 && existingLoanCount < 2
      });
    }

    const salaryAdvanceMinCreditScore = await SystemConfig.getConfig('loanSalaryAdvanceMinCreditScore') || 600;
    if (creditData.score >= salaryAdvanceMinCreditScore && user.agent_info?.is_active) {
      const salaryAdvanceRate = await calculateLoanRate(LoanType.SALARY_ADVANCE, creditData.score, existingLoanCount);
      const maxAmount = Math.min((user.agent_info?.total_commission_earned || 0) * 2, await SystemConfig.getConfig('loanSalaryAdvanceMaxAmount') || 2000);
      const minAmount = await SystemConfig.getConfig('loanSalaryAdvanceMinAmount') || 200;

      if (maxAmount >= minAmount) {
        offers.push({
          loanType: 'salaryAdvance',
          maxAmount,
          minAmount,
          interestRate: Math.round(salaryAdvanceRate * 100),
          maxTermMonths: await SystemConfig.getConfig('loanSalaryAdvanceMaxTermMonths') || 6,
          minTermMonths: await SystemConfig.getConfig('loanSalaryAdvanceMinTermMonths') || 1,
          eligibilityScore: Math.round(baseEligibilityScore * 0.8),
          preApproved: creditData.score >= 700 && existingLoanCount === 0
        });
      }
    }

    return reply.status(200).send({
      success: true,
      message: 'loan offers retrieved successfully',
      data: {

        loanUIData,
        availableOffers: offers.map(offer => ({
          loanType: offer.loanType,
          loanTypeName: offer.loanType.replace('_', ' ').toUpperCase(),
          maxLoanAmount: offer.maxAmount,
          minLoanAmount: offer.minAmount,
          interestRate: `${offer.interestRate}%`,
          maxTerm: `${offer.maxTermMonths} months`,
          minTerm: `${offer.minTermMonths} months`,
          eligibilityScore: offer.eligibilityScore,
          preApproved: offer.preApproved,
          estimatedMonthlyPayment: Math.round((offer.maxAmount * (offer.interestRate / 100 / 12)) * 100) / 100
        })),
        eligibilitySummary: {
          overallScore: Math.round(baseEligibilityScore),
          creditworthiness: getCreditworthiness(creditData.score),
          recommendedLoanType: offers.length > 0 ? offers[0]?.loanType : 'none',
          maxRecommendedAmount: offers.length > 0 ? Math.max(...offers.map(o => o.maxAmount)) : 0,
          creditScore: creditData.score,
          existingLoans: existingLoanCount,
          kycStatus: user.kyc_status
        }
      }
    });
  } catch (error: any) {
    logger.error('get loan offers error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve loan offers'
    });
  }
};

async function calculateLoanRate(loanType: LoanType, creditScore: number, existingLoans: number): Promise<number> {

  let baseRate = await SystemConfig.getConfig('loanDefaultInterestRate') || 0.15;

  switch (loanType) {
    case LoanType.PERSONAL:
      baseRate = await SystemConfig.getConfig('loanPersonalInterestRate') || 0.18;
      break;
    case LoanType.MICROLOAN:
      baseRate = await SystemConfig.getConfig('loanMicroloanInterestRate') || 0.20;
      break;
    case LoanType.SALARY_ADVANCE:
      baseRate = await SystemConfig.getConfig('loanSalaryAdvanceInterestRate') || 0.10;
      break;
    case LoanType.BUSINESS:
      baseRate = await SystemConfig.getConfig('loanBusinessInterestRate') || 0.12;
      break;
    case LoanType.BNPL:
      baseRate = await SystemConfig.getConfig('loanBnplInterestRate') || 0.25;
      break;
  }

  const excellentCreditDiscount = await SystemConfig.getConfig('loanExcellentCreditDiscount') || 0.03;
  const veryGoodCreditDiscount = await SystemConfig.getConfig('loanVeryGoodCreditDiscount') || 0.02;
  const goodCreditDiscount = await SystemConfig.getConfig('loanGoodCreditDiscount') || 0.01;
  const poorCreditPenalty = await SystemConfig.getConfig('loanPoorCreditPenalty') || 0.02;

  if (creditScore >= 800) {
    baseRate -= excellentCreditDiscount;
  } else if (creditScore >= 750) {
    baseRate -= veryGoodCreditDiscount;
  } else if (creditScore >= 700) {
    baseRate -= goodCreditDiscount;
  } else if (creditScore < 650) {
    baseRate += poorCreditPenalty;
  }

  const existingLoanPenalty = await SystemConfig.getConfig('loanExistingLoanPenalty') || 0.01;
  if (existingLoans > 0) {
    baseRate += existingLoans * existingLoanPenalty;
  }

  const minimumRate = await SystemConfig.getConfig('loanMinimumInterestRate') || 0.08;
  return Math.max(baseRate, minimumRate);
}

function getCreditworthiness(creditScore: number): string {
  if (creditScore >= 800) return 'excellent';
  if (creditScore >= 750) return 'veryGood';
  if (creditScore >= 700) return 'good';
  if (creditScore >= 650) return 'fair';
  return 'poor';
}
