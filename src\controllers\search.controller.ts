import { FastifyReply } from 'fastify';
import { AuthenticatedRequest, UserRole } from '../types';
import { SearchService } from '../services/search.service';
import { Transaction } from '../models/transaction.model';
import { logger } from '../config/logger';

export const searchUsers = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {

    if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
      return reply.status(403).send({
        success: false,
        message: 'admin access required'
      });
    }

    const queryParams = request.query as any;
    const filters = {
      query: queryParams?.q as string,
      status: queryParams?.status as string,
      dateFrom: queryParams?.dateFrom as string,
      dateTo: queryParams?.dateTo as string,
      page: parseInt(queryParams?.page as string) || 1,
      limit: parseInt(queryParams?.limit as string) || 20,
      sortBy: queryParams?.sortBy as string || 'created_at',
      sortOrder: (queryParams?.sortOrder as 'asc' | 'desc') || 'desc'
    };

    const result = await SearchService.searchUsers(filters);

    if (!result.success) {
      return reply.status(500).send(result);
    }

    logger.info('Users search performed', {
      adminId: request.user.id,
      filters,
      resultCount: result.data?.users?.length || 0
    });

    return reply.status(200).send({
      success: true,
      message: result.message,
      data: result.data,
      metadata: {
        searchType: 'users',
        performedBy: request.user.id,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error searching users:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to search users'
    });
  }
};

export const searchTransactions = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {

    if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
      return reply.status(403).send({
        success: false,
        message: 'admin access required'
      });
    }

    const queryParams = request.query as any;
    const filters = {
      query: queryParams?.q as string,
      status: queryParams?.status as string,
      type: queryParams?.type as string,
      currency: queryParams?.currency as string,
      dateFrom: queryParams?.dateFrom as string,
      dateTo: queryParams?.dateTo as string,
      amount: {
        min: queryParams?.minAmount ? parseFloat(queryParams.minAmount as string) : 0,
        max: queryParams?.maxAmount ? parseFloat(queryParams.maxAmount as string) : 999999999
      },
      page: parseInt(queryParams?.page as string) || 1,
      limit: parseInt(queryParams?.limit as string) || 20,
      sortBy: queryParams?.sortBy as string || 'created_at',
      sortOrder: (queryParams?.sortOrder as 'asc' | 'desc') || 'desc'
    };

    const result = await SearchService.searchTransactions(filters);

    if (!result.success) {
      return reply.status(500).send(result);
    }

    logger.info('Transactions search performed', {
      adminId: request.user.id,
      filters,
      resultCount: result.data?.transactions?.length || 0
    });

    return reply.status(200).send({
      success: true,
      message: result.message,
      data: result.data,
      metadata: {
        searchType: 'transactions',
        performedBy: request.user.id,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error searching transactions:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to search transactions'
    });
  }
};

export const searchAgents = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {

    if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
      return reply.status(403).send({
        success: false,
        message: 'admin access required'
      });
    }

    const queryParams = request.query as any;
    const filters = {
      query: queryParams?.q as string,
      status: queryParams?.status as string,
      dateFrom: queryParams?.dateFrom as string,
      dateTo: queryParams?.dateTo as string,
      page: parseInt(queryParams?.page as string) || 1,
      limit: parseInt(queryParams?.limit as string) || 20,
      sortBy: queryParams?.sortBy as string || 'created_at',
      sortOrder: (queryParams?.sortOrder as 'asc' | 'desc') || 'desc'
    };

    const result = await SearchService.searchAgents(filters);

    if (!result.success) {
      return reply.status(500).send(result);
    }

    logger.info('Agents search performed', {
      adminId: request.user.id,
      filters,
      resultCount: result.data?.agents?.length || 0
    });

    return reply.status(200).send({
      success: true,
      message: result.message,
      data: result.data,
      metadata: {
        searchType: 'agents',
        performedBy: request.user.id,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error searching agents:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to search agents'
    });
  }
};


export const globalSearch = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {

    if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
      return reply.status(403).send({
        success: false,
        message: 'admin access required'
      });
    }

    const queryParams = request.query as any;
    const query = queryParams?.q as string;
    const limit = parseInt(queryParams?.limit as string) || 5;

    if (!query || query.trim().length < 2) {
      return reply.status(400).send({
        success: false,
        message: 'search query must be at least 2 characters'
      });
    }

    const [usersResult, transactionsResult, agentsResult] = await Promise.all([
      SearchService.searchUsers({ query, limit }),
      SearchService.searchTransactions({ query, limit }),
      SearchService.searchAgents({ query, limit })
    ]);

    const globalResults = {
      users: usersResult.success ? usersResult.data?.users?.slice(0, limit) || [] : [],
      transactions: transactionsResult.success ? transactionsResult.data?.transactions?.slice(0, limit) || [] : [],
      agents: agentsResult.success ? agentsResult.data?.agents?.slice(0, limit) || [] : [],
      summary: {
        totalUsers: usersResult.success ? usersResult.data?.pagination?.totalItems || 0 : 0,
        totalTransactions: transactionsResult.success ? transactionsResult.data?.pagination?.totalItems || 0 : 0,
        totalAgents: agentsResult.success ? agentsResult.data?.pagination?.totalItems || 0 : 0
      }
    };

    logger.info('Global search performed', {
      adminId: request.user.id,
      query,
      resultCounts: {
        users: globalResults.users.length,
        transactions: globalResults.transactions.length,
        agents: globalResults.agents.length
      }
    });

    return reply.status(200).send({
      success: true,
      message: 'global search completed successfully',
      data: globalResults,
      metadata: {
        searchType: 'global',
        query,
        performedBy: request.user.id,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error performing global search:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to perform global search'
    });
  }
};

export const getSearchSuggestions = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {

    if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
      return reply.status(403).send({
        success: false,
        message: 'admin access required'
      });
    }

    const queryParams = request.query as any;
    const query = queryParams?.q as string;
    const type = queryParams?.type as string || 'all';

    if (!query || query.trim().length < 2) {
      return reply.status(200).send({
        success: true,
        message: 'search suggestions',
        data: { suggestions: [] }
      });
    }

    const suggestions: string[] = [];

    if (type === 'all' || type === 'users') {
      const { User } = await import('../models/user.model');
      const userSuggestions = await User.find({
        $or: [
          { email: { $regex: query, $options: 'i' } },
          { first_name: { $regex: query, $options: 'i' } },
          { last_name: { $regex: query, $options: 'i' } }
        ]
      })
      .select('email first_name last_name')
      .limit(5)
      .lean();

      userSuggestions.forEach(user => {
        suggestions.push(user.email);
        suggestions.push(`${user.first_name} ${user.last_name}`);
      });
    }

    if (type === 'all' || type === 'transactions') {
      const transactionSuggestions = await Transaction.find({
        $or: [
          { external_reference: { $regex: query, $options: 'i' } },
          { description: { $regex: query, $options: 'i' } }
        ]
      })
      .select('external_reference description')
      .limit(5)
      .lean();

      transactionSuggestions.forEach(txn => {
        if (txn.external_reference) suggestions.push(txn.external_reference);
        if (txn.description) suggestions.push(txn.description);
      });
    }

    const uniqueSuggestions = [...new Set(suggestions)].slice(0, 10);

    return reply.status(200).send({
      success: true,
      message: 'search suggestions retrieved',
      data: {
        suggestions: uniqueSuggestions,
        query,
        type
      }
    });

  } catch (error: any) {
    logger.error('Error getting search suggestions:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to get search suggestions'
    });
  }
};
