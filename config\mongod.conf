# MongoDB Production Configuration

# Network interfaces
net:
  port: 27017
  bindIp: 0.0.0.0
  maxIncomingConnections: 65536
  wireObjectCheck: true
  ipv6: false

# Storage
storage:
  dbPath: /data/db
  journal:
    enabled: true
    commitIntervalMs: 100
  directoryPerDB: true
  syncPeriodSecs: 60
  engine: wiredTiger
  wiredTiger:
    engineConfig:
      cacheSizeGB: 1
      journalCompressor: snappy
      directoryForIndexes: true
    collectionConfig:
      blockCompressor: snappy
    indexConfig:
      prefixCompression: true

# System log
systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log
  logRotate: reopen
  verbosity: 0
  component:
    accessControl:
      verbosity: 0
    command:
      verbosity: 0

# Process management
processManagement:
  fork: false
  pidFilePath: /var/run/mongodb/mongod.pid
  timeZoneInfo: /usr/share/zoneinfo

# Security
security:
  authorization: enabled
  keyFile: /etc/mongodb/keyfile

# Operation profiling
operationProfiling:
  slowOpThresholdMs: 100
  mode: slowOp

# Replication (for production clusters)
# replication:
#   replSetName: "aetrust-rs"

# Sharding (for large deployments)
# sharding:
#   clusterRole: shardsvr

# Set parameters
setParameter:
  enableLocalhostAuthBypass: false
  authenticationMechanisms: SCRAM-SHA-1,SCRAM-SHA-256
  maxLogSizeKB: 10240
  logLevel: 1
  cursorTimeoutMillis: 600000
  failIndexKeyTooLong: false
