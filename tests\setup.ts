import { MongoMemoryServer } from 'mongodb-memory-server';
import mongoose from 'mongoose';
import { FastifyInstance } from 'fastify';
import { createApp } from '../src/app';

let mongoServer: MongoMemoryServer;
let app: FastifyInstance;

export const setupTestDb = async () => {
  mongoServer = await MongoMemoryServer.create();
  const mongoUri = mongoServer.getUri();
  
  await mongoose.connect(mongoUri);
  
  const collections = mongoose.connection.collections;
  for (const key in collections) {
    await collections[key].deleteMany({});
  }
};

export const teardownTestDb = async () => {
  if (mongoose.connection.readyState !== 0) {
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
  }
  
  if (mongoServer) {
    await mongoServer.stop();
  }
};

export const setupTestApp = async () => {
  app = await createApp();
  await app.ready();
  return app;
};

export const teardownTestApp = async () => {
  if (app) {
    await app.close();
  }
};

export const clearDatabase = async () => {
  try {
    const collections = mongoose.connection.collections;
    for (const key in collections) {
      await collections[key].deleteMany({});
    }

    // Also drop indexes to ensure clean state
    for (const key in collections) {
      try {
        await collections[key].dropIndexes();
      } catch (error) {
        // Ignore errors 
      }
    }
  } catch (error) {
    console.error('Error clearing database:', error);
  }
};


export const createTestUser = (overrides = {}) => ({
  email: '<EMAIL>',
  phone: '+*************',
  password: 'Test123!@#',
  first_name: 'Test',
  last_name: 'User',
  role: 'user',
  is_verified: true,
  kyc_status: 'verified',
  account_status: 'active',
  wallet_balance: 1000,
  registration_step: 'completed',
  registration_completed: true,
  phone_verified: true,
  email_verified: true,
  transaction_pin_set: true,
  biometric_enabled: false,
  security: {
    login_attempts: 0,
    two_factor_enabled: false
  },
  preferences: {
    language: 'en',
    currency: 'NGN',
    notifications: {
      email: true,
      sms: true,
      push: true
    }
  },
  created_at: new Date(),
  updated_at: new Date(),
  ...overrides
});

export const createTestAgent = (overrides = {}) => ({
  user_id: new mongoose.Types.ObjectId(),
  agent_code: 'AGT001',
  business_name: 'Test Business',
  business_type: 'retail',
  location: {
    address: '123 Test Street',
    city: 'Lagos',
    state: 'Lagos',
    country: 'Nigeria'
  },
  contact_info: {
    phone: '+*************',
    email: '<EMAIL>'
  },
  kyc_status: 'verified',
  agent_status: 'active',
  commission_structure: {
    cash_in_rate: 0.01,
    cash_out_rate: 0.015,
    bill_payment_rate: 0.005,
    transfer_rate: 0.008,
    minimum_commission: 0.1
  },
  transaction_limits: {
    daily_cash_in_limit: 50000,
    daily_cash_out_limit: 30000,
    single_transaction_limit: 5000,
    monthly_volume_limit: 1000000
  },
  wallet_info: {
    main_wallet_id: new mongoose.Types.ObjectId(),
    commission_wallet_id: new mongoose.Types.ObjectId(),
    available_balance: 10000,
    commission_balance: 500,
    float_balance: 5000,
    currency: 'NGN'
  },
  float_management: {
    minimum_balance: 10000,
    maximum_balance: 500000,
    auto_topup_enabled: false,
    auto_topup_threshold: 50000
  },
  statistics: {
    total_transactions: 0,
    total_volume: 0,
    total_commission_earned: 0,
    cash_in_count: 0,
    cash_out_count: 0
  },
  performance_rating: 4.5,
  commission_rate: 0.02,
  is_active: true,
  created_at: new Date(),
  updated_at: new Date(),
  ...overrides
});

export const createTestTransaction = (overrides = {}) => ({
  transaction_ref: `TXN${Date.now()}`,
  user_id: new mongoose.Types.ObjectId(),
  wallet_id: new mongoose.Types.ObjectId(),
  type: 'transfer',
  status: 'completed',
  amount: 1000,
  fee: 10,
  currency: 'NGN',
  description: 'Test transaction',
  balance_before: 2000,
  balance_after: 990,
  metadata: {
    ip_address: '127.0.0.1',
    user_agent: 'test-agent',
    channel: 'web',
    agentCommission: 0
  },
  processing: {
    initiated_at: new Date(),
    completed_at: new Date(),
    retry_count: 0
  },
  created_at: new Date(),
  updated_at: new Date(),
  ...overrides
});

// Helper functions for tests
export const generateAuthToken = (userId: string) => {

  return `Bearer test-token-${userId}`;
};

export const mockAuthenticatedRequest = (userId: string) => ({
  user: {
    id: userId,
    email: '<EMAIL>',
    role: 'user'
  }
});

beforeAll(async () => {
  await setupTestDb();
}, 30000);

afterAll(async () => {
  await teardownTestDb();
}, 30000);

beforeEach(async () => {
  await clearDatabase();
}, 10000);

afterEach(async () => {

  await clearDatabase();
}, 10000);

export const jestConfig = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  testMatch: ['**/__tests__/**/*.test.ts', '**/?(*.)+(spec|test).ts'],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/types/**',
    '!src/**/*.interface.ts'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html', 'cobertura'],
  testTimeout: 30000
};
