import { FastifyReply } from 'fastify';
import { EventEmitter } from 'events';
import { logger } from '../config/logger';
import { User } from '../models/user.model';
// import { Transaction } from '../models/transaction.model';
import { Wallet } from '../models/wallet.model';
// import { Platform } from '../types';

interface SSEConnection {
  userId: string;
  reply: FastifyReply;
  lastPing: Date;
  subscriptions: Set<string>;
}

interface RealtimeEvent {
  type: string;
  userId: string;
  data: any;
  timestamp: Date;
}

export class RealtimeService extends EventEmitter {
  private static instance: RealtimeService;
  private connections: Map<string, SSEConnection> = new Map();
  private pingInterval?: NodeJS.Timeout;
  private cleanupInterval?: NodeJS.Timeout;

  private constructor() {
    super();
    this.setupEventHandlers();
    this.startPingInterval();
    this.startCleanupInterval();
  }

  static getInstance(): RealtimeService {
    if (!RealtimeService.instance) {
      RealtimeService.instance = new RealtimeService();
    }
    return RealtimeService.instance;
  }

  async createSSEConnection(userId: string, reply: FastifyReply, subscriptions: string[] = []): Promise<void> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        reply.status(401).send({ success: false, message: 'user not found' });
        return;
      }

      reply.raw.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      });

      const connection: SSEConnection = {
        userId,
        reply,
        lastPing: new Date(),
        subscriptions: new Set(subscriptions)
      };

      const connectionId = `${userId}_${Date.now()}`;
      this.connections.set(connectionId, connection);

      this.sendSSEMessage(reply, {
        type: 'connection',
        data: {
          status: 'connected',
          userId,
          subscriptions: Array.from(connection.subscriptions),
          timestamp: new Date().toISOString()
        }
      });

      await this.sendInitialDashboardData(userId, reply);
      reply.raw.on('close', () => {
        this.connections.delete(connectionId);
        logger.info('SSE connection closed', { userId, connectionId });
      });

      logger.info('SSE connection established', { userId, connectionId, subscriptions });

    } catch (error: any) {
      logger.error('Error creating SSE connection:', error);
      reply.status(500).send({ success: false, message: 'failed to establish SSE connection' });
    }
  }

  // send message to specific user
  async sendToUser(userId: string, event: Omit<RealtimeEvent, 'userId' | 'timestamp'>): Promise<void> {
    const userConnections = Array.from(this.connections.values()).filter(conn => conn.userId === userId);
    
    if (userConnections.length === 0) {
      logger.debug('No active connections for user', { userId });
      return;
    }

    const message = {
      type: event.type,
      data: event.data,
      timestamp: new Date().toISOString()
    };

    userConnections.forEach(connection => {
      try {
        this.sendSSEMessage(connection.reply, message);
      } catch (error: any) {
        logger.error('Error sending SSE message to user:', error);
      }
    });

    logger.debug('Sent realtime event to user', { userId, eventType: event.type, connections: userConnections.length });
  }

  // broadcast to all connected users
  async broadcast(event: Omit<RealtimeEvent, 'userId' | 'timestamp'>): Promise<void> {
    const message = {
      type: event.type,
      data: event.data,
      timestamp: new Date().toISOString()
    };

    this.connections.forEach((connection, connectionId) => {
      try {
        this.sendSSEMessage(connection.reply, message);
      } catch (error: any) {
        logger.error('Error broadcasting SSE message:', error);
        this.connections.delete(connectionId);
      }
    });

    logger.info('Broadcasted realtime event', { eventType: event.type, connections: this.connections.size });
  }

  // send balance update
  async sendBalanceUpdate(userId: string, walletData: any): Promise<void> {
    await this.sendToUser(userId, {
      type: 'balance_update',
      data: {
        walletBalance: walletData.mainBalance,
        totalBalance: walletData.totalBalance,
        currency: walletData.currency,
        wallets: walletData.wallets,
        lastUpdated: new Date().toISOString()
      }
    });
  }

  // send transaction notification
  async sendTransactionNotification(userId: string, transaction: any): Promise<void> {
    await this.sendToUser(userId, {
      type: 'transaction_notification',
      data: {
        transactionId: transaction.id,
        type: transaction.type,
        amount: transaction.amount,
        currency: transaction.currency,
        status: transaction.status,
        description: transaction.description,
        timestamp: transaction.createdAt,
        reference: transaction.reference
      }
    });
  }

  // send dashboard stats update
  async sendDashboardUpdate(userId: string, dashboardData: any): Promise<void> {
    await this.sendToUser(userId, {
      type: 'dashboard_update',
      data: {
        transactionStats: dashboardData.transactionSummary?.stats,
        breakdown: dashboardData.transactionSummary?.breakdown,
        recentTransactions: dashboardData.transactionSummary?.recentTransactions?.slice(0, 5),
        walletInfo: dashboardData.walletInfo,
        lastUpdated: new Date().toISOString()
      }
    });
  }

  // send system notification
  async sendSystemNotification(userId: string, notification: any): Promise<void> {
    await this.sendToUser(userId, {
      type: 'system_notification',
      data: {
        id: notification.id,
        title: notification.title,
        message: notification.message,
        category: notification.category,
        priority: notification.priority,
        timestamp: notification.timestamp
      }
    });
  }

  // private helper methods
  private sendSSEMessage(reply: FastifyReply, message: any): void {
    const data = JSON.stringify(message);
    reply.raw.write(`data: ${data}\n\n`);
  }

  private async sendInitialDashboardData(userId: string, reply: FastifyReply): Promise<void> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        logger.error('User not found for dashboard data:', userId);
        return;
      }

      const wallet = await Wallet.findOne({ user_id: userId });
      const balance = wallet?.balance || 0;
      const recentTransactions = await this.getRecentTransactions(userId, 5);
      const notificationsCount = await this.getUnreadNotificationsCount(userId);

      const dashboardData = {
        user: {
          id: user._id.toString(),
          name: `${user.first_name} ${user.last_name}`,
          email: user.email
        },
        wallet: {
          balance: Math.round(balance * 100) / 100,
          currency: 'NGN'
        },
        recentTransactions,
        notifications: {
          unreadCount: notificationsCount
        },
        lastUpdated: new Date()
      };

      this.sendSSEMessage(reply, {
        type: 'initial_dashboard',
        data: dashboardData,
        timestamp: new Date().toISOString()
      });
    } catch (error: any) {
      logger.error('Error sending initial dashboard data:', error);
    }
  }

  private async getRecentTransactions(userId: string, limit: number = 5): Promise<any[]> {
    try {
      const { Transaction } = await import('../models/transaction.model');

      const transactions = await Transaction.find({
        $or: [
          { sender_id: userId },
          { receiver_id: userId }
        ]
      })
      .sort({ created_at: -1 })
      .limit(limit)
      .populate('sender_id', 'first_name last_name')
      .populate('receiver_id', 'first_name last_name')
      .lean();

      return transactions.map((tx: any) => ({
        id: tx._id.toString(),
        type: tx.transaction_type,
        amount: tx.amount,
        currency: tx.currency,
        description: tx.description || `${tx.transaction_type} transaction`,
        status: tx.status,
        direction: tx.sender_id.toString() === userId ? 'outgoing' : 'incoming',
        counterparty: tx.sender_id.toString() === userId
          ? `${tx.receiver_id?.first_name} ${tx.receiver_id?.last_name}`.trim()
          : `${tx.sender_id?.first_name} ${tx.sender_id?.last_name}`.trim(),
        createdAt: tx.created_at
      }));
    } catch (error) {
      logger.error('Error getting recent transactions:', error);
      return [];
    }
  }

  private async getUnreadNotificationsCount(userId: string): Promise<number> {
    try {
      const { Notification } = await import('../models/notification.model');

      const count = await Notification.countDocuments({
        user_id: userId,
        is_read: false,
        deleted_at: null
      });

      return count;
    } catch (error) {
      logger.error('Error getting notifications count:', error);
      return 0;
    }
  }

  private setupEventHandlers(): void {

    this.on('transaction_created', async (transaction: any) => {
      await this.sendTransactionNotification(transaction.user_id, transaction);
      await this.updateUserBalance(transaction.user_id);
    });

    this.on('transaction_completed', async (transaction: any) => {
      await this.sendTransactionNotification(transaction.user_id, transaction);
      await this.updateUserBalance(transaction.user_id);
    });

    this.on('balance_changed', async (data: { userId: string; walletData: any }) => {
      await this.sendBalanceUpdate(data.userId, data.walletData);
    });

    this.on('dashboard_refresh', async (userId: string) => {
      await this.refreshUserDashboard(userId);
    });
  }

  private async updateUserBalance(userId: string): Promise<void> {
    try {
      const wallets = await Wallet.find({ user_id: userId, status: 'active' });
      const mainWallet = wallets.find(w => w.wallet_type === 'main');
      const totalBalance = wallets.reduce((sum, wallet) => sum + wallet.balance, 0);
      
      const walletData = {
        mainBalance: mainWallet?.balance || 0,
        totalBalance,
        currency: mainWallet?.currency || 'USD',
        wallets: wallets.map(wallet => ({
          id: wallet._id.toString(),
          type: wallet.wallet_type,
          balance: wallet.balance,
          currency: wallet.currency
        }))
      };

      await this.sendBalanceUpdate(userId, walletData);
    } catch (error: any) {
      logger.error('Error updating user balance:', error);
    }
  }

  private async refreshUserDashboard(userId: string): Promise<void> {
    try {
      // TODO: Implement dashboard service
      // const { DashboardService } = await import('./dashboard.service');
      // const result = await DashboardService.getEnhancedUserDashboard(userId, 'web');

      // Mock dashboard data for now
      const result = {
        success: true,
        data: {
          balance: 1000,
          recentTransactions: [],
          notifications: []
        }
      };
      
      if (result.success) {
        await this.sendDashboardUpdate(userId, result.data);
      }
    } catch (error: any) {
      logger.error('Error refreshing user dashboard:', error);
    }
  }

  private startPingInterval(): void {
    this.pingInterval = setInterval(() => {
      this.connections.forEach((connection, connectionId) => {
        try {
          this.sendSSEMessage(connection.reply, {
            type: 'ping',
            data: { timestamp: new Date().toISOString() }
          });
          connection.lastPing = new Date();
        } catch (error: any) {
          logger.debug('Ping failed, removing connection:', connectionId);
          this.connections.delete(connectionId);
        }
      });
    }, 30000); // ping every 30 seconds
  }

  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(() => {
      const now = new Date();
      const staleConnections: string[] = [];

      this.connections.forEach((connection, connectionId) => {
        const timeSinceLastPing = now.getTime() - connection.lastPing.getTime();
        if (timeSinceLastPing > 120000) { // 2 minutes
          staleConnections.push(connectionId);
        }
      });

      staleConnections.forEach(connectionId => {
        this.connections.delete(connectionId);
        logger.debug('Removed stale SSE connection:', connectionId);
      });

      if (staleConnections.length > 0) {
        logger.info('Cleaned up stale SSE connections', { count: staleConnections.length });
      }
    }, 60000); 
  }

  destroy(): void {
    if (this.pingInterval) clearInterval(this.pingInterval);
    if (this.cleanupInterval) clearInterval(this.cleanupInterval);
    
    this.connections.forEach((connection) => {
      try {
        connection.reply.raw.end();
      } catch (error) {

      }
    });
    
    this.connections.clear();
    this.removeAllListeners();
  }

  getStats(): any {
    return {
      totalConnections: this.connections.size,
      connectionsByUser: Array.from(this.connections.values()).reduce((acc, conn) => {
        acc[conn.userId] = (acc[conn.userId] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    };
  }
}

export const realtimeService = RealtimeService.getInstance();
