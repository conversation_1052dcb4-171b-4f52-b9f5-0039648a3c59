import { FastifyReply } from 'fastify';
import { AuthenticatedRequest } from '../types';
import { NotificationService } from '../services/notification.service';
import { logger } from '../config/logger';

export const getNotifications = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { category, type, read, limit, skip } = request.query as {
      category?: string;
      type?: string;
      read?: string;
      limit?: string;
      skip?: string;
    };

    const options: any = {
      limit: limit ? parseInt(limit) : 20,
      skip: skip ? parseInt(skip) : 0
    };

    if (category) options.category = category;
    if (type) options.type = type as any;
    if (read !== undefined) options.read = read === 'true';

    const result = await NotificationService.getUserNotifications(request.user.id, options);

    return reply.status(200).send({
      success: true,
      message: 'notifications retrieved successfully',
      data: {
        notifications: result.notifications.map(notification => ({
          id: notification._id.toString(),
          type: notification.type,
          title: notification.title,
          message: notification.message,
          category: notification.category,
          priority: notification.priority,
          read: notification.read,
          clicked: notification.clicked,
          data: notification.data,
          createdAt: notification.created_at,
          readAt: notification.read_at,
          clickedAt: notification.clicked_at
        })),
        pagination: {
          total: result.total,
          unreadCount: result.unreadCount,
          limit: options.limit,
          skip: options.skip,
          hasMore: result.total > (options.skip + options.limit)
        }
      }
    });
  } catch (error: any) {
    logger.error('Error getting notifications:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve notifications'
    });
  }
};

export const getNotificationHistory = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { search } = request.query as { search?: string };

    const result = await NotificationService.getNotificationHistory(request.user.id, search);

    return reply.status(200).send({
      success: true,
      message: 'notification history retrieved successfully',
      data: result
    });
  } catch (error: any) {
    logger.error('Error getting notification history:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve notification history'
    });
  }
};

export const markAsRead = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { notificationId } = request.params as { notificationId: string };

    const success = await NotificationService.markAsRead(notificationId, request.user.id);

    if (!success) {
      return reply.status(404).send({
        success: false,
        message: 'notification not found'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'notification marked as read'
    });
  } catch (error: any) {
    logger.error('Error marking notification as read:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to mark notification as read'
    });
  }
};

export const markAllAsRead = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const success = await NotificationService.markAllAsRead(request.user.id);

    if (!success) {
      return reply.status(500).send({
        success: false,
        message: 'failed to mark notifications as read'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'all notifications marked as read'
    });
  } catch (error: any) {
    logger.error('Error marking all notifications as read:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to mark all notifications as read'
    });
  }
};

export const getUnreadCount = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const result = await NotificationService.getUserNotifications(request.user.id, { limit: 1 });

    return reply.status(200).send({
      success: true,
      message: 'unread count retrieved successfully',
      data: {
        unreadCount: result.unreadCount,
        totalCount: result.total
      }
    });
  } catch (error: any) {
    logger.error('Error getting unread count:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to get unread count'
    });
  }
};

export const createNotification = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const data = request.body as any;

    const notificationData: any = {
      userId: request.user.id,
      type: data.type,
      title: data.title,
      message: data.message,
      category: data.category,
      priority: data.priority,
      data: data.data
    };

    if (data.scheduledFor) notificationData.scheduledFor = new Date(data.scheduledFor);
    if (data.expiresAt) notificationData.expiresAt = new Date(data.expiresAt);

    const notification = await NotificationService.createNotification(notificationData);

    return reply.status(201).send({
      success: true,
      message: 'notification created successfully',
      data: {
        id: notification._id.toString(),
        type: notification.type,
        title: notification.title,
        message: notification.message,
        category: notification.category,
        priority: notification.priority,
        status: notification.status,
        createdAt: notification.created_at
      }
    });
  } catch (error: any) {
    logger.error('Error creating notification:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to create notification'
    });
  }
};

export const deleteNotification = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { notificationId } = request.params as { notificationId: string };

    const result = await NotificationService.deleteNotification(notificationId, request.user.id);

    if (!result) {
      return reply.status(404).send({
        success: false,
        message: 'notification not found'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'notification deleted successfully'
    });
  } catch (error: any) {
    logger.error('Error deleting notification:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to delete notification'
    });
  }
};

export const getNotificationSettings = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const settings = await NotificationService.getNotificationSettings(request.user.id);

    return reply.status(200).send({
      success: true,
      message: 'notification settings retrieved successfully',
      data: settings
    });
  } catch (error: any) {
    logger.error('Error getting notification settings:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to get notification settings'
    });
  }
};

export const updateNotificationSettings = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const settings = request.body as any;

    const result = await NotificationService.updateNotificationSettings(request.user.id, settings);

    return reply.status(200).send({
      success: true,
      message: 'notification settings updated successfully',
      data: result
    });
  } catch (error: any) {
    logger.error('Error updating notification settings:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to update notification settings'
    });
  }
};
