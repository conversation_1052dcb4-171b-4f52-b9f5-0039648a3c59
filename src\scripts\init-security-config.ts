import mongoose from 'mongoose';
import { SystemConfig } from '../models/system-config.model';
import { logger } from '../config/logger';
import { config } from '../config';

interface SecurityConfigItem {
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description: string;
}

const securityConfigs: SecurityConfigItem[] = [
  {
    key: 'securityMaxLoginAttempts',
    value: 5,
    type: 'number',
    description: 'Maximum failed login attempts before account lockout'
  },
  {
    key: 'securityLockoutDuration',
    value: 1800000, // 30 minutes
    type: 'number',
    description: 'Account lockout duration in milliseconds'
  },
  {
    key: 'securityMaxSessionAge',
    value: ********, // 24 hours
    type: 'number',
    description: 'Maximum session age in milliseconds'
  },
  {
    key: 'securitySessionRotationInterval',
    value: 3600000, // 1 hour
    type: 'number',
    description: 'Session token rotation interval in milliseconds'
  },

  // Rate Limiting
  {
    key: 'securityIpRateLimit',
    value: 100,
    type: 'number',
    description: 'Maximum requests per hour per IP address'
  },
  {
    key: 'securityUserRateLimit',
    value: 1000,
    type: 'number',
    description: 'Maximum requests per hour per authenticated user'
  },
  {
    key: 'securityApiRateLimit',
    value: 50,
    type: 'number',
    description: 'Maximum API requests per minute per user'
  },

  // Fraud Detection
  {
    key: 'fraudVelocityThreshold',
    value: 5,
    type: 'number',
    description: 'Maximum transactions per hour before fraud alert'
  },
  {
    key: 'fraudAmountThreshold',
    value: 10000,
    type: 'number',
    description: 'Transaction amount threshold for fraud detection'
  },
  {
    key: 'fraudLocationCheckEnabled',
    value: true,
    type: 'boolean',
    description: 'Enable location-based fraud detection'
  },
  {
    key: 'fraudRiskScoreThreshold',
    value: 70,
    type: 'number',
    description: 'Risk score threshold for blocking transactions'
  },
  {
    key: 'fraudChallengeThreshold',
    value: 40,
    type: 'number',
    description: 'Risk score threshold for additional verification'
  },

  // Activity Pattern Analysis
  {
    key: 'activityPatternThreshold',
    value: 0.1,
    type: 'number',
    description: 'Minimum activity threshold for normal time detection'
  },
  {
    key: 'deviceTrustThreshold',
    value: 3,
    type: 'number',
    description: 'Minimum usage count to trust a device'
  },
  {
    key: 'locationTrustThreshold',
    value: 3,
    type: 'number',
    description: 'Minimum login count to trust a location'
  },

  // Transaction Security
  {
    key: 'maxSingleTransactionAmount',
    value: 50000,
    type: 'number',
    description: 'Maximum single transaction amount'
  },
  {
    key: 'maxSingleTransactionNewUser',
    value: 1000,
    type: 'number',
    description: 'Maximum single transaction amount for new users'
  },
  {
    key: 'maxDailyTransactionAmount',
    value: 100000,
    type: 'number',
    description: 'Maximum daily transaction amount per user'
  },
  {
    key: 'transactionVelocityLimit',
    value: 10,
    type: 'number',
    description: 'Maximum transactions per hour per user'
  },

  // Loan Security Configuration
  {
    key: 'loanDefaultInterestRate',
    value: 0.15,
    type: 'number',
    description: 'Default annual interest rate for loans'
  },
  {
    key: 'loanPersonalInterestRate',
    value: 0.18,
    type: 'number',
    description: 'Annual interest rate for personal loans'
  },
  {
    key: 'loanMicroloanInterestRate',
    value: 0.20,
    type: 'number',
    description: 'Annual interest rate for microloans'
  },
  {
    key: 'loanSalaryAdvanceInterestRate',
    value: 0.10,
    type: 'number',
    description: 'Annual interest rate for salary advance loans'
  },
  {
    key: 'loanBusinessInterestRate',
    value: 0.12,
    type: 'number',
    description: 'Annual interest rate for business loans'
  },
  {
    key: 'loanBnplInterestRate',
    value: 0.25,
    type: 'number',
    description: 'Annual interest rate for BNPL loans'
  },
  {
    key: 'loanExcellentCreditDiscount',
    value: 0.03,
    type: 'number',
    description: 'Interest rate discount for excellent credit (800+)'
  },
  {
    key: 'loanVeryGoodCreditDiscount',
    value: 0.02,
    type: 'number',
    description: 'Interest rate discount for very good credit (750+)'
  },
  {
    key: 'loanGoodCreditDiscount',
    value: 0.01,
    type: 'number',
    description: 'Interest rate discount for good credit (700+)'
  },
  {
    key: 'loanPoorCreditPenalty',
    value: 0.02,
    type: 'number',
    description: 'Interest rate penalty for poor credit (<650)'
  },
  {
    key: 'loanExistingLoanPenalty',
    value: 0.01,
    type: 'number',
    description: 'Interest rate penalty per existing loan'
  },
  {
    key: 'loanMinimumInterestRate',
    value: 0.08,
    type: 'number',
    description: 'Minimum allowed interest rate'
  },

  // Loan Limits
  {
    key: 'loanPersonalMaxAmount',
    value: 5000,
    type: 'number',
    description: 'Maximum personal loan amount'
  },
  {
    key: 'loanPersonalMinAmount',
    value: 500,
    type: 'number',
    description: 'Minimum personal loan amount'
  },
  {
    key: 'loanPersonalMaxTermMonths',
    value: 24,
    type: 'number',
    description: 'Maximum personal loan term in months'
  },
  {
    key: 'loanPersonalMinTermMonths',
    value: 3,
    type: 'number',
    description: 'Minimum personal loan term in months'
  },
  {
    key: 'loanMicroloanMaxAmount',
    value: 1000,
    type: 'number',
    description: 'Maximum microloan amount'
  },
  {
    key: 'loanMicroloanMinAmount',
    value: 100,
    type: 'number',
    description: 'Minimum microloan amount'
  },
  {
    key: 'loanMicroloanMaxTermMonths',
    value: 12,
    type: 'number',
    description: 'Maximum microloan term in months'
  },
  {
    key: 'loanMicroloanMinTermMonths',
    value: 1,
    type: 'number',
    description: 'Minimum microloan term in months'
  },
  {
    key: 'loanMicroloanMinCreditScore',
    value: 600,
    type: 'number',
    description: 'Minimum credit score for microloans'
  },
  {
    key: 'loanSalaryAdvanceMaxAmount',
    value: 2000,
    type: 'number',
    description: 'Maximum salary advance amount'
  },
  {
    key: 'loanSalaryAdvanceMinAmount',
    value: 200,
    type: 'number',
    description: 'Minimum salary advance amount'
  },
  {
    key: 'loanSalaryAdvanceMaxTermMonths',
    value: 6,
    type: 'number',
    description: 'Maximum salary advance term in months'
  },
  {
    key: 'loanSalaryAdvanceMinTermMonths',
    value: 1,
    type: 'number',
    description: 'Minimum salary advance term in months'
  },
  {
    key: 'loanSalaryAdvanceMinCreditScore',
    value: 600,
    type: 'number',
    description: 'Minimum credit score for salary advance'
  },

  // Security Monitoring
  {
    key: 'securityAlertThreshold',
    value: 5,
    type: 'number',
    description: 'Number of security events before alert'
  },
  {
    key: 'securityLogRetentionDays',
    value: 90,
    type: 'number',
    description: 'Security log retention period in days'
  },
  {
    key: 'securityReportingEnabled',
    value: true,
    type: 'boolean',
    description: 'Enable automated security reporting'
  }
];

export async function initializeSecurityConfig(): Promise<void> {
  try {
    logger.info('Initializing security configuration...');

    if (mongoose.connection.readyState === 0) {
      await mongoose.connect(config.database.uri);
    }

    let created = 0;
    let updated = 0;
    let skipped = 0;

    for (const configItem of securityConfigs) {
      try {
        const existingConfig = await SystemConfig.findOne({ 
          config_key: configItem.key 
        });

        if (existingConfig) {

          if (existingConfig.description !== configItem.description) {
            existingConfig.description = configItem.description;
            await existingConfig.save();
            updated++;
            logger.info(`Updated security config: ${configItem.key}`);
          } else {
            skipped++;
          }
        } else {
          const newConfig = new SystemConfig({
            config_key: configItem.key,
            config_value: configItem.value,
            config_type: configItem.type,
            description: configItem.description,
            category: 'security',
            is_active: true,
            created_by: 'system',
            updated_by: 'system'
          });

          await newConfig.save();
          created++;
          logger.info(`Created security config: ${configItem.key} = ${configItem.value}`);
        }
      } catch (error) {
        logger.error(`Error processing config ${configItem.key}:`, error);
      }
    }

    logger.info(`Security configuration initialization complete:`, {
      created,
      updated,
      skipped,
      total: securityConfigs.length
    });

  } catch (error) {
    logger.error('Error initializing security configuration:', error);
    throw error;
  }
}

if (require.main === module) {
  initializeSecurityConfig()
    .then(() => {
      logger.info('Security configuration initialization completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Security configuration initialization failed:', error);
      process.exit(1);
    });
}
