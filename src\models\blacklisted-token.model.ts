import mongoose, { Document, Schema } from 'mongoose';

export interface IBlacklistedToken extends Document {
  _id: mongoose.Types.ObjectId;
  token_hash: string;
  user_id: mongoose.Types.ObjectId;
  expires_at: Date;
  created_at: Date;
}

const blacklistedTokenSchema = new Schema<IBlacklistedToken>({
  token_hash: {
    type: String,
    required: true,
    unique: true
  },
  user_id: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  expires_at: {
    type: Date,
    required: true
  },
  created_at: {
    type: Date,
    default: Date.now
  }
});

// Create indexes
blacklistedTokenSchema.index({ token_hash: 1 }, { unique: true });
blacklistedTokenSchema.index({ user_id: 1 });
blacklistedTokenSchema.index({ expires_at: 1 }, { expireAfterSeconds: 0 });
blacklistedTokenSchema.index({ created_at: 1 });
blacklistedTokenSchema.statics.isBlacklisted = async function(tokenHash: string): Promise<boolean> {
  const blacklistedToken = await this.findOne({
    token_hash: tokenHash,
    expires_at: { $gt: new Date() }
  });
  return !!blacklistedToken;
};

blacklistedTokenSchema.statics.cleanupExpired = async function(): Promise<number> {
  const result = await this.deleteMany({
    expires_at: { $lt: new Date() }
  });
  return result.deletedCount || 0;
};

export const BlacklistedToken = mongoose.model<IBlacklistedToken>('BlacklistedToken', blacklistedTokenSchema);
