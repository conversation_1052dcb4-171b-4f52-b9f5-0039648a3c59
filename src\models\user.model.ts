import mongoose, { Schema, Document } from 'mongoose';
import { UserRole, KycStatus, AccountStatus, RegistrationStep, IdType, VerificationStatus } from '../types';

export interface IUser extends Document {
  _id: mongoose.Types.ObjectId;
  email: string;
  password?: string;
  phone: string;
  username?: string;
  first_name?: string;
  last_name?: string;
  profile_picture?: string;
  bio?: string;
  date_of_birth?: Date;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    postal_code?: string;
  };
  role: UserRole;
  is_verified: boolean;
  kyc_status: KycStatus;
  account_status: AccountStatus;
  wallet_balance: number;

  registration_step: RegistrationStep;
  registration_completed: boolean;

  phone_verified: boolean;
  email_verified: boolean;
  phone_verification_code?: string | null;
  phone_verification_expires?: Date | null;
  email_verification_code?: string | null;
  email_verification_expires?: Date | null;

  identity_verification: {
    id_type?: IdType;
    id_number?: string;
    id_document_front?: string;
    id_document_back?: string;
    selfie_photo?: string;
    verification_status: VerificationStatus;
    verified_at?: Date;
    rejection_reason?: string;
  };

  transaction_pin?: string;
  transaction_pin_set: boolean;

  biometric_enabled: boolean;
  biometric_enrolled_at?: Date;

  agent_info?: {
    commission_rate: number;
    total_transactions: number;
    total_commission_earned: number;
    is_active: boolean;
    business_name?: string;
    business_type?: string;
    business_registration_number?: string;
    business_address?: string;
    business_document?: string;
    tax_certificate?: string;
    verification_status?: string;
    monthly_commission?: number;
    last_commission_date?: Date;
    performance_rating?: number;
    successful_transactions?: number;
  };
  merchant_info?: {
    business_name?: string;
    business_type?: string;
    business_registration?: string;
    tax_id?: string;
  };
  last_login?: Date;
  security: {
    login_attempts: number;
    locked_until?: Date;
    password_reset_token?: string;
    password_reset_expires?: Date;
    email_verification_token?: string;
    email_verification_expires?: Date;
    two_factor_enabled: boolean;
    last_login?: Date;
    last_login_ip?: string;
    last_login_platform?: string;
    pin_hash?: string;
    pin_attempts?: number;
    pin_locked_until?: Date;
  };
  preferences: {
    language: string;
    currency: string;
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
    };
  };
  created_at: Date;
  updated_at: Date;
  deleted_at?: Date;
}

const userSchema = new Schema<IUser>({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  password: {
    type: String,
    required: false,
    minlength: 8
  },
  phone: {
    type: String,
    required: true,
    unique: true
  },
  username: {
    type: String,
    unique: true,
    sparse: true,
    trim: true,
    lowercase: true
  },
  first_name: {
    type: String,
    required: false,
    trim: true
  },
  last_name: {
    type: String,
    required: false,
    trim: true
  },
  profile_picture: {
    type: String,
    default: null
  },
  bio: {
    type: String,
    maxlength: 500,
    default: ''
  },
  date_of_birth: {
    type: Date,
    required: false
  },
  address: {
    street: { type: String, default: '' },
    city: { type: String, default: '' },
    state: { type: String, default: '' },
    country: { type: String, default: '' },
    postal_code: { type: String, default: '' }
  },
  role: {
    type: String,
    enum: Object.values(UserRole),
    default: UserRole.USER
  },
  is_verified: {
    type: Boolean,
    default: false
  },
  kyc_status: {
    type: String,
    enum: Object.values(KycStatus),
    default: KycStatus.PENDING,
    
  },
  account_status: {
    type: String,
    enum: Object.values(AccountStatus),
    default: AccountStatus.ACTIVE,
    
  },
  wallet_balance: {
    type: Number,
    default: 0,
    min: 0
  },

  registration_step: {
    type: String,
    enum: Object.values(RegistrationStep),
    default: RegistrationStep.PHONE_EMAIL,
    
  },
  registration_completed: {
    type: Boolean,
    default: false,
    
  },

  phone_verified: {
    type: Boolean,
    default: false,
    
  },
  email_verified: {
    type: Boolean,
    default: false,
    
  },
  phone_verification_code: {
    type: String,
    default: null
  },
  phone_verification_expires: {
    type: Date,
    default: null
  },
  email_verification_code: {
    type: String,
    default: null
  },
  email_verification_expires: {
    type: Date,
    default: null
  },

  identity_verification: {
    id_type: {
      type: String,
      enum: Object.values(IdType),
      default: null
    },
    id_number: {
      type: String,
      default: null
    },
    id_document_front: {
      type: String,
      default: null
    },
    id_document_back: {
      type: String,
      default: null
    },
    selfie_photo: {
      type: String,
      default: null
    },
    verification_status: {
      type: String,
      enum: Object.values(VerificationStatus),
      default: VerificationStatus.PENDING
    },
    verified_at: {
      type: Date,
      default: null
    },
    rejection_reason: {
      type: String,
      default: null
    }
  },

  transaction_pin: {
    type: String,
    default: null
  },
  transaction_pin_set: {
    type: Boolean,
    default: false,
    
  },

  biometric_enabled: {
    type: Boolean,
    default: false,
    
  },
  biometric_enrolled_at: {
    type: Date,
    default: null
  },
  last_login: {
    type: Date,
    
  },
  agent_info: {
    commission_rate: { type: Number, default: 0.02 },
    total_transactions: { type: Number, default: 0 },
    total_commission_earned: { type: Number, default: 0 },
    is_active: { type: Boolean, default: true },
    business_name: { type: String, trim: true },
    business_type: { type: String, trim: true },
    business_registration_number: { type: String, trim: true },
    business_address: { type: String, trim: true },
    business_document: { type: String },
    tax_certificate: { type: String },
    verification_status: { type: String, default: 'pending' },
    monthly_commission: { type: Number, default: 0 },
    last_commission_date: { type: Date },
    performance_rating: { type: Number, default: 0 },
    successful_transactions: { type: Number, default: 0 }
  },
  merchant_info: {
    business_name: { type: String, default: '' },
    business_type: { type: String, default: '' },
    business_registration: { type: String, default: '' },
    tax_id: { type: String, default: '' }
  },
  security: {
    login_attempts: { type: Number, default: 0 },
    locked_until: { type: Date, default: null },
    password_reset_token: { type: String, default: null },
    password_reset_expires: { type: Date, default: null },
    email_verification_token: { type: String, default: null },
    email_verification_expires: { type: Date, default: null },
    two_factor_enabled: { type: Boolean, default: false },
    last_login: { type: Date, default: null },
    last_login_ip: { type: String, default: null },
    last_login_platform: { type: String, default: null },
    pin_hash: { type: String, default: null },
    pin_attempts: { type: Number, default: 0 },
    pin_locked_until: { type: Date, default: null }
  },
  preferences: {
    language: { type: String, default: 'en' },
    currency: { type: String, default: 'USD' },
    notifications: {
      email: { type: Boolean, default: true },
      sms: { type: Boolean, default: true },
      push: { type: Boolean, default: true }
    }
  },
  created_at: {
    type: Date,
    default: Date.now
  },
  updated_at: {
    type: Date,
    default: Date.now
  },
  deleted_at: {
    type: Date,
    default: null,
    
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  toJSON: {
    transform: function(_doc: any, ret: any) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      delete ret.password;
      return ret;
    }
  }
});

userSchema.index({ email: 1, account_status: 1 });
userSchema.index({ phone: 1, account_status: 1 });
userSchema.index({ role: 1, account_status: 1 });
userSchema.index({ kyc_status: 1, created_at: -1 });
userSchema.index({ 'security.last_login': -1 });
userSchema.index({ created_at: -1 });
userSchema.index({ registration_step: 1, created_at: -1 });
userSchema.index({ phone_verified: 1, email_verified: 1 });
userSchema.index({ 'identity_verification.verification_status': 1 });
userSchema.index({ transaction_pin_set: 1, biometric_enabled: 1 });

userSchema.pre('save', function(next) {
  this.updated_at = new Date();
  next();
});

userSchema.virtual('fullName').get(function() {
  return `${this.first_name} ${this.last_name}`;
});

userSchema.methods.toSafeObject = function() {
  const obj = this.toObject();
  delete obj.password;
  delete obj.security.password_reset_token;
  delete obj.security.email_verification_token;
  return obj;
};

userSchema.methods.isAccountLocked = function() {
  return this.security.locked_until && this.security.locked_until > new Date();
};

userSchema.methods.incrementLoginAttempts = function() {
  if (this.security.login_attempts >= 5) {
    this.security.locked_until = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
  }
  this.security.login_attempts += 1;
  return this.save();
};

userSchema.methods.resetLoginAttempts = function() {
  this.security.login_attempts = 0;
  this.security.locked_until = undefined;
  return this.save();
};

userSchema.methods.updateRegistrationStep = function(step: RegistrationStep) {
  this.registration_step = step;
  if (step === RegistrationStep.COMPLETED) {
    this.registration_completed = true;
  }
  return this.save();
};

userSchema.methods.canProceedToStep = function(step: RegistrationStep): boolean {
  const stepOrder = [
    RegistrationStep.PHONE_EMAIL,
    RegistrationStep.PHONE_VERIFICATION,
    RegistrationStep.EMAIL_VERIFICATION,
    RegistrationStep.PERSONAL_INFO,
    RegistrationStep.IDENTITY_VERIFICATION,
    RegistrationStep.TRANSACTION_PIN,
    RegistrationStep.BIOMETRIC_ENROLLMENT,
    RegistrationStep.COMPLETED
  ];

  const currentIndex = stepOrder.indexOf(this.registration_step);
  const targetIndex = stepOrder.indexOf(step);

  return targetIndex <= currentIndex + 1;
};

userSchema.methods.isRegistrationComplete = function(): boolean {
  return this.registration_completed &&
         this.phone_verified &&
         this.email_verified &&
         this.first_name &&
         this.last_name &&
         this.password &&
         this.identity_verification.verification_status === VerificationStatus.VERIFIED &&
         this.transaction_pin_set;
};

userSchema.methods.getRegistrationProgress = function() {
  const steps = [
    { step: RegistrationStep.PHONE_EMAIL, completed: !!this.phone && !!this.email },
    { step: RegistrationStep.PHONE_VERIFICATION, completed: this.phone_verified },
    { step: RegistrationStep.EMAIL_VERIFICATION, completed: this.email_verified },
    { step: RegistrationStep.PERSONAL_INFO, completed: !!(this.first_name && this.last_name && this.password) },
    { step: RegistrationStep.IDENTITY_VERIFICATION, completed: this.identity_verification.verification_status === VerificationStatus.VERIFIED },
    { step: RegistrationStep.TRANSACTION_PIN, completed: this.transaction_pin_set },
    { step: RegistrationStep.BIOMETRIC_ENROLLMENT, completed: true }, // optional step
    { step: RegistrationStep.COMPLETED, completed: this.registration_completed }
  ];

  const completedSteps = steps.filter(s => s.completed).length;
  const totalSteps = steps.length;

  return {
    current_step: this.registration_step,
    steps,
    progress_percentage: Math.round((completedSteps / totalSteps) * 100),
    completed_steps: completedSteps,
    total_steps: totalSteps
  };
};

export const User = mongoose.model<IUser>('User', userSchema);
