import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { AuthMiddleware } from '../middleware/auth.middleware';
import * as agentController from '../controllers/agent.controller';

export async function agentDashboardRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  fastify.get('/dashboard', { preHandler: AuthMiddleware.authenticateRequest }, agentController.getAgentDashboard as any);
  fastify.get('/performance', { preHandler: AuthMiddleware.authenticateRequest }, agentController.getAgentPerformanceMetrics as any);
  fastify.get('/commission', { preHandler: AuthMiddleware.authenticateRequest }, agentController.getAgentCommissionData as any);
  fastify.get('/transactions/stats', { preHandler: AuthMiddleware.authenticateRequest }, agentController.getAgentTransactionStats as any);
  fastify.get('/transactions/recent', { preHandler: AuthMiddleware.authenticateRequest }, agentController.getAgentRecentTransactions as any);
  fastify.get('/float', { preHandler: AuthMiddleware.authenticateRequest }, agentController.getAgentFloatBalance as any);
  fastify.post('/cash-in', { preHandler: AuthMiddleware.authenticateRequest }, agentController.processCashIn as any);

  fastify.get('/health', async () => {
    return {
      success: true,
      message: 'agent dashboard service running',
      data: {
        status: 'ok',
        features: ['dashboard', 'performance_metrics', 'commission_tracking', 'transaction_processing']
      }
    };
  });
}
