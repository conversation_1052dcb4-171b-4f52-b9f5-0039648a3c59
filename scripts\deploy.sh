#!/bin/bash

# AeTrust Backend Deployment Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-production}
BACKUP_ENABLED=${BACKUP_ENABLED:-true}
HEALTH_CHECK_TIMEOUT=${HEALTH_CHECK_TIMEOUT:-120}
ROLLBACK_ON_FAILURE=${ROLLBACK_ON_FAILURE:-true}

echo -e "${BLUE}🚀 Starting AeTrust Backend Deployment${NC}"
echo -e "${BLUE}Environment: ${ENVIRONMENT}${NC}"
echo -e "${BLUE}Timestamp: $(date)${NC}"

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
    echo -e "${RED}❌ Invalid environment: $ENVIRONMENT${NC}"
    echo -e "${YELLOW}Valid environments: development, staging, production${NC}"
    exit 1
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker is not running${NC}"
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose is not installed${NC}"
    exit 1
fi

# Set compose file based on environment
case $ENVIRONMENT in
    "development")
        COMPOSE_FILE="docker-compose.yml"
        ;;
    "staging")
        COMPOSE_FILE="docker-compose.staging.yml"
        ;;
    "production")
        COMPOSE_FILE="docker-compose.prod.yml"
        ;;
esac

echo -e "${BLUE}📋 Using compose file: $COMPOSE_FILE${NC}"

# Create backup if enabled
if [ "$BACKUP_ENABLED" = true ] && [ "$ENVIRONMENT" != "development" ]; then
    echo -e "${YELLOW}📦 Creating backup before deployment...${NC}"
    ./scripts/backup.sh
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Backup failed${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Backup completed${NC}"
fi

# Pull latest images
echo -e "${YELLOW}📥 Pulling latest Docker images...${NC}"
docker-compose -f $COMPOSE_FILE pull
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to pull Docker images${NC}"
    exit 1
fi

# Stop existing containers gracefully
echo -e "${YELLOW}🛑 Stopping existing containers...${NC}"
docker-compose -f $COMPOSE_FILE down --timeout 30

# Start new containers
echo -e "${YELLOW}🚀 Starting new containers...${NC}"
docker-compose -f $COMPOSE_FILE up -d

# Wait for services to be ready
echo -e "${YELLOW}⏳ Waiting for services to be ready...${NC}"
sleep 10

# Health check function
check_health() {
    local url=$1
    local timeout=$2
    local interval=5
    local elapsed=0

    while [ $elapsed -lt $timeout ]; do
        if curl -f -s "$url" > /dev/null; then
            return 0
        fi
        sleep $interval
        elapsed=$((elapsed + interval))
        echo -e "${YELLOW}⏳ Waiting for health check... (${elapsed}s/${timeout}s)${NC}"
    done
    return 1
}

# Perform health checks
echo -e "${YELLOW}🏥 Performing health checks...${NC}"

# API health check
API_URL="http://localhost:3000/health"
if check_health $API_URL $HEALTH_CHECK_TIMEOUT; then
    echo -e "${GREEN}✅ API health check passed${NC}"
else
    echo -e "${RED}❌ API health check failed${NC}"
    
    if [ "$ROLLBACK_ON_FAILURE" = true ]; then
        echo -e "${YELLOW}🔄 Rolling back deployment...${NC}"
        docker-compose -f $COMPOSE_FILE down
        # Restore from backup if available
        if [ "$BACKUP_ENABLED" = true ]; then
            ./scripts/restore.sh latest
        fi
        exit 1
    fi
fi

# Database health check
echo -e "${YELLOW}🗄️ Checking database connectivity...${NC}"
if docker-compose -f $COMPOSE_FILE exec -T mongodb mongosh --eval "db.adminCommand('ping')" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Database health check passed${NC}"
else
    echo -e "${RED}❌ Database health check failed${NC}"
    exit 1
fi

# Redis health check
echo -e "${YELLOW}🔴 Checking Redis connectivity...${NC}"
if docker-compose -f $COMPOSE_FILE exec -T redis redis-cli ping > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Redis health check passed${NC}"
else
    echo -e "${RED}❌ Redis health check failed${NC}"
    exit 1
fi

# Run database migrations (production only)
if [ "$ENVIRONMENT" = "production" ]; then
    echo -e "${YELLOW}🔄 Running database migrations...${NC}"
    docker-compose -f $COMPOSE_FILE exec -T api npm run db:migrate
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Database migrations failed${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Database migrations completed${NC}"
fi

# Clean up old images and containers
echo -e "${YELLOW}🧹 Cleaning up old Docker resources...${NC}"
docker system prune -f
docker image prune -f

# Display running containers
echo -e "${BLUE}📊 Deployment Status:${NC}"
docker-compose -f $COMPOSE_FILE ps

# Display logs for verification
echo -e "${BLUE}📝 Recent logs:${NC}"
docker-compose -f $COMPOSE_FILE logs --tail=20 api

echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
echo -e "${GREEN}Environment: $ENVIRONMENT${NC}"
echo -e "${GREEN}API URL: http://localhost:3000${NC}"
echo -e "${GREEN}Health Check: http://localhost:3000/health${NC}"

# Send notification (if configured)
if [ ! -z "$SLACK_WEBHOOK_URL" ]; then
    curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"✅ AeTrust Backend deployed successfully to $ENVIRONMENT\"}" \
        $SLACK_WEBHOOK_URL
fi

echo -e "${BLUE}📋 Deployment Summary:${NC}"
echo -e "${BLUE}- Environment: $ENVIRONMENT${NC}"
echo -e "${BLUE}- Compose File: $COMPOSE_FILE${NC}"
echo -e "${BLUE}- Backup Created: $BACKUP_ENABLED${NC}"
echo -e "${BLUE}- Health Checks: Passed${NC}"
echo -e "${BLUE}- Deployment Time: $(date)${NC}"
