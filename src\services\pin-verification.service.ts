import bcrypt from 'bcrypt';
import { User } from '../models/user.model';
import { SystemConfig } from '../models/system-config.model';
import { AuditLogService } from './audit-log.service';
import { logger } from '../config/logger';
import { AuditAction, Platform } from '../types';

export interface PinVerificationResult {
  success: boolean;
  message: string;
  attemptsRemaining?: number;
  isLocked?: boolean;
  lockoutDuration?: number;
}

export class PinVerificationService {
  private static readonly MAX_PIN_ATTEMPTS = 3;
  private static readonly LOCKOUT_DURATION = 30 * 60 * 1000; // 30 minutes
  private static readonly SALT_ROUNDS = 12;

  static async verifyPin(userId: string, pin: string, context?: string): Promise<PinVerificationResult> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        return {
          success: false,
          message: 'User not found'
        };
      }

      // Check if PIN is locked
      if (user.security?.pin_locked_until && new Date() < user.security.pin_locked_until) {
        const remainingTime = Math.ceil((user.security.pin_locked_until.getTime() - Date.now()) / 60000);
        
        await AuditLogService.log({
          userId,
          action: AuditAction.PIN_VERIFICATION_BLOCKED,
          resource: 'pin_verification',
          details: `PIN verification blocked - account locked for ${remainingTime} minutes`,
          ipAddress: '',
          userAgent: '',
          platform: Platform.API,
          success: false
        });

        return {
          success: false,
          message: `PIN is locked. Try again in ${remainingTime} minutes`,
          isLocked: true,
          lockoutDuration: remainingTime
        };
      }

      const maxAttempts = await SystemConfig.getConfig('pinMaxAttempts') || this.MAX_PIN_ATTEMPTS;
      const lockoutDuration = await SystemConfig.getConfig('pinLockoutDuration') || this.LOCKOUT_DURATION;

      if (!user.security?.pin_hash) {
        return {
          success: false,
          message: 'PIN not set. Please set up your PIN first'
        };
      }

      const isValidPin = await bcrypt.compare(pin, user.security.pin_hash);

      if (isValidPin) {

        await User.findByIdAndUpdate(userId, {
          $unset: {
            'security.pin_attempts': 1,
            'security.pin_locked_until': 1,
            'security.last_failed_pin_attempt': 1
          },
          $set: {
            'security.last_successful_pin_verification': new Date()
          }
        });

        await AuditLogService.log({
          userId,
          action: AuditAction.PIN_VERIFICATION_SUCCESS,
          resource: 'pin_verification',
          details: `PIN verified successfully${context ? ` for ${context}` : ''}`,
          ipAddress: '',
          userAgent: '',
          platform: Platform.API,
          success: true
        });

        return {
          success: true,
          message: 'PIN verified successfully'
        };
      } else {

        const currentAttempts = (user.security?.pin_attempts || 0) + 1;
        const attemptsRemaining = maxAttempts - currentAttempts;

        const updateData: any = {
          'security.pin_attempts': currentAttempts,
          'security.last_failed_pin_attempt': new Date()
        };

        // Lock PIN if max attempts reached
        if (currentAttempts >= maxAttempts) {
          updateData['security.pin_locked_until'] = new Date(Date.now() + lockoutDuration);
          updateData['security.pin_attempts'] = 0; // Reset for next cycle
        }

        await User.findByIdAndUpdate(userId, { $set: updateData });

        await AuditLogService.log({
          userId,
          action: AuditAction.PIN_VERIFICATION_FAILED,
          resource: 'pin_verification',
          details: `PIN verification failed. Attempt ${currentAttempts}/${maxAttempts}${context ? ` for ${context}` : ''}`,
          ipAddress: '',
          userAgent: '',
          platform: Platform.API,
          success: false
        });

        if (currentAttempts >= maxAttempts) {
          return {
            success: false,
            message: `Too many failed attempts. PIN locked for ${Math.ceil(lockoutDuration / 60000)} minutes`,
            isLocked: true,
            lockoutDuration: Math.ceil(lockoutDuration / 60000)
          };
        }

        return {
          success: false,
          message: `Invalid PIN. ${attemptsRemaining} attempts remaining`,
          attemptsRemaining
        };
      }
    } catch (error: any) {
      logger.error('PIN verification error:', error);
      
      await AuditLogService.log({
        userId,
        action: AuditAction.PIN_VERIFICATION_ERROR,
        resource: 'pin_verification',
        details: `PIN verification system error: ${error.message}`,
        ipAddress: '',
        userAgent: '',
        platform: Platform.API,
        success: false
      });

      return {
        success: false,
        message: 'PIN verification system error'
      };
    }
  }


  static async setPin(userId: string, newPin: string, currentPin?: string): Promise<PinVerificationResult> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        return {
          success: false,
          message: 'User not found'
        };
      }

      if (!/^\d{4,6}$/.test(newPin)) {
        return {
          success: false,
          message: 'PIN must be 4-6 digits'
        };
      }

      if (user.security?.pin_hash && currentPin) {
        const currentPinVerification = await this.verifyPin(userId, currentPin, 'PIN change');
        if (!currentPinVerification.success) {
          return currentPinVerification;
        }
      }

      const pinHash = await bcrypt.hash(newPin, this.SALT_ROUNDS);

      await User.findByIdAndUpdate(userId, {
        $set: {
          'security.pin_hash': pinHash,
          'security.pin_set_at': new Date()
        },
        $unset: {
          'security.pin_attempts': 1,
          'security.pin_locked_until': 1,
          'security.last_failed_pin_attempt': 1
        }
      });

      await AuditLogService.log({
        userId,
        action: AuditAction.PIN_SET,
        resource: 'pin_management',
        details: user.security?.pin_hash ? 'PIN updated' : 'PIN set for first time',
        ipAddress: '',
        userAgent: '',
        platform: Platform.API,
        success: true
      });

      return {
        success: true,
        message: 'PIN set successfully'
      };
    } catch (error: any) {
      logger.error('PIN setup error:', error);
      return {
        success: false,
        message: 'Failed to set PIN'
      };
    }
  }


  static async hasPinSet(userId: string): Promise<boolean> {
    try {
      const user = await User.findById(userId).select('security.pin_hash');
      return !!(user?.security?.pin_hash);
    } catch (error: any) {
      logger.error('PIN check error:', error);
      return false;
    }
  }

 
  static async resetPin(userId: string, adminId: string): Promise<PinVerificationResult> {
    try {
      await User.findByIdAndUpdate(userId, {
        $unset: {
          'security.pin_hash': 1,
          'security.pin_attempts': 1,
          'security.pin_locked_until': 1,
          'security.last_failed_pin_attempt': 1,
          'security.last_successful_pin_verification': 1,
          'security.pin_set_at': 1
        }
      });

      await AuditLogService.log({
        userId: adminId,
        action: AuditAction.PIN_RESET,
        resource: 'pin_management',
        details: `PIN reset for user ${userId}`,
        ipAddress: '',
        userAgent: '',
        platform: Platform.ADMIN,
        success: true
      });

      return {
        success: true,
        message: 'PIN reset successfully'
      };
    } catch (error: any) {
      logger.error('PIN reset error:', error);
      return {
        success: false,
        message: 'Failed to reset PIN'
      };
    }
  }

 
  static async getPinStatus(userId: string): Promise<{
    hasPinSet: boolean;
    isLocked: boolean;
    attemptsRemaining?: number;
    lockoutDuration?: number;
  }> {
    try {
      const user = await User.findById(userId).select('security');
      
      const hasPinSet = !!(user?.security?.pin_hash);
      const isLocked = !!(user?.security?.pin_locked_until && new Date() < user.security.pin_locked_until);
      
      let attemptsRemaining;
      let lockoutDuration;

      if (isLocked && user?.security?.pin_locked_until) {
        lockoutDuration = Math.ceil((user.security.pin_locked_until.getTime() - Date.now()) / 60000);
      } else if (user?.security?.pin_attempts) {
        const maxAttempts = await SystemConfig.getConfig('pinMaxAttempts') || this.MAX_PIN_ATTEMPTS;
        attemptsRemaining = maxAttempts - user.security.pin_attempts;
      }

      return {
        hasPinSet,
        isLocked,
        ...(attemptsRemaining !== undefined && { attemptsRemaining }),
        ...(lockoutDuration !== undefined && { lockoutDuration })
      };
    } catch (error: any) {
      logger.error('PIN status check error:', error);
      return {
        hasPinSet: false,
        isLocked: false
      };
    }
  }
}
