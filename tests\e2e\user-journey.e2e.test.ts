import { FastifyInstance } from 'fastify';
import { setupTestApp, teardownTestApp, setupTestDb, teardownTestDb, clearDatabase } from '../setup';

describe('User Journey E2E Tests', () => {
  let app: FastifyInstance;

  beforeAll(async () => {
    await setupTestDb();
    app = await setupTestApp();
  });

  afterAll(async () => {
    await teardownTestApp();
    await teardownTestDb();
  });

  beforeEach(async () => {
    await clearDatabase();
  });

  describe('Complete User Registration and Login Flow', () => {
    it('should complete full user registration and login journey', async () => {
      const timestamp = Date.now();
      const userEmail = `e2e-user-${timestamp}@example.com`;
      const userPhone = `+234812345${timestamp.toString().slice(-4)}`;
      const userPassword = 'Test123!@#';

      // Step 1: Initiate registration
      const initiateResponse = await app.inject({
        method: 'POST',
        url: '/registration/initiate',
        payload: {
          email: userEmail,
          phone: userPhone,
          password: userPassword,
          confirmPassword: userPassword,
          acceptTerms: true
        }
      });

      expect(initiateResponse.statusCode).toBe(200);
      const initiateBody = JSON.parse(initiateResponse.body);
      expect(initiateBody.success).toBe(true);
      expect(initiateBody.data).toHaveProperty('userId');
      expect(initiateBody.data).toHaveProperty('nextStep');

      const userId = initiateBody.data.userId;

      // Step 2: Verify phone number
      const verifyPhoneResponse = await app.inject({
        method: 'POST',
        url: '/registration/verify-phone',
        payload: {
          userId,
          verificationCode: '123456' 
        }
      });

      expect(verifyPhoneResponse.statusCode).toBe(200);
      const verifyPhoneBody = JSON.parse(verifyPhoneResponse.body);
      expect(verifyPhoneBody.success).toBe(true);

      // Step 3: Verify email
      const verifyEmailResponse = await app.inject({
        method: 'POST',
        url: '/registration/verify-email',
        payload: {
          userId,
          verificationCode: '123456' 
        }
      });

      expect(verifyEmailResponse.statusCode).toBe(200);
      const verifyEmailBody = JSON.parse(verifyEmailResponse.body);
      expect(verifyEmailBody.success).toBe(true);

      // Step 4: Complete profile
      const completeProfileResponse = await app.inject({
        method: 'POST',
        url: '/registration/complete-profile',
        payload: {
          userId,
          firstName: 'John',
          lastName: 'Doe',
          dateOfBirth: '1990-01-01',
          address: {
            street: '123 Test Street',
            city: 'Lagos',
            state: 'Lagos',
            country: 'Nigeria',
            postalCode: '100001'
          }
        }
      });

      expect(completeProfileResponse.statusCode).toBe(200);
      const completeProfileBody = JSON.parse(completeProfileResponse.body);
      expect(completeProfileBody.success).toBe(true);

      // Step 5: Set transaction PIN
      const setPinResponse = await app.inject({
        method: 'POST',
        url: '/registration/set-transaction-pin',
        payload: {
          userId,
          pin: '1234',
          confirmPin: '1234'
        }
      });

      expect(setPinResponse.statusCode).toBe(200);
      const setPinBody = JSON.parse(setPinResponse.body);
      expect(setPinBody.success).toBe(true);

      // Step 6: Complete registration
      const completeResponse = await app.inject({
        method: 'POST',
        url: '/registration/complete',
        payload: {
          userId
        }
      });

      expect(completeResponse.statusCode).toBe(200);
      const completeBody = JSON.parse(completeResponse.body);
      expect(completeBody.success).toBe(true);
      expect(completeBody.data).toHaveProperty('token');

      // Step 7: Login with new credentials
      const loginResponse = await app.inject({
        method: 'POST',
        url: '/auth/login',
        payload: {
          email: userEmail,
          password: userPassword
        }
      });

      expect(loginResponse.statusCode).toBe(200);
      const loginBody = JSON.parse(loginResponse.body);
      expect(loginBody.success).toBe(true);
      expect(loginBody.data).toHaveProperty('token');
      expect(loginBody.data.user.email).toBe(userEmail);

      // Step 8: Access protected route
      const token = loginBody.data.token;
      const profileResponse = await app.inject({
        method: 'GET',
        url: '/user/me',
        headers: {
          authorization: `Bearer ${token}`
        }
      });

      expect(profileResponse.statusCode).toBe(200);
      const profileBody = JSON.parse(profileResponse.body);
      expect(profileBody.success).toBe(true);
      expect(profileBody.data.email).toBe(userEmail);
      expect(profileBody.data.first_name).toBe('John');
      expect(profileBody.data.last_name).toBe('Doe');

      // Cleanup - delete the test user
      const { User } = await import('../../src/models/user.model');
      await User.deleteOne({ email: userEmail });
    });

    it('should handle registration validation errors', async () => {
      // Test with invalid email
      const invalidEmailResponse = await app.inject({
        method: 'POST',
        url: '/registration/initiate',
        payload: {
          email: 'invalid-email',
          phone: '+2348123456789',
          password: 'Test123!@#',
          confirmPassword: 'Test123!@#',
          acceptTerms: true
        }
      });

      expect(invalidEmailResponse.statusCode).toBe(400);
      const invalidEmailBody = JSON.parse(invalidEmailResponse.body);
      expect(invalidEmailBody.success).toBe(false);

      // Test with weak password
      const weakPasswordResponse = await app.inject({
        method: 'POST',
        url: '/registration/initiate',
        payload: {
          email: '<EMAIL>',
          phone: '+2348123456789',
          password: '123',
          confirmPassword: '123',
          acceptTerms: true
        }
      });

      expect(weakPasswordResponse.statusCode).toBe(400);
      const weakPasswordBody = JSON.parse(weakPasswordResponse.body);
      expect(weakPasswordBody.success).toBe(false);

      // Test with mismatched passwords
      const mismatchedPasswordResponse = await app.inject({
        method: 'POST',
        url: '/registration/initiate',
        payload: {
          email: '<EMAIL>',
          phone: '+2348123456789',
          password: 'Test123!@#',
          confirmPassword: 'Different123!@#',
          acceptTerms: true
        }
      });

      expect(mismatchedPasswordResponse.statusCode).toBe(400);
      const mismatchedPasswordBody = JSON.parse(mismatchedPasswordResponse.body);
      expect(mismatchedPasswordBody.success).toBe(false);
    });
  });

  describe('User Profile Management Journey', () => {
    let authToken: string;
    let userId: string;

    beforeEach(async () => {
      // Create and login a user for profile tests
      const registrationResponse = await app.inject({
        method: 'POST',
        url: '/registration/initiate',
        payload: {
          email: '<EMAIL>',
          phone: '+2348123456789',
          password: 'Test123!@#',
          confirmPassword: 'Test123!@#',
          acceptTerms: true
        }
      });

      const regBody = JSON.parse(registrationResponse.body);
      userId = regBody.data.userId;

      // Complete registration steps
      await app.inject({
        method: 'POST',
        url: '/registration/complete',
        payload: { userId }
      });

      // Login
      const loginResponse = await app.inject({
        method: 'POST',
        url: '/auth/login',
        payload: {
          email: '<EMAIL>',
          password: 'Test123!@#'
        }
      });

      const loginBody = JSON.parse(loginResponse.body);
      authToken = loginBody.data.token;
    });

    afterEach(async () => {
      // Cleanup test user
      if (userId) {
        const { User } = await import('../../src/models/user.model');
        await User.findByIdAndDelete(userId);
      }
    });

    it('should update user profile successfully', async () => {
      const updateResponse = await app.inject({
        method: 'PUT',
        url: '/user/profile',
        headers: {
          authorization: `Bearer ${authToken}`
        },
        payload: {
          firstName: 'Updated',
          lastName: 'Name',
          bio: 'Updated bio description'
        }
      });

      expect(updateResponse.statusCode).toBe(200);
      const updateBody = JSON.parse(updateResponse.body);
      expect(updateBody.success).toBe(true);
      expect(updateBody.data.first_name).toBe('Updated');
      expect(updateBody.data.last_name).toBe('Name');
      expect(updateBody.data.bio).toBe('Updated bio description');
    });

    it('should change password successfully', async () => {
      const changePasswordResponse = await app.inject({
        method: 'PUT',
        url: '/user/password',
        headers: {
          authorization: `Bearer ${authToken}`
        },
        payload: {
          currentPassword: 'Test123!@#',
          newPassword: 'NewPassword123!@#',
          confirmPassword: 'NewPassword123!@#'
        }
      });

      expect(changePasswordResponse.statusCode).toBe(200);
      const changeBody = JSON.parse(changePasswordResponse.body);
      expect(changeBody.success).toBe(true);

      // Verify new password works
      const loginResponse = await app.inject({
        method: 'POST',
        url: '/auth/login',
        payload: {
          email: '<EMAIL>',
          password: 'NewPassword123!@#'
        }
      });

      expect(loginResponse.statusCode).toBe(200);
      const loginBody = JSON.parse(loginResponse.body);
      expect(loginBody.success).toBe(true);
    });
  });
});
