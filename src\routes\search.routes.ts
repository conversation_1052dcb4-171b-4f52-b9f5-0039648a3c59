import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { AuthMiddleware } from '../middleware/auth.middleware';
import * as searchController from '../controllers/search.controller';

export async function searchRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  fastify.get('/users', { preHandler: AuthMiddleware.authenticateRequest }, searchController.searchUsers as any);
  fastify.get('/transactions', { preHandler: AuthMiddleware.authenticateRequest }, searchController.searchTransactions as any);
  fastify.get('/agents', { preHandler: AuthMiddleware.authenticateRequest }, searchController.searchAgents as any);
  fastify.get('/global', { preHandler: AuthMiddleware.authenticateRequest }, searchController.globalSearch as any);
  fastify.get('/suggestions', { preHandler: AuthMiddleware.authenticateRequest }, searchController.getSearchSuggestions as any);

  fastify.get('/health', async () => {
    return {
      success: true,
      message: 'search service running',
      data: { 
        status: 'ok',
        features: ['user_search', 'transaction_search', 'agent_search', 'global_search', 'autocomplete']
      }
    };
  });
}
