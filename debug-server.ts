console.log('Starting debug...');

try {
  console.log('1. Testing basic imports...');
  
  console.log('2. Testing config...');
  const { config } = require('./src/config');
  console.log('Config loaded:', !!config);
  
  console.log('3. Testing Fastify...');
  const Fastify = require('fastify');
  console.log('Fastify loaded:', !!Fastify);
  
  console.log('4. Creating Fastify instance...');
  const app = Fastify({ logger: false });
  console.log('Fastify instance created');
  
  console.log('5. Testing route...');
  app.get('/test', async () => ({ test: 'ok' }));
  console.log('Route added');
  
  console.log('6. Starting server...');
  app.listen({ port: 3001, host: '0.0.0.0' }, (err, address) => {
    if (err) {
      console.error('Server error:', err);
      process.exit(1);
    }
    console.log(`Server listening on ${address}`);
  });
  
} catch (error) {
  console.error('Error:', error);
  process.exit(1);
}
