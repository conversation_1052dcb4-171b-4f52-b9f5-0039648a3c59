import { User } from '../models/user.model';
import { Transaction } from '../models/transaction.model';
import { Agent } from '../models/agent.model';
import { WalletService } from './wallet.service';
import { ExternalApiService } from './external-api.service';
import { logger } from '../config/logger';
import { TransactionType, TransactionStatus, Currency, Platform } from '../types';
import { CryptoUtils } from '../utils/crypto';
import mongoose from 'mongoose';

export enum TopUpMethod {
  BANK_TRANSFER = 'bank_transfer',
  CARD_PAYMENT = 'card_payment',
  AGENT_DEPOSIT = 'agent_deposit',
  MOBILE_MONEY = 'mobile_money',
  CRYPTO = 'crypto'
}

export enum TopUpStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export class TopUpService {
  static async initiateBankTransfer(data: {
    userId: string;
    amount: number;
    currency: Currency;
    bankCode: string;
    platform: Platform;
    ipAddress?: string;
  }): Promise<{ success: boolean; message: string; data?: any }> {
    try {
      const user = await User.findById(data.userId);
      if (!user) {
        return { success: false, message: 'user not found' };
      }

      const wallet = await WalletService.getDefaultWallet(data.userId, data.currency);
      if (!wallet) {
        return { success: false, message: 'wallet not found' };
      }

      // generate virtual account for bank transfer
      const virtualAccount = await this.generateVirtualAccount(data.userId, data.bankCode);
      
      const transactionRef = CryptoUtils.generateTransactionRef('BT');
      
      // create pending transaction
      const transaction = new Transaction({
        transaction_ref: transactionRef,
        user_id: data.userId,
        wallet_id: wallet._id,
        type: TransactionType.DEPOSIT,
        status: TransactionStatus.PENDING,
        amount: data.amount,
        currency: data.currency,
        description: `bank transfer top-up via ${data.bankCode}`,
        metadata: {
          method: TopUpMethod.BANK_TRANSFER,
          bank_code: data.bankCode,
          virtual_account: virtualAccount,
          platform: data.platform,
          ip_address: data.ipAddress
        }
      });

      await transaction.save();

      logger.info('Bank transfer initiated', {
        userId: data.userId,
        transactionRef,
        amount: data.amount,
        bankCode: data.bankCode
      });

      return {
        success: true,
        message: 'bank transfer initiated successfully',
        data: {
          transaction_ref: transactionRef,
          virtual_account: virtualAccount,
          amount: data.amount,
          currency: data.currency,
          bank_name: this.getBankName(data.bankCode),
          expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
        }
      };
    } catch (error: any) {
      logger.error('Error initiating bank transfer:', error);
      return { success: false, message: 'failed to initiate bank transfer' };
    }
  }

  static async initiateCardPayment(data: {
    userId: string;
    amount: number;
    currency: Currency;
    cardDetails: {
      cardNumber: string;
      expiryMonth: string;
      expiryYear: string;
      cvv: string;
      cardHolderName: string;
    };
    platform: Platform;
    ipAddress?: string;
  }): Promise<{ success: boolean; message: string; data?: any }> {
    try {
      const user = await User.findById(data.userId);
      if (!user) {
        return { success: false, message: 'user not found' };
      }

      const wallet = await WalletService.getDefaultWallet(data.userId, data.currency);
      if (!wallet) {
        return { success: false, message: 'wallet not found' };
      }

      const transactionRef = CryptoUtils.generateTransactionRef('CP');

      // process card payment via external gateway
      const paymentResult = await ExternalApiService.processPayment('card', {
        amount: data.amount,
        currency: data.currency,
        card: data.cardDetails,
        reference: transactionRef,
        customer: {
          email: user.email,
          name: `${user.first_name} ${user.last_name}`
        }
      });

      if (!paymentResult.success) {
        return { success: false, message: paymentResult.message };
      }

      // create transaction
      const transaction = new Transaction({
        transaction_ref: transactionRef,
        user_id: data.userId,
        wallet_id: wallet._id,
        type: TransactionType.DEPOSIT,
        status: TransactionStatus.PROCESSING,
        amount: data.amount,
        currency: data.currency,
        description: 'card payment top-up',
        metadata: {
          method: TopUpMethod.CARD_PAYMENT,
          payment_gateway_ref: paymentResult.gatewayRef,
          masked_card: `****-****-****-${data.cardDetails.cardNumber.slice(-4)}`,
          platform: data.platform,
          ip_address: data.ipAddress
        }
      });

      await transaction.save();

      // if payment successful, credit wallet
      if (paymentResult.status === 'success') {
        await this.completeTopUp(transactionRef, paymentResult.gatewayRef);
      }

      logger.info('Card payment initiated', {
        userId: data.userId,
        transactionRef,
        amount: data.amount
      });

      return {
        success: true,
        message: 'card payment processed successfully',
        data: {
          transaction_ref: transactionRef,
          status: paymentResult.status,
          amount: data.amount,
          currency: data.currency
        }
      };
    } catch (error: any) {
      logger.error('Error processing card payment:', error);
      return { success: false, message: 'failed to process card payment' };
    }
  }

  static async initiateAgentDeposit(data: {
    userId: string;
    agentId: string;
    amount: number;
    currency: Currency;
    platform: Platform;
    ipAddress?: string;
  }): Promise<{ success: boolean; message: string; data?: any }> {
    try {
      const user = await User.findById(data.userId);
      if (!user) {
        return { success: false, message: 'user not found' };
      }

      const agent = await Agent.findById(data.agentId);
      if (!agent || agent.agent_status !== 'active') {
        return { success: false, message: 'agent not available' };
      }

      const wallet = await WalletService.getDefaultWallet(data.userId, data.currency);
      if (!wallet) {
        return { success: false, message: 'wallet not found' };
      }

      const transactionRef = CryptoUtils.generateTransactionRef('AD');

      // create pending transaction
      const transaction = new Transaction({
        transaction_ref: transactionRef,
        user_id: data.userId,
        wallet_id: wallet._id,
        type: TransactionType.DEPOSIT,
        status: TransactionStatus.PENDING,
        amount: data.amount,
        currency: data.currency,
        description: `agent deposit via ${agent.business_name}`,
        metadata: {
          method: TopUpMethod.AGENT_DEPOSIT,
          agent_id: data.agentId,
          agent_name: agent.business_name,
          agent_location: agent.location,
          platform: data.platform,
          ip_address: data.ipAddress
        }
      });

      await transaction.save();

      logger.info('Agent deposit initiated', {
        userId: data.userId,
        agentId: data.agentId,
        transactionRef,
        amount: data.amount
      });

      return {
        success: true,
        message: 'agent deposit initiated successfully',
        data: {
          transaction_ref: transactionRef,
          agent: {
            name: agent.business_name,
            location: agent.location,
            phone: agent.contact_info.phone
          },
          amount: data.amount,
          currency: data.currency,
          expires_at: new Date(Date.now() + 2 * 60 * 60 * 1000) // 2 hours
        }
      };
    } catch (error: any) {
      logger.error('Error initiating agent deposit:', error);
      return { success: false, message: 'failed to initiate agent deposit' };
    }
  }

  static async findNearestAgents(data: {
    latitude: number;
    longitude: number;
    radius?: number;
    limit?: number;
  }): Promise<any[]> {
    try {
      const radius = data.radius || 5000; // 5km default
      const limit = data.limit || 10;

      const agents = await Agent.find({
        status: 'active',
        'location.coordinates': {
          $near: {
            $geometry: {
              type: 'Point',
              coordinates: [data.longitude, data.latitude]
            },
            $maxDistance: radius
          }
        }
      })
      .limit(limit)
      .select('business_name contact_info location services operating_hours')
      .lean();

      return agents.map(agent => ({
        id: agent._id,
        name: agent.business_name,
        phone: agent.contact_info.phone,
        address: agent.location.address,
        coordinates: agent.location.coordinates,
        services: agent.business_type,
        operating_hours: '24/7',
        distance: this.calculateDistance(
          data.latitude,
          data.longitude,
          agent.location.coordinates?.latitude || 0,
          agent.location.coordinates?.longitude || 0
        )
      }));
    } catch (error: any) {
      logger.error('Error finding nearest agents:', error);
      return [];
    }
  }

  static async completeTopUp(transactionRef: string, externalRef?: string): Promise<boolean> {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();

      const transaction = await Transaction.findOne({ transaction_ref: transactionRef }).session(session);
      if (!transaction || transaction.status !== TransactionStatus.PENDING) {
        throw new Error('transaction not found or already processed');
      }

      // credit wallet
      await WalletService.creditWallet({
        walletId: transaction.wallet_id.toString(),
        amount: transaction.amount,
        description: transaction.description || 'Agent deposit',
        transactionType: TransactionType.DEPOSIT,
        ...(externalRef && { externalRef })
      });

      // update transaction status
      await Transaction.findByIdAndUpdate(
        transaction._id,
        {
          $set: {
            status: TransactionStatus.COMPLETED,
            'metadata.external_ref': externalRef,
            completed_at: new Date()
          }
        },
        { session }
      );

      await session.commitTransaction();

      logger.info('Top-up completed successfully', {
        transactionRef,
        amount: transaction.amount,
        userId: transaction.user_id
      });

      return true;
    } catch (error: any) {
      await session.abortTransaction();
      logger.error('Error completing top-up:', error);
      return false;
    } finally {
      session.endSession();
    }
  }

  private static async generateVirtualAccount(userId: string, bankCode: string): Promise<any> {
    // generate virtual account number
    const accountNumber = `${bankCode}${userId.slice(-6)}${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;
    
    return {
      account_number: accountNumber,
      account_name: 'AETRUST VIRTUAL ACCOUNT',
      bank_code: bankCode,
      bank_name: this.getBankName(bankCode)
    };
  }

  private static getBankName(bankCode: string): string {
    const banks: { [key: string]: string } = {
      '044': 'Access Bank',
      '014': 'Afribank',
      '023': 'Citibank',
      '050': 'Ecobank',
      '011': 'First Bank',
      '214': 'First City Monument Bank',
      '070': 'Fidelity Bank',
      '058': 'Guaranty Trust Bank',
      '030': 'Heritage Bank',
      '082': 'Keystone Bank',
      '076': 'Polaris Bank',
      '221': 'Stanbic IBTC Bank',
      '068': 'Standard Chartered',
      '232': 'Sterling Bank',
      '032': 'Union Bank',
      '033': 'United Bank for Africa',
      '215': 'Unity Bank',
      '035': 'Wema Bank',
      '057': 'Zenith Bank'
    };
    
    return banks[bankCode] || 'Unknown Bank';
  }

  private static calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // earth radius in km
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lon2 - lon1);
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  private static deg2rad(deg: number): number {
    return deg * (Math.PI/180);
  }
}
