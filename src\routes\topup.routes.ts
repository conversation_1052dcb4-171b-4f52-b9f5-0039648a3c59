// import { FastifyInstance, FastifyPluginOptions } from 'fastify';
// import * as topupController from '../controllers/topup.controller';
// import { AuthMiddleware } from '../middleware/auth.middleware';

// export async function topupRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
//   fastify.post('/bank-transfer', { preHandler: AuthMiddleware.authenticateRequest }, topupController.initiateBankTransfer as any);
//   fastify.post('/card-payment', { preHandler: AuthMiddleware.authenticateRequest }, topupController.initiateCardPayment as any);
//   fastify.post('/agent-deposit', { preHandler: AuthMiddleware.authenticateRequest }, topupController.initiateAgentDeposit as any);
//   fastify.get('/agents/nearest', { preHandler: AuthMiddleware.authenticateRequest }, topupController.findNearestAgents as any);
//   fastify.get('/history', { preHandler: AuthMiddleware.authenticateRequest }, topupController.getTopUpHistory as any);
//   fastify.get('/methods', topupController.getTopUpMethods as any);
//   fastify.get('/banks', topupController.getSupportedBanks as any);

//   fastify.get('/health', async () => {
//     return {
//       success: true,
//       message: 'top-up service is running',
//       data: { status: 'ok' }
//     };
//   });
// }
