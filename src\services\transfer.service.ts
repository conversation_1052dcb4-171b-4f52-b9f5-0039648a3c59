import { WalletService } from './wallet.service';
import { UserService } from './user.service';
import { PinVerificationService } from './pin-verification.service';
import { Transaction } from '../models/transaction.model';
import { SystemConfig } from '../models/system-config.model';
import { TransactionType, TransactionStatus, Currency } from '../types';
import { CryptoUtils } from '../utils/crypto';
import { logger } from '../config/logger';
import axios from 'axios';
import mongoose from 'mongoose';

export class TransferService {
  static async sendMoney(data: {
    senderId: string;
    recipientId?: string;
    recipientEmail?: string;
    recipientPhone?: string;
    amount: number;
    currency: Currency;
    description?: string;
    pin: string;
    metadata?: any;
  }): Promise<{
    success: boolean;
    transferId: string;
    transactionRef: string;
    amount: number;
    fee: number;
    totalAmount: number;
    recipient: any;
    estimatedDelivery: string;
    status: string;
  }> {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();

      const sender = await UserService.getUserById(data.senderId);
      if (!sender) {
        throw new Error('sender not found');
      }

      let recipient;
      if (data.recipientId) {
        recipient = await UserService.getUserById(data.recipientId);
      } else if (data.recipientEmail) {
        recipient = await UserService.getUserByEmail(data.recipientEmail);
      } else if (data.recipientPhone) {
        recipient = await UserService.getUserByPhone(data.recipientPhone);
      }

      if (!recipient) {
        throw new Error('recipient not found');
      }

      const senderWallet = await WalletService.getDefaultWallet(data.senderId, data.currency);
      if (!senderWallet) {
        throw new Error('sender wallet not found');
      }

      const recipientWallet = await WalletService.getDefaultWallet(recipient._id.toString(), data.currency);
      if (!recipientWallet) {
        throw new Error('recipient wallet not found');
      }

      const fee = await this.calculateTransferFee(data.amount, data.currency);
      const totalAmount = data.amount + fee;

      if (senderWallet.available_balance < totalAmount) {
        throw new Error('insufficient balance');
      }

      // Verify PIN using proper PIN verification service
      const pinVerification = await PinVerificationService.verifyPin(
        data.senderId,
        data.pin,
        'money_transfer'
      );

      if (!pinVerification.success) {
        throw new Error(pinVerification.message);
      }
      
      // perform transfer
      const { fromTransaction } = await WalletService.transferBetweenWallets({
        fromWalletId: senderWallet._id.toString(),
        toWalletId: recipientWallet._id.toString(),
        amount: data.amount,
        description: data.description || 'P2P transfer',
        metadata: {
          ...data.metadata,
          fee,
          transferType: 'p2p',
          senderName: `${sender.first_name} ${sender.last_name}`,
          recipientName: `${recipient.first_name} ${recipient.last_name}`
        }
      });

      // deduct fee from sender
      if (fee > 0) {
        await WalletService.debitWallet({
          walletId: senderWallet._id.toString(),
          amount: fee,
          description: 'Transfer fee',
          transactionType: TransactionType.FEE,
          metadata: { relatedTransfer: fromTransaction._id }
        });
      }

      await session.commitTransaction();

      logger.info('p2p transfer completed', {
        senderId: data.senderId,
        recipientId: recipient._id,
        amount: data.amount,
        fee,
        transactionRef: fromTransaction.transaction_ref
      });

      return {
        success: true,
        transferId: fromTransaction._id.toString(),
        transactionRef: fromTransaction.transaction_ref,
        amount: data.amount,
        fee,
        totalAmount,
        recipient: {
          id: recipient._id.toString(),
          name: `${recipient.first_name} ${recipient.last_name}`,
          email: recipient.email,
          phone: recipient.phone
        },
        estimatedDelivery: 'instant',
        status: 'completed'
      };
    } catch (error: any) {
      await session.abortTransaction();
      logger.error('p2p transfer failed:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async businessTransfer(data: {
    senderId: string;
    businessId: string;
    amount: number;
    currency: Currency;
    pin: string;
    description?: string;
    accountNumber?: string;
    metadata?: any;
  }): Promise<{
    success: boolean;
    transferId: string;
    transactionRef: string;
    amount: number;
    fee: number;
    totalAmount: number;
    business: any;
    status: string;
    estimatedDelivery: string;
  }> {
    const session = await mongoose.startSession();

    try {
      session.startTransaction();

      const sender = await UserService.getUserById(data.senderId);
      if (!sender) {
        throw new Error('sender not found');
      }

      const { Merchant } = await import('../models/merchant.model');
      const business = await Merchant.findById(data.businessId);
      if (!business) {
        throw new Error('business not found');
      }

      const senderWallet = await WalletService.getDefaultWallet(data.senderId, data.currency);
      if (!senderWallet) {
        throw new Error('sender wallet not found');
      }

      const businessWallet = await WalletService.getDefaultWallet(business.user_id.toString(), data.currency);
      if (!businessWallet) {
        throw new Error('business wallet not found');
      }

      const fee = await this.calculateTransferFee(data.amount, data.currency);
      const totalAmount = data.amount + fee;

      // check balance
      if (senderWallet.available_balance < totalAmount) {
        throw new Error('insufficient balance');
      }

      // verify PIN
      const { TransactionPinService } = await import('./transaction-pin.service');
      const pinValidation = await TransactionPinService.validatePin(
        data.senderId,
        data.pin,
        data.metadata?.platform || 'web',
        data.metadata?.ipAddress
      );

      if (!pinValidation.isValid) {
        throw new Error(pinValidation.message);
      }

      // perform transfer
      const { fromTransaction } = await WalletService.transferBetweenWallets({
        fromWalletId: senderWallet._id.toString(),
        toWalletId: businessWallet._id.toString(),
        amount: data.amount,
        description: data.description || `Payment to ${business.business_name}`,
        metadata: {
          ...data.metadata,
          fee,
          transferType: 'business',
          senderName: `${sender.first_name} ${sender.last_name}`,
          businessName: business.business_name,
          businessId: data.businessId,
          accountNumber: data.accountNumber
        }
      });

      await session.commitTransaction();

      logger.info('business transfer completed', {
        senderId: data.senderId,
        businessId: data.businessId,
        amount: data.amount,
        transactionRef: fromTransaction.transaction_ref
      });

      return {
        success: true,
        transferId: fromTransaction._id.toString(),
        transactionRef: fromTransaction.transaction_ref,
        amount: data.amount,
        fee,
        totalAmount,
        business: {
          id: business._id.toString(),
          name: business.business_name,
          type: business.business_type,
          accountNumber: data.accountNumber
        },
        status: 'completed',
        estimatedDelivery: 'instant'
      };
    } catch (error: any) {
      await session.abortTransaction();
      logger.error('business transfer failed:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async sendRemittance(data: {
    senderId: string;
    recipientCountry: string;
    recipientName: string;
    recipientPhone: string;
    amount: number;
    sourceCurrency: Currency;
    targetCurrency: Currency;
    deliveryMethod: 'bank' | 'mobile_money' | 'cash_pickup';
    bankDetails?: any;
    description?: string;
    metadata?: any;
  }): Promise<{
    success: boolean;
    remittanceId: string;
    referenceNumber: string;
    sourceAmount: number;
    targetAmount: number;
    exchangeRate: number;
    fee: number;
    totalCost: number;
    recipient: any;
    estimatedDelivery: string;
    trackingCode: string;
  }> {
    try {
      const sender = await UserService.getUserById(data.senderId);
      if (!sender) {
        throw new Error('sender not found');
      }

      const senderWallet = await WalletService.getDefaultWallet(data.senderId, data.sourceCurrency);
      if (!senderWallet) {
        throw new Error('sender wallet not found');
      }

      const exchangeRate = await this.getExchangeRate(data.sourceCurrency, data.targetCurrency);
      const targetAmount = data.amount * exchangeRate;
      
      const fee = await this.calculateRemittanceFee(data.amount, data.sourceCurrency, data.targetCurrency);
      const totalCost = data.amount + fee;

      if (senderWallet.available_balance < totalCost) {
        throw new Error('insufficient balance');
      }

      const transaction = new Transaction({
        transaction_ref: CryptoUtils.generateTransactionRef('REM'),
        user_id: data.senderId,
        wallet_id: senderWallet._id,
        type: TransactionType.TRANSFER,
        status: TransactionStatus.PROCESSING,
        amount: data.amount,
        fee,
        currency: data.sourceCurrency,
        description: data.description || 'International remittance',
        balance_before: senderWallet.balance,
        balance_after: senderWallet.balance - totalCost,
        metadata: {
          ...data.metadata,
          remittanceType: 'international',
          recipientCountry: data.recipientCountry,
          recipientName: data.recipientName,
          recipientPhone: data.recipientPhone,
          targetCurrency: data.targetCurrency,
          targetAmount,
          exchangeRate,
          deliveryMethod: data.deliveryMethod,
          bankDetails: data.bankDetails
        }
      });

      await transaction.save();

      await WalletService.debitWallet({
        walletId: senderWallet._id.toString(),
        amount: totalCost,
        description: 'International remittance',
        transactionType: TransactionType.TRANSFER,
        externalRef: transaction.transaction_ref,
        metadata: transaction.metadata
      });

      const trackingCode = CryptoUtils.generateTransactionRef('TRACK');

      logger.info('remittance initiated', {
        senderId: data.senderId,
        amount: data.amount,
        targetAmount,
        recipientCountry: data.recipientCountry,
        transactionRef: transaction.transaction_ref
      });

      return {
        success: true,
        remittanceId: transaction._id.toString(),
        referenceNumber: transaction.transaction_ref,
        sourceAmount: data.amount,
        targetAmount,
        exchangeRate,
        fee,
        totalCost,
        recipient: {
          name: data.recipientName,
          phone: data.recipientPhone,
          country: data.recipientCountry,
          deliveryMethod: data.deliveryMethod
        },
        estimatedDelivery: this.getEstimatedDelivery(data.recipientCountry, data.deliveryMethod),
        trackingCode
      };
    } catch (error: any) {
      logger.error('remittance failed:', error);
      throw error;
    }
  }

  static async payBill(data: {
    userId: string;
    billType: 'electricity' | 'water' | 'internet' | 'mobile' | 'tv' | 'insurance';
    provider: string;
    accountNumber: string;
    amount: number;
    currency: Currency;
    metadata?: any;
  }): Promise<{
    success: boolean;
    paymentId: string;
    referenceNumber: string;
    amount: number;
    fee: number;
    totalAmount: number;
    provider: string;
    accountNumber: string;
    status: string;
    confirmationCode?: string;
  }> {
    try {
      const user = await UserService.getUserById(data.userId);
      if (!user) {
        throw new Error('user not found');
      }

      const wallet = await WalletService.getDefaultWallet(data.userId, data.currency);
      if (!wallet) {
        throw new Error('wallet not found');
      }

      const fee = await this.calculateBillPaymentFee(data.amount, data.billType);
      const totalAmount = data.amount + fee;

      if (wallet.available_balance < totalAmount) {
        throw new Error('insufficient balance');
      }

      const transaction = new Transaction({
        transaction_ref: CryptoUtils.generateTransactionRef('BILL'),
        user_id: data.userId,
        wallet_id: wallet._id,
        type: TransactionType.PAYMENT,
        status: TransactionStatus.PROCESSING,
        amount: data.amount,
        fee,
        currency: data.currency,
        description: `${data.billType} bill payment - ${data.provider}`,
        balance_before: wallet.balance,
        balance_after: wallet.balance - totalAmount,
        external_provider: data.provider,
        external_reference: data.accountNumber,
        metadata: {
          ...data.metadata,
          billType: data.billType,
          provider: data.provider,
          accountNumber: data.accountNumber
        }
      });

      await transaction.save();

      // debit wallet
      await WalletService.debitWallet({
        walletId: wallet._id.toString(),
        amount: totalAmount,
        description: `Bill payment - ${data.provider}`,
        transactionType: TransactionType.PAYMENT,
        externalRef: transaction.transaction_ref,
        metadata: transaction.metadata
      });

      let confirmationCode: string;
      let providerResponse: any;

      try {
        // simulation for now
        switch (data.provider.toLowerCase()) {
          case 'electricity':
            providerResponse = await this.processElectricityPayment(data);
            break;
          case 'water':
            providerResponse = await this.processWaterPayment(data);
            break;
          case 'internet':
            providerResponse = await this.processInternetPayment(data);
            break;
          case 'mobile':
            providerResponse = await this.processMobilePayment(data);
            break;
          default:
            providerResponse = await this.processGenericBillPayment(data);
        }

        confirmationCode = providerResponse.confirmationCode;

        transaction.status = TransactionStatus.COMPLETED;
        transaction.metadata = {
          ...transaction.metadata,
          exchange_rate: 1.0,
          original_amount: transaction.amount,
          original_currency: transaction.currency
        };

        transaction.processing = {
          ...transaction.processing,
          completed_at: new Date()
        };

      } catch (providerError: any) {
        transaction.status = TransactionStatus.FAILED;
        transaction.metadata = {
          ...transaction.metadata,
          exchange_rate: 1.0,
          original_amount: transaction.amount,
          original_currency: transaction.currency
        };

        transaction.processing = {
          ...transaction.processing,
          failed_at: new Date(),
          failure_reason: providerError.message
        };

        confirmationCode = 'FAILED';

        logger.error('Bill payment provider error:', {
          error: providerError.message,
          provider: data.provider,
          transactionRef: transaction.transaction_ref
        });
      }

      await transaction.save();

      logger.info('bill payment completed', {
        userId: data.userId,
        billType: data.billType,
        provider: data.provider,
        amount: data.amount,
        transactionRef: transaction.transaction_ref
      });

      return {
        success: true,
        paymentId: transaction._id.toString(),
        referenceNumber: transaction.transaction_ref,
        amount: data.amount,
        fee,
        totalAmount,
        provider: data.provider,
        accountNumber: data.accountNumber,
        status: 'completed',
        confirmationCode
      };
    } catch (error: any) {
      logger.error('bill payment failed:', error);
      throw error;
    }
  }

  private static async calculateTransferFee(amount: number, currency: Currency): Promise<number> {
    const percentage = await SystemConfig.getConfig('transferFeePercentage') || 0.01;
    const minFeeUSD = await SystemConfig.getConfig('transferMinFeeUSD') || 0.5;
    const minFeeOther = await SystemConfig.getConfig('transferMinFeeOther') || 100;
    const minFee = currency === Currency.USD ? minFeeUSD : minFeeOther;
    return Math.max(amount * percentage, minFee);
  }

  private static async calculateRemittanceFee(amount: number, sourceCurrency: Currency, targetCurrency: Currency): Promise<number> {
    const percentage = await SystemConfig.getConfig('remittanceFeePercentage') || 0.025;
    const minFeeUSD = await SystemConfig.getConfig('remittanceMinFeeUSD') || 2;
    const minFeeOther = await SystemConfig.getConfig('remittanceMinFeeOther') || 500;
    const crossBorderFeeAmount = await SystemConfig.getConfig('remittanceCrossBorderFee') || 1;

    const minFee = sourceCurrency === Currency.USD ? minFeeUSD : minFeeOther;
    const crossBorderFee = sourceCurrency !== targetCurrency ? crossBorderFeeAmount : 0;
    return Math.max(amount * percentage, minFee) + crossBorderFee;
  }

  private static async calculateBillPaymentFee(amount: number, billType: string): Promise<number> {
    const electricityFee = await SystemConfig.getConfig('billPaymentElectricityFee') || 0.005;
    const waterFee = await SystemConfig.getConfig('billPaymentWaterFee') || 0.005;
    const internetFee = await SystemConfig.getConfig('billPaymentInternetFee') || 0.01;
    const mobileFee = await SystemConfig.getConfig('billPaymentMobileFee') || 0.02;
    const tvFee = await SystemConfig.getConfig('billPaymentTvFee') || 0.01;
    const insuranceFee = await SystemConfig.getConfig('billPaymentInsuranceFee') || 0.015;
    const defaultFee = await SystemConfig.getConfig('billPaymentDefaultFee') || 0.01;
    const minFee = await SystemConfig.getConfig('billPaymentMinFee') || 0.25;

    const feeMap: Record<string, number> = {
      electricity: electricityFee,
      water: waterFee,
      internet: internetFee,
      mobile: mobileFee,
      tv: tvFee,
      insurance: insuranceFee
    };

    const percentage = feeMap[billType] || defaultFee;
    return Math.max(amount * percentage, minFee);
  }

  private static async getExchangeRate(from: Currency, to: Currency): Promise<number> {
    if (from === to) {
      return 1;
    }

    try {

      const exchangeApiUrl = process.env.EXCHANGE_RATE_API_URL;
      const exchangeApiKey = process.env.EXCHANGE_RATE_API_KEY;

      if (exchangeApiUrl && exchangeApiKey) {

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        try {
          const response = await fetch(`${exchangeApiUrl}?from=${from}&to=${to}&key=${exchangeApiKey}`, {
            signal: controller.signal
          });

          clearTimeout(timeoutId);

          if (response.ok) {
            const data = await response.json() as any;
            if (data.rate && typeof data.rate === 'number') {
              logger.info('Exchange rate fetched from API', {
                from,
                to,
                rate: data.rate,
                source: 'external_api'
              });
              return data.rate;
            }
          }
        } catch (fetchError) {
          clearTimeout(timeoutId);
          throw fetchError;
        }
      }
    } catch (error) {
      logger.warn('Failed to fetch live exchange rate, using fallback', {
        from,
        to,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    const baseRates: Record<string, Record<string, number>> = {
      [Currency.USD]: { [Currency.RWF]: 1300, [Currency.KES]: 150, [Currency.EUR]: 0.85 },
      [Currency.EUR]: { [Currency.USD]: 1.18, [Currency.RWF]: 1534, [Currency.KES]: 177 },
      [Currency.RWF]: { [Currency.USD]: 0.00077, [Currency.EUR]: 0.00065, [Currency.KES]: 0.115 },
      [Currency.KES]: { [Currency.USD]: 0.0067, [Currency.EUR]: 0.0056, [Currency.RWF]: 8.67 }
    };

    const baseRate = baseRates[from]?.[to] || 1;

    const fluctuation = (Math.random() - 0.5) * 0.04; // ±2%
    const finalRate = baseRate * (1 + fluctuation);

    logger.info('Exchange rate calculated', {
      from,
      to,
      baseRate,
      finalRate: Math.round(finalRate * 10000) / 10000,
      source: 'fallback_with_fluctuation'
    });

    return Math.round(finalRate * 10000) / 10000; 
  }

  private static getEstimatedDelivery(country: string, method: string): string {
    const deliveryTimes: Record<string, Record<string, string>> = {
      'RW': { bank: '1-2 hours', mobile_money: 'instant', cash_pickup: '30 minutes' },
      'KE': { bank: '2-4 hours', mobile_money: 'instant', cash_pickup: '1 hour' },
      'UG': { bank: '4-6 hours', mobile_money: '5 minutes', cash_pickup: '2 hours' },
      'TZ': { bank: '6-8 hours', mobile_money: '10 minutes', cash_pickup: '3 hours' }
    };

    return deliveryTimes[country]?.[method] || '1-2 business days';
  }

  private static async processElectricityPayment(data: any): Promise<any> {    const apiUrl = await SystemConfig.getConfig('electricityProviderApiUrl');
    const apiKey = await SystemConfig.getConfig('electricityProviderApiKey');

    if (!apiUrl || !apiKey) {
      throw new Error('Electricity provider API not configured');
    }

    try {
      const response = await axios.post(apiUrl, {
        accountNumber: data.accountNumber,
        amount: data.amount,
        apiKey: apiKey
      }, { timeout: 30000 });

      return {
        confirmationCode: response.data.confirmationCode,
        providerRef: response.data.providerRef,
        status: response.data.status,
        units: response.data.units,
        meterNumber: data.accountNumber,
        provider: response.data.provider
      };
    } catch (error: any) {
      logger.error('Electricity payment processing failed:', error);
      throw new Error('Failed to process electricity payment');
    }
  }

  private static async processWaterPayment(data: any): Promise<any> {
    // simulate water provider API call
    await new Promise(resolve => setTimeout(resolve, 800));

    return {
      confirmationCode: `WATER_${CryptoUtils.generateRandomString(8).toUpperCase()}`,
      providerRef: `H2O_${Date.now()}`,
      status: 'success',
      accountNumber: data.accountNumber,
      provider: 'City Water Authority'
    };
  }

  private static async processInternetPayment(_data: any): Promise<any> {
    // simulate internet provider API call
    await new Promise(resolve => setTimeout(resolve, 1200));

    return {
      confirmationCode: `NET_${CryptoUtils.generateRandomString(8).toUpperCase()}`,
      providerRef: `ISP_${Date.now()}`,
      status: 'success',
      packageName: 'Standard Package',
      validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      provider: 'FastNet Internet'
    };
  }

  private static async processMobilePayment(data: any): Promise<any> {
    // Get mobile provider API configuration
    const apiUrl = await SystemConfig.getConfig('mobileProviderApiUrl');
    const apiKey = await SystemConfig.getConfig('mobileProviderApiKey');

    if (!apiUrl || !apiKey) {
      throw new Error('Mobile provider API not configured');
    }

    try {
      const response = await axios.post(apiUrl, {
        phoneNumber: data.accountNumber,
        amount: data.amount,
        apiKey: apiKey
      }, { timeout: 30000 });

      return {
        confirmationCode: response.data.confirmationCode,
        providerRef: response.data.providerRef,
        status: response.data.status,
        phoneNumber: data.accountNumber,
        airtime: data.amount,
        provider: response.data.provider
      };
    } catch (error: any) {
      logger.error('Mobile payment processing failed:', error);
      throw new Error('Failed to process mobile payment');
    }
  }

  private static async processGenericBillPayment(data: any): Promise<any> {
    await new Promise(resolve => setTimeout(resolve, 1000));

    return {
      confirmationCode: `BILL_${CryptoUtils.generateRandomString(8).toUpperCase()}`,
      providerRef: `GEN_${Date.now()}`,
      status: 'success',
      accountNumber: data.accountNumber,
      provider: data.provider
    };
  }
}
