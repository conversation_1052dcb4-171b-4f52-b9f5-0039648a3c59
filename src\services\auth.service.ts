import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { User } from '../models/user.model';
import { logger } from '../config/logger';

export class AuthService {
  static async login(credentials: { email?: string; phone?: string; password: string }) {
    try {
      const { email, phone, password } = credentials;
      
      let user;
      if (email) {
        user = await User.findOne({ email, deleted_at: null });
      } else if (phone) {
        user = await User.findOne({ phone, deleted_at: null });
      }

      if (!user) {
        return {
          success: false,
          message: 'invalid credentials'
        };
      }


      if (user.security?.locked_until && user.security.locked_until > new Date()) {
        return {
          success: false,
          message: 'account is locked'
        };
      }

      if (!user.password) {
        return {
          success: false,
          message: 'invalid credentials'
        };
      }

      const isValidPassword = await bcrypt.compare(password, user.password);
      if (!isValidPassword) {
        return {
          success: false,
          message: 'invalid credentials'
        };
      }

      const jwtSecret = process.env.JWT_SECRET || 'default-secret';
      const payload = {
        id: user._id.toString(),
        email: user.email,
        role: user.role
      };
      const token = jwt.sign(payload, jwtSecret);

      user.last_login = new Date();
      await user.save();

      return {
        success: true,
        data: {
          token,
          user: {
            id: user._id.toString(),
            email: user.email,
            phone: user.phone,
            first_name: user.first_name,
            last_name: user.last_name,
            role: user.role
          }
        }
      };
    } catch (error: any) {
      logger.error('Login error:', error);
      return {
        success: false,
        message: 'login failed'
      };
    }
  }

  static async validateToken(token: string) {
    try {
      const jwtSecret = process.env.JWT_SECRET || 'default-secret';
      const decoded = jwt.verify(token, jwtSecret) as any;
      const user = await User.findById(decoded.id);
      
      if (!user || user.deleted_at) {
        return {
          success: false,
          message: 'invalid token'
        };
      }

      return {
        success: true,
        data: {
          user: {
            id: user._id.toString(),
            email: user.email,
            role: user.role
          }
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'invalid token'
      };
    }
  }

  static async refreshToken(token: string) {
    try {
      const validation = await this.validateToken(token);
      if (!validation.success) {
        return validation;
      }

      const jwtSecret = process.env.JWT_SECRET || 'default-secret';
      const userData = validation.data?.user;
      if (!userData) {
        return {
          success: false,
          message: 'invalid token data'
        };
      }

      const payload = {
        id: userData.id,
        email: userData.email,
        role: userData.role
      };
      const newToken = jwt.sign(payload, jwtSecret);

      return {
        success: true,
        data: {
          token: newToken
        }
      };
    } catch (error: any) {
      logger.error('Token refresh error:', error);
      return {
        success: false,
        message: 'token refresh failed'
      };
    }
  }

  static async logout(token: string) {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default-secret') as any;
      const blacklistKey = `blacklist:${decoded.jti || token.slice(-10)}`;
      const expiresAt = new Date(decoded.exp * 1000);

      // Store blacklisted token in database
      const { BlacklistedToken } = await import('../models/blacklisted-token.model');
      await BlacklistedToken.create({
        token_hash: blacklistKey,
        user_id: decoded.id,
        expires_at: expiresAt,
        created_at: new Date()
      });

      return {
        success: true,
        message: 'logged out successfully'
      };
    } catch (error: any) {
      logger.error('Logout error:', error);
      return {
        success: false,
        message: 'logout failed'
      };
    }
  }

  static async changePassword(userId: string, passwordData: { 
    currentPassword: string; 
    newPassword: string; 
    confirmPassword: string; 
  }) {
    try {
      const { currentPassword, newPassword, confirmPassword } = passwordData;

      if (newPassword !== confirmPassword) {
        return {
          success: false,
          message: 'passwords do not match'
        };
      }

      const user = await User.findById(userId);
      if (!user) {
        return {
          success: false,
          message: 'user not found'
        };
      }

      if (!user.password) {
        return {
          success: false,
          message: 'user password not set'
        };
      }

      const isValidPassword = await bcrypt.compare(currentPassword, user.password);
      if (!isValidPassword) {
        return {
          success: false,
          message: 'current password is incorrect'
        };
      }

      const hashedPassword = await bcrypt.hash(newPassword, 10);
      user.password = hashedPassword;
      await user.save();

      return {
        success: true,
        message: 'password changed successfully'
      };
    } catch (error: any) {
      logger.error('Change password error:', error);
      return {
        success: false,
        message: 'password change failed'
      };
    }
  }

  static async initiatePasswordReset(email: string) {
    try {
      const user = await User.findOne({ email, deleted_at: null });
      if (!user) {
        return {
          success: false,
          message: 'user not found'
        };
      }

      // Generate password reset token
      const resetToken = jwt.sign(
        { userId: user._id.toString(), type: 'password_reset' },
        process.env.JWT_SECRET || 'default-secret'
      );

      // Store reset token with expiration (15 minutes)
      user.security = user.security || {};
      user.security.password_reset_token = resetToken;
      user.security.password_reset_expires = new Date(Date.now() + 15 * 60 * 1000);
      await user.save();

      // Send email with reset token
      await this.sendPasswordResetEmail(email, resetToken);

      return {
        success: true,
        message: 'password reset email sent'
      };
    } catch (error: any) {
      logger.error('Password reset error:', error);
      return {
        success: false,
        message: 'password reset failed'
      };
    }
  }
  private static async sendPasswordResetEmail(email: string, token: string) {
    try {

      const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/reset-password?token=${token}`;

      logger.info(`Password reset email would be sent to ${email} with URL: ${resetUrl}`);

      // Dev mode
      if (process.env.NODE_ENV === 'development') {
        console.log(`Password Reset Email:
To: ${email}
Subject: Reset Your Password
Reset URL: ${resetUrl}
Token expires in 15 minutes.`);
      }

      return true;
    } catch (error) {
      logger.error('Error sending password reset email:', error);
      throw error;
    }
  }
}
