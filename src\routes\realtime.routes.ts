import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { AuthMiddleware } from '../middleware/auth.middleware';
import * as realtimeController from '../controllers/realtime.controller';

export async function realtimeRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  fastify.get('/connect', { preHandler: AuthMiddleware.authenticateRequest }, realtimeController.connectSSE as any);
  fastify.post('/refresh-dashboard', { preHandler: AuthMiddleware.authenticateRequest }, realtimeController.refreshDashboard as any);
  fastify.post('/trigger-balance-update', { preHandler: AuthMiddleware.authenticateRequest }, realtimeController.triggerBalanceUpdate as any);
  fastify.post('/test-notification', { preHandler: AuthMiddleware.authenticateRequest }, realtimeController.sendTestNotification as any);
  fastify.get('/stats', { preHandler: AuthMiddleware.authenticateRequest }, realtimeController.getConnectionStats as any);
  fastify.post('/broadcast', { preHandler: AuthMiddleware.authenticateRequest }, realtimeController.broadcastMessage as any);
  fastify.get('/health', realtimeController.getRealtimeHealth as any);

  fastify.get('/service-health', async () => {
    return {
      success: true,
      message: 'real-time service running',
      data: { 
        status: 'ok',
        features: ['sse', 'dashboard_updates', 'balance_notifications', 'transaction_alerts']
      }
    };
  });
}
