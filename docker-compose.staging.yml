# Staging Docker Compose
version: '3.8'

services:
  # Main API Service
  api:
    image: ghcr.io/${GITHUB_REPOSITORY}:develop
    container_name: aetrust-api-staging
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=staging
      - PORT=3000
      - MONGODB_URI=${MONGODB_URI}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
      - LOG_LEVEL=debug
      - API_RATE_LIMIT=200
    volumes:
      - ./logs:/app/logs
    depends_on:
      - mongodb
      - redis
    networks:
      - aetrust-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB Database
  mongodb:
    image: mongo:5.0
    container_name: aetrust-mongodb-staging
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_ROOT_USERNAME}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_ROOT_PASSWORD}
      - MONGO_INITDB_DATABASE=aetrust_staging
    volumes:
      - mongodb_data:/data/db
    networks:
      - aetrust-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: aetrust-redis-staging
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - aetrust-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: aetrust-nginx-staging
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx/staging.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - api
    networks:
      - aetrust-network
    restart: unless-stopped

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  aetrust-network:
    driver: bridge
