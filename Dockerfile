FROM node:18-alpine AS base

RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init && \
    addgroup -g 1001 -S nodejs && \
    adduser -S aetrust -u 1001

WORKDIR /app

COPY package*.json ./

FROM base AS development
ENV NODE_ENV=development
RUN npm ci --include=dev
COPY . .
RUN npm run build
USER aetrust
EXPOSE 3000
CMD ["dumb-init", "npm", "run", "dev"]

# Production dependencies stage
FROM base AS deps
ENV NODE_ENV=production
RUN npm ci --only=production && npm cache clean --force

# Production build stage
FROM base AS build
ENV NODE_ENV=production
COPY package*.json ./
RUN npm ci --include=dev
COPY . .
RUN npm run build && npm prune --production

# Production stage
FROM node:18-alpine AS production

# Install security updates
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init && \
    addgroup -g 1001 -S nodejs && \
    adduser -S aetrust -u 1001

WORKDIR /app

# Copy production dependencies
COPY --from=deps --chown=aetrust:nodejs /app/node_modules ./node_modules

# Copy built application
COPY --from=build --chown=aetrust:nodejs /app/dist ./dist
COPY --from=build --chown=aetrust:nodejs /app/package*.json ./

# Create logs directory
RUN mkdir -p logs && chown aetrust:nodejs logs

# Security: Use non-root user
USER aetrust

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Expose port
EXPOSE 3000

# Start application with dumb-init for proper signal handling
CMD ["dumb-init", "node", "dist/server.js"]
