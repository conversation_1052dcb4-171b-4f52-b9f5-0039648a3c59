import crypto from 'crypto';
import { User } from '../models/user.model';
import { CommunicationService } from './communication.service';
import { logger, securityLogger } from '../config/logger';
import { Platform } from '../types';

export class VerificationService {
  private static phoneCodeCache = new Map<string, { code: string; attempts: number; lastAttempt: Date }>();
  private static emailCodeCache = new Map<string, { code: string; attempts: number; lastAttempt: Date }>();
  
  // rate limiting - max 3 attempts per 15 minutes
  private static readonly MAX_ATTEMPTS = 3;
  private static readonly RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutes
  private static readonly CODE_EXPIRY = 10 * 60 * 1000; // 10 minutes
  
  static generateVerificationCode(): string {
    return crypto.randomInt(100000, 999999).toString();
  }
  
  static async sendPhoneVerification(phone: string, platform: Platform, ipAddress?: string): Promise<{ success: boolean; message: string; retryAfter?: number }> {
    try {
      // check rate limiting
      const rateLimitCheck = this.checkRateLimit(phone, this.phoneCodeCache);
      if (!rateLimitCheck.allowed) {
        securityLogger.warn('Phone verification rate limit exceeded', {
          phone,
          platform,
          ipAddress,
          attempts: rateLimitCheck.attempts
        });
        
        return {
          success: false,
          message: 'too many verification attempts, please try again later',
          ...(rateLimitCheck.retryAfter && { retryAfter: rateLimitCheck.retryAfter })
        };
      }
      
      const code = this.generateVerificationCode();
      const expiresAt = new Date(Date.now() + this.CODE_EXPIRY);
      
      // store in cache for rate limiting
      this.phoneCodeCache.set(phone, {
        code,
        attempts: (this.phoneCodeCache.get(phone)?.attempts || 0) + 1,
        lastAttempt: new Date()
      });
      
      // update user record
      await User.updateOne(
        { phone },
        {
          phone_verification_code: code,
          phone_verification_expires: expiresAt
        }
      );
      
      const smsResult = await CommunicationService.sendVerificationSms(
        phone,
        code,
        platform
      );

      if (!smsResult.success) {
        logger.error('Failed to send SMS verification', { phone, platform, error: smsResult.message });
        return {
          success: false,
          message: 'failed to send verification code'
        };
      }
      
      logger.info('Phone verification code sent', {
        phone,
        platform,
        ipAddress,
        expiresAt
      });
      
      return {
        success: true,
        message: 'verification code sent to your phone'
      };
      
    } catch (error: any) {
      logger.error('Phone verification error:', error);
      return {
        success: false,
        message: 'failed to send verification code'
      };
    }
  }
  
  static async sendEmailVerification(email: string, platform: Platform, ipAddress?: string): Promise<{ success: boolean; message: string; retryAfter?: number }> {
    try {
      // check rate limiting
      const rateLimitCheck = this.checkRateLimit(email, this.emailCodeCache);
      if (!rateLimitCheck.allowed) {
        securityLogger.warn('Email verification rate limit exceeded', {
          email,
          platform,
          ipAddress,
          attempts: rateLimitCheck.attempts
        });
        
        return {
          success: false,
          message: 'too many verification attempts, please try again later',
          ...(rateLimitCheck.retryAfter && { retryAfter: rateLimitCheck.retryAfter })
        };
      }
      
      const code = this.generateVerificationCode();
      const expiresAt = new Date(Date.now() + this.CODE_EXPIRY);
      
      this.emailCodeCache.set(email, {
        code,
        attempts: (this.emailCodeCache.get(email)?.attempts || 0) + 1,
        lastAttempt: new Date()
      });
      
      await User.updateOne(
        { email },
        {
          email_verification_code: code,
          email_verification_expires: expiresAt
        }
      );
      
      const user = await User.findOne({ email });
      const emailResult = await CommunicationService.sendVerificationEmail(
        email,
        code,
        user?.first_name
      );

      if (!emailResult.success) {
        logger.error('Failed to send email verification', { email, platform, error: emailResult.message });
        return {
          success: false,
          message: 'failed to send verification code'
        };
      }
      
      logger.info('Email verification code sent', {
        email,
        platform,
        ipAddress,
        expiresAt
      });
      
      return {
        success: true,
        message: 'verification code sent to your email'
      };
      
    } catch (error: any) {
      logger.error('Email verification error:', error);
      return {
        success: false,
        message: 'failed to send verification code'
      };
    }
  }
  
  static async verifyPhoneCode(phone: string, code: string, platform: Platform, ipAddress?: string): Promise<{ success: boolean; message: string; user?: any }> {
    try {
      const user = await User.findOne({ phone });
      
      if (!user) {
        securityLogger.warn('Phone verification failed - user not found', {
          phone,
          platform,
          ipAddress
        });
        
        return {
          success: false,
          message: 'invalid verification code'
        };
      }
      
      if (!user.phone_verification_code || !user.phone_verification_expires) {
        return {
          success: false,
          message: 'no verification code found, please request a new one'
        };
      }
      
      if (user.phone_verification_expires < new Date()) {
        return {
          success: false,
          message: 'verification code has expired, please request a new one'
        };
      }
      
      if (user.phone_verification_code !== code) {
        securityLogger.warn('Phone verification failed - invalid code', {
          phone,
          platform,
          ipAddress,
          providedCode: code
        });
        
        return {
          success: false,
          message: 'invalid verification code'
        };
      }
      
      user.phone_verified = true;
      user.phone_verification_code = null;
      user.phone_verification_expires = null;
      await user.save();
      
      this.phoneCodeCache.delete(phone);
      
      logger.info('Phone verification successful', {
        userId: user._id,
        phone,
        platform,
        ipAddress
      });
      
      return {
        success: true,
        message: 'phone number verified successfully',
        user: {
          id: user._id.toString(),
          phone: user.phone,
          phoneVerified: user.phone_verified
        }
      };
      
    } catch (error: any) {
      logger.error('Phone verification error:', error);
      return {
        success: false,
        message: 'verification failed'
      };
    }
  }
  
  static async verifyEmailCode(email: string, code: string, platform: Platform, ipAddress?: string): Promise<{ success: boolean; message: string; user?: any }> {
    try {
      const user = await User.findOne({ email });
      
      if (!user) {
        securityLogger.warn('Email verification failed - user not found', {
          email,
          platform,
          ipAddress
        });
        
        return {
          success: false,
          message: 'invalid verification code'
        };
      }
      
      if (!user.email_verification_code || !user.email_verification_expires) {
        return {
          success: false,
          message: 'no verification code found, please request a new one'
        };
      }
      
      if (user.email_verification_expires < new Date()) {
        return {
          success: false,
          message: 'verification code has expired, please request a new one'
        };
      }
      
      if (user.email_verification_code !== code) {
        securityLogger.warn('Email verification failed - invalid code', {
          email,
          platform,
          ipAddress,
          providedCode: code
        });
        
        return {
          success: false,
          message: 'invalid verification code'
        };
      }
      
      // mark email as verified
      user.email_verified = true;
      user.email_verification_code = null;
      user.email_verification_expires = null;
      user.is_verified = user.phone_verified && true; // both phone and email verified
      await user.save();
      
      // clear rate limiting cache
      this.emailCodeCache.delete(email);
      
      logger.info('Email verification successful', {
        userId: user._id,
        email,
        platform,
        ipAddress
      });
      
      return {
        success: true,
        message: 'email verified successfully',
        user: {
          id: user._id.toString(),
          email: user.email,
          emailVerified: user.email_verified
        }
      };
      
    } catch (error: any) {
      logger.error('Email verification error:', error);
      return {
        success: false,
        message: 'verification failed'
      };
    }
  }
  
  private static checkRateLimit(identifier: string, cache: Map<string, any>): { allowed: boolean; attempts: number; retryAfter?: number } {
    const record = cache.get(identifier);
    
    if (!record) {
      return { allowed: true, attempts: 0 };
    }
    
    const timeSinceLastAttempt = Date.now() - record.lastAttempt.getTime();
    
    if (timeSinceLastAttempt > this.RATE_LIMIT_WINDOW) {
      // reset attempts after rate limit window
      cache.delete(identifier);
      return { allowed: true, attempts: 0 };
    }
    
    if (record.attempts >= this.MAX_ATTEMPTS) {
      const retryAfter = Math.ceil((this.RATE_LIMIT_WINDOW - timeSinceLastAttempt) / 1000);
      return { allowed: false, attempts: record.attempts, retryAfter };
    }
    
    return { allowed: true, attempts: record.attempts };
  }
  

  
  // cleanup expired codes periodically
  static cleanupExpiredCodes(): void {
    const now = Date.now();
    
    for (const [key, value] of this.phoneCodeCache.entries()) {
      if (now - value.lastAttempt.getTime() > this.RATE_LIMIT_WINDOW) {
        this.phoneCodeCache.delete(key);
      }
    }
    
    for (const [key, value] of this.emailCodeCache.entries()) {
      if (now - value.lastAttempt.getTime() > this.RATE_LIMIT_WINDOW) {
        this.emailCodeCache.delete(key);
      }
    }
  }
}

// cleanup expired codes every 30 minutes
setInterval(() => {
  VerificationService.cleanupExpiredCodes();
}, 30 * 60 * 1000);
