import { FastifyRequest, FastifyReply } from 'fastify';
import { TopUpService, TopUpMethod } from '../services/topup.service';
import { TransactionService } from '../services/transaction.service';
import { AuthenticatedRequest, TransactionType } from '../types';
import { logger } from '../config/logger';
import { Currency, Platform } from '../types';

export const initiateBankTransfer = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { amount, currency, bankCode, platform = 'web' } = request.body as any;
    const ipAddress = request.ip;

    if (!amount || !currency || !bankCode) {
      return reply.status(400).send({
        success: false,
        message: 'amount, currency and bank code required'
      });
    }

    if (amount <= 0) {
      return reply.status(400).send({
        success: false,
        message: 'amount must be greater than zero'
      });
    }

    const result = await TopUpService.initiateBankTransfer({
      userId: request.user.id,
      amount,
      currency: currency as Currency,
      bankCode: bankCode,
      platform: platform as Platform,
      ipAddress
    });

    if (!result.success) {
      return reply.status(400).send(result);
    }

    return reply.status(200).send({
      success: true,
      message: result.message,
      data: result.data
    });
  } catch (error: any) {
    logger.error('Error initiating bank transfer:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to initiate bank transfer'
    });
  }
};

export const initiateCardPayment = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { 
      amount, 
      currency, 
      card_number, 
      expiry_month, 
      expiry_year, 
      cvv, 
      card_holder_name,
      platform = 'web' 
    } = request.body as any;
    const ipAddress = request.ip;

    if (!amount || !currency || !card_number || !expiry_month || !expiry_year || !cvv || !card_holder_name) {
      return reply.status(400).send({
        success: false,
        message: 'all card details required'
      });
    }

    if (amount <= 0) {
      return reply.status(400).send({
        success: false,
        message: 'amount must be greater than zero'
      });
    }

    // basic card validation
    if (!/^\d{16}$/.test(card_number.replace(/\s/g, ''))) {
      return reply.status(400).send({
        success: false,
        message: 'invalid card number'
      });
    }

    if (!/^\d{3,4}$/.test(cvv)) {
      return reply.status(400).send({
        success: false,
        message: 'invalid cvv'
      });
    }

    const result = await TopUpService.initiateCardPayment({
      userId: request.user.id,
      amount,
      currency: currency as Currency,
      cardDetails: {
        cardNumber: card_number.replace(/\s/g, ''),
        expiryMonth: expiry_month,
        expiryYear: expiry_year,
        cvv,
        cardHolderName: card_holder_name
      },
      platform: platform as Platform,
      ipAddress
    });

    if (!result.success) {
      return reply.status(400).send(result);
    }

    return reply.status(200).send({
      success: true,
      message: result.message,
      data: result.data
    });
  } catch (error: any) {
    logger.error('Error processing card payment:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to process card payment'
    });
  }
};

export const initiateAgentDeposit = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { amount, currency, agentId, platform = 'web' } = request.body as any;
    const ipAddress = request.ip;

    if (!amount || !currency || !agentId) {
      return reply.status(400).send({
        success: false,
        message: 'amount, currency and agent id required'
      });
    }

    if (amount <= 0) {
      return reply.status(400).send({
        success: false,
        message: 'amount must be greater than zero'
      });
    }

    const result = await TopUpService.initiateAgentDeposit({
      userId: request.user.id,
      agentId: agentId,
      amount,
      currency: currency as Currency,
      platform: platform as Platform,
      ipAddress
    });

    if (!result.success) {
      return reply.status(400).send(result);
    }

    return reply.status(200).send({
      success: true,
      message: result.message,
      data: result.data
    });
  } catch (error: any) {
    logger.error('Error initiating agent deposit:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to initiate agent deposit'
    });
  }
};

export const findNearestAgents = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { latitude, longitude, radius, limit } = request.query as any;

    if (!latitude || !longitude) {
      return reply.status(400).send({
        success: false,
        message: 'latitude and longitude required'
      });
    }

    const lat = parseFloat(latitude);
    const lng = parseFloat(longitude);

    if (isNaN(lat) || isNaN(lng)) {
      return reply.status(400).send({
        success: false,
        message: 'invalid coordinates'
      });
    }

    const searchParams: any = {
      latitude: lat,
      longitude: lng
    };

    if (radius) searchParams.radius = parseInt(radius);
    if (limit) searchParams.limit = parseInt(limit);

    const agents = await TopUpService.findNearestAgents(searchParams);

    return reply.status(200).send({
      success: true,
      message: 'nearest agents retrieved successfully',
      data: {
        agents,
        total_found: agents.length,
        search_location: { latitude: lat, longitude: lng }
      }
    });
  } catch (error: any) {
    logger.error('Error finding nearest agents:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to find nearest agents'
    });
  }
};

export const getTopUpHistory = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { page = 1, limit = 20 } = request.query as any;

    const result = await TransactionService.getUserTransactions(request.user.id, {
      page: parseInt(page),
      limit: parseInt(limit),
      type: TransactionType.DEPOSIT
    });

    const transactions = result.transactions.map((transaction: any) => ({
      id: transaction._id.toString(),
      transactionRef: transaction.transaction_ref,
      method: transaction.metadata?.method || 'unknown',
      amount: transaction.amount,
      currency: transaction.currency,
      status: transaction.status,
      createdAt: transaction.created_at,
      bankName: transaction.metadata?.bankName,
      maskedCard: transaction.metadata?.maskedCard,
      agentName: transaction.metadata?.agentName
    }));

    const topupHistory = {
      transactions,
      pagination: {
        currentPage: result.page,
        itemsPerPage: parseInt(limit),
        totalPages: result.pages,
        totalItems: result.total,
        hasNext: result.page < result.pages,
        hasPrevious: result.page > 1
      }
    };

    return reply.status(200).send({
      success: true,
      message: 'top-up history retrieved successfully',
      data: topupHistory
    });
  } catch (error: any) {
    logger.error('Error getting top-up history:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to get top-up history'
    });
  }
};

export const getSupportedBanks = async (_request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const banks = [
      { code: '044', name: 'Access Bank', logo: '/assets/banks/access.png' },
      { code: '014', name: 'Afribank', logo: '/assets/banks/afribank.png' },
      { code: '023', name: 'Citibank', logo: '/assets/banks/citibank.png' },
      { code: '050', name: 'Ecobank', logo: '/assets/banks/ecobank.png' },
      { code: '011', name: 'First Bank', logo: '/assets/banks/firstbank.png' },
      { code: '214', name: 'First City Monument Bank', logo: '/assets/banks/fcmb.png' },
      { code: '070', name: 'Fidelity Bank', logo: '/assets/banks/fidelity.png' },
      { code: '058', name: 'Guaranty Trust Bank', logo: '/assets/banks/gtbank.png' },
      { code: '030', name: 'Heritage Bank', logo: '/assets/banks/heritage.png' },
      { code: '082', name: 'Keystone Bank', logo: '/assets/banks/keystone.png' },
      { code: '076', name: 'Polaris Bank', logo: '/assets/banks/polaris.png' },
      { code: '221', name: 'Stanbic IBTC Bank', logo: '/assets/banks/stanbic.png' },
      { code: '068', name: 'Standard Chartered', logo: '/assets/banks/standardchartered.png' },
      { code: '232', name: 'Sterling Bank', logo: '/assets/banks/sterling.png' },
      { code: '032', name: 'Union Bank', logo: '/assets/banks/union.png' },
      { code: '033', name: 'United Bank for Africa', logo: '/assets/banks/uba.png' },
      { code: '215', name: 'Unity Bank', logo: '/assets/banks/unity.png' },
      { code: '035', name: 'Wema Bank', logo: '/assets/banks/wema.png' },
      { code: '057', name: 'Zenith Bank', logo: '/assets/banks/zenith.png' }
    ];

    return reply.status(200).send({
      success: true,
      message: 'supported banks retrieved successfully',
      data: {
        banks,
        total_banks: banks.length
      }
    });
  } catch (error: any) {
    logger.error('Error getting supported banks:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to get supported banks'
    });
  }
};

export const getTopUpMethods = async (_request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const methods = [
      {
        method: TopUpMethod.BANK_TRANSFER,
        name: 'Bank Transfer',
        description: 'Transfer from your bank account',
        icon: '/assets/icons/bank-transfer.png',
        minAmount: 100,
        maxAmount: 1000000,
        feePercentage: 0,
        processingTime: '5-10 minutes'
      },
      {
        method: TopUpMethod.CARD_PAYMENT,
        name: 'Debit/Credit Card',
        description: 'Pay with your debit or credit card',
        icon: '/assets/icons/card-payment.png',
        minAmount: 100,
        maxAmount: 500000,
        feePercentage: 1.5,
        processingTime: 'Instant'
      },
      {
        method: TopUpMethod.AGENT_DEPOSIT,
        name: 'Agent Deposit',
        description: 'Deposit cash at nearest agent location',
        icon: '/assets/icons/agent-deposit.png',
        minAmount: 500,
        maxAmount: 200000,
        feePercentage: 0.5,
        processingTime: 'Instant'
      }
    ];

    return reply.status(200).send({
      success: true,
      message: 'top-up methods retrieved successfully',
      data: {
        methods,
        totalMethods: methods.length
      }
    });
  } catch (error: any) {
    logger.error('Error getting top-up methods:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to get top-up methods'
    });
  }
};
