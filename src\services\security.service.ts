import { SystemConfig } from '../models/system-config.model';
import { logger, securityLogger } from '../config/logger';
import { CryptoUtils } from '../utils/crypto';
import { redis } from '../config/redis';

export class SecurityService {
  private static readonly MAX_LOGIN_ATTEMPTS = 5;
  private static readonly LOCKOUT_DURATION = 30 * 60 * 1000; // 30 minutes
  // private static readonly SUSPICIOUS_ACTIVITY_THRESHOLD = 10;
  private static readonly IP_RATE_LIMIT = 100; // requests per hour
  private static readonly USER_RATE_LIMIT = 1000; // requests per hour

  /**
   * Advanced brute force protection with progressive delays
   */
  static async checkBruteForceProtection(identifier: string, type: 'ip' | 'user' | 'phone'): Promise<{
    allowed: boolean;
    remainingAttempts?: number;
    lockoutTime?: number;
    reason?: string;
  }> {
    try {
      const key = `bruteforce:${type}:${identifier}`;
      const attempts = await redis.get(key);
      const attemptCount = attempts ? parseInt(attempts) : 0;

      const maxAttempts = await SystemConfig.getConfig('securityMaxLoginAttempts') || this.MAX_LOGIN_ATTEMPTS;

      if (attemptCount >= maxAttempts) {
        const lockoutKey = `lockout:${type}:${identifier}`;
        const lockoutTime = await redis.get(lockoutKey);
        
        if (lockoutTime) {
          const remainingTime = parseInt(lockoutTime) - Date.now();
          if (remainingTime > 0) {
            securityLogger.warn('Brute force protection triggered', {
              type,
              identifier: this.maskSensitiveDataInternal(identifier),
              attemptCount,
              remainingTime
            });

            return {
              allowed: false,
              lockoutTime: remainingTime,
              reason: 'Account temporarily locked due to multiple failed attempts'
            };
          } else {
            // lockout expired, reset attempts
            await redis.del(key);
            await redis.del(lockoutKey);
          }
        }
      }

      return {
        allowed: true,
        remainingAttempts: Math.max(0, maxAttempts - attemptCount)
      };
    } catch (error) {
      logger.error('Error checking brute force protection:', error);
      return { allowed: true }; // fail open for availability
    }
  }

  /**
   * Record failed login attempt with progressive penalties
   */
  static async recordFailedAttempt(identifier: string, type: 'ip' | 'user' | 'phone'): Promise<void> {
    try {
      const key = `bruteforce:${type}:${identifier}`;
      const attempts = await redis.get(key);
      const attemptCount = (attempts ? parseInt(attempts) : 0) + 1;

      const maxAttempts = await SystemConfig.getConfig('securityMaxLoginAttempts') || this.MAX_LOGIN_ATTEMPTS;
      const lockoutDuration = await SystemConfig.getConfig('securityLockoutDuration') || this.LOCKOUT_DURATION;

      // Set attempt count with exponential backoff
      const ttl = Math.min(3600, 60 * Math.pow(2, attemptCount - 1)); // max 1 hour
      await redis.set(key, attemptCount.toString(), ttl);

      if (attemptCount >= maxAttempts) {
        const lockoutKey = `lockout:${type}:${identifier}`;
        const lockoutUntil = Date.now() + lockoutDuration;
        await redis.set(lockoutKey, lockoutUntil.toString(), Math.floor(lockoutDuration / 1000));

        securityLogger.warn('Account locked due to brute force', {
          type,
          identifier: this.maskSensitiveDataInternal(identifier),
          attemptCount,
          lockoutDuration
        });
      }
    } catch (error) {
      logger.error('Error recording failed attempt:', error);
    }
  }

  /**
   * Clear failed attempts on successful login
   */
  static async clearFailedAttempts(identifier: string, type: 'ip' | 'user' | 'phone'): Promise<void> {
    try {
      const key = `bruteforce:${type}:${identifier}`;
      const lockoutKey = `lockout:${type}:${identifier}`;
      await redis.del(key);
      await redis.del(lockoutKey);
    } catch (error) {
      logger.error('Error clearing failed attempts:', error);
    }
  }

  /**
   * Advanced rate limiting with different tiers
   */
  static async checkRateLimit(identifier: string, type: 'ip' | 'user', endpoint?: string): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: number;
    reason?: string;
  }> {
    try {
      const baseKey = endpoint ? `ratelimit:${type}:${endpoint}:${identifier}` : `ratelimit:${type}:${identifier}`;
      const window = 3600; // 1 hour window
      
      const limit = type === 'ip' 
        ? await SystemConfig.getConfig('securityIpRateLimit') || this.IP_RATE_LIMIT
        : await SystemConfig.getConfig('securityUserRateLimit') || this.USER_RATE_LIMIT;

      const current = await redis.get(baseKey);
      const count = current ? parseInt(current) : 0;

      if (count >= limit) {
        const ttl = 3600; // Default 1 hour TTL

        securityLogger.warn('Rate limit exceeded', {
          type,
          identifier: this.maskSensitiveDataInternal(identifier),
          endpoint,
          count,
          limit
        });

        return {
          allowed: false,
          remaining: 0,
          resetTime: ttl,
          reason: 'Rate limit exceeded. Please try again later.'
        };
      }

      // increment counter
      if (current) {
        await redis.set(baseKey, (count + 1).toString());
      } else {
        await redis.set(baseKey, '1', window);
      }

      return {
        allowed: true,
        remaining: limit - count - 1,
        resetTime: window
      };
    } catch (error) {
      logger.error('Error checking rate limit:', error);
      return { allowed: true, remaining: 1000, resetTime: 3600 }; // fail open
    }
  }

  /**
   * Detect and prevent suspicious activity patterns
   */
  static async detectSuspiciousActivity(userId: string, activity: {
    type: string;
    ipAddress: string;
    userAgent: string;
    location?: { country: string; city: string; };
    amount?: number;
  }): Promise<{
    isSuspicious: boolean;
    riskScore: number;
    reasons: string[];
    action: 'allow' | 'challenge' | 'block';
  }> {
    try {
      const reasons: string[] = [];
      let riskScore = 0;

      // check for unusual location
      const userLocationHistory = await this.getUserLocationHistory(userId);
      if (activity.location && !this.isKnownLocation(activity.location, userLocationHistory)) {
        reasons.push('Unusual location detected');
        riskScore += 30;
      }

      // check for unusual time patterns
      const currentHour = new Date().getHours();
      const userActivityPattern = await this.getUserActivityPattern(userId);
      if (!(await this.isNormalActivityTime(currentHour, userActivityPattern))) {
        reasons.push('Unusual activity time');
        riskScore += 20;
      }

      // check for device fingerprinting
      if (!await this.isKnownDevice(userId, activity.userAgent)) {
        reasons.push('New or unknown device');
        riskScore += 25;
      }

      // check for velocity anomalies
      if (activity.amount && await this.isUnusualTransactionAmount(userId, activity.amount)) {
        reasons.push('Unusual transaction amount');
        riskScore += 35;
      }

      // determine action based on risk score
      let action: 'allow' | 'challenge' | 'block' = 'allow';
      if (riskScore >= 70) {
        action = 'block';
      } else if (riskScore >= 40) {
        action = 'challenge';
      }

      if (riskScore > 0) {
        securityLogger.warn('Suspicious activity detected', {
          userId: this.maskSensitiveDataInternal(userId),
          activity: {
            ...activity,
            ipAddress: this.maskSensitiveDataInternal(activity.ipAddress)
          },
          riskScore,
          reasons,
          action
        });
      }

      return {
        isSuspicious: riskScore > 0,
        riskScore,
        reasons,
        action
      };
    } catch (error) {
      logger.error('Error detecting suspicious activity:', error);
      return { isSuspicious: false, riskScore: 0, reasons: [], action: 'allow' };
    }
  }

  /**
   * Validate session integrity and detect session hijacking
   */
  static async validateSessionIntegrity(userId: string, sessionData: {
    ipAddress: string;
    userAgent: string;
    tokenIssuedAt: number;
  }): Promise<{
    isValid: boolean;
    reason?: string;
    action: 'continue' | 'reauth' | 'terminate';
  }> {
    try {
      // check for session age
      const maxSessionAge = await SystemConfig.getConfig('securityMaxSessionAge') || 24 * 60 * 60 * 1000; // 24 hours
      if (Date.now() - sessionData.tokenIssuedAt > maxSessionAge) {
        return {
          isValid: false,
          reason: 'Session expired',
          action: 'reauth'
        };
      }

      // check for IP address changes (with some tolerance for mobile users)
      const storedSession = await redis.get(`session:${userId}`);
      if (storedSession) {
        const session = JSON.parse(storedSession);
        if (session.ipAddress !== sessionData.ipAddress) {
          // allow IP changes within same subnet for mobile users
          if (!this.isSameSubnet(session.ipAddress, sessionData.ipAddress)) {
            securityLogger.warn('Session IP address changed', {
              userId: this.maskSensitiveDataInternal(userId),
              oldIp: this.maskSensitiveDataInternal(session.ipAddress),
              newIp: this.maskSensitiveDataInternal(sessionData.ipAddress)
            });

            return {
              isValid: false,
              reason: 'IP address changed',
              action: 'reauth'
            };
          }
        }
      }

      // update session data
      await redis.set(`session:${userId}`, JSON.stringify(sessionData), 3600);

      return {
        isValid: true,
        action: 'continue'
      };
    } catch (error) {
      logger.error('Error validating session integrity:', error);
      return { isValid: true, action: 'continue' }; // fail open
    }
  }

  /**
   * Public method to mask sensitive data for logging
   */
  static maskSensitiveData(data: string, visibleChars: number = 2): string {
    return CryptoUtils.maskSensitiveData(data, visibleChars);
  }

  // Helper methods
  private static maskSensitiveDataInternal(data: string): string {
    return CryptoUtils.maskSensitiveData(data, 2);
  }

  private static async getUserLocationHistory(userId: string): Promise<Array<{
    country: string;
    city: string;
    ipAddress: string;
    timestamp: Date;
    frequency: number;
  }>> {
    try {
      // Get user's login history from audit logs
      const locationHistory = await redis.get(`user_locations:${userId}`);
      if (locationHistory) {
        return JSON.parse(locationHistory);
      }

      // Fallback to database query for location history
      const { AuditLog } = await import('../models/audit-log.model');
      const loginHistory = await AuditLog.aggregate([
        {
          $match: {
            user_id: userId,
            action: 'USER_LOGIN',
            created_at: { $gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) } // 90 days
          }
        },
        {
          $group: {
            _id: {
              country: '$location.country',
              city: '$location.city',
              ipAddress: '$ip_address'
            },
            frequency: { $sum: 1 },
            lastSeen: { $max: '$created_at' }
          }
        },
        { $sort: { frequency: -1 } }
      ]);

      const locations = loginHistory.map(item => ({
        country: item._id.country || 'Unknown',
        city: item._id.city || 'Unknown',
        ipAddress: item._id.ipAddress,
        timestamp: item.lastSeen,
        frequency: item.frequency
      }));

      // Cache for 1 hour
      await redis.set(`user_locations:${userId}`, JSON.stringify(locations), 3600);
      return locations;
    } catch (error) {
      logger.error('Error getting user location history:', error);
      return [];
    }
  }

  private static isKnownLocation(location: { country: string; city: string; }, history: Array<{
    country: string;
    city: string;
    frequency: number;
  }>): boolean {
    if (!location.country || !location.city) return false;

    // Consider location known if user has logged in from there at least 3 times
    return history.some(h =>
      h.country === location.country &&
      h.city === location.city &&
      h.frequency >= 3
    );
  }

  private static async getUserActivityPattern(userId: string): Promise<number[]> {
    try {
      // Get cached activity pattern
      const cachedPattern = await redis.get(`activity_pattern:${userId}`);
      if (cachedPattern) {
        return JSON.parse(cachedPattern);
      }

      // Calculate activity pattern from audit logs
      const { AuditLog } = await import('../models/audit-log.model');
      const activityData = await AuditLog.aggregate([
        {
          $match: {
            user_id: userId,
            created_at: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } // 30 days
          }
        },
        {
          $group: {
            _id: { $hour: '$created_at' },
            count: { $sum: 1 }
          }
        }
      ]);

      // Create 24-hour activity pattern
      const pattern = new Array(24).fill(0);
      const totalActivity = activityData.reduce((sum, item) => sum + item.count, 0);

      if (totalActivity > 0) {
        activityData.forEach(item => {
          // Normalize activity (0-1 scale)
          pattern[item._id] = item.count / totalActivity;
        });
      }

      // Cache for 6 hours
      await redis.set(`activity_pattern:${userId}`, JSON.stringify(pattern), 21600);
      return pattern;
    } catch (error) {
      logger.error('Error getting user activity pattern:', error);
      // Return default pattern (active during business hours)
      const defaultPattern = new Array(24).fill(0);
      for (let i = 8; i <= 22; i++) {
        defaultPattern[i] = 0.5; // 50% activity during business hours
      }
      return defaultPattern;
    }
  }

  private static async isNormalActivityTime(hour: number, pattern: number[]): Promise<boolean> {
    const threshold = await SystemConfig.getConfig('activityPatternThreshold') || 0.1;
    return pattern && pattern[hour] !== undefined && pattern[hour] >= threshold;
  }

  private static async isKnownDevice(userId: string, userAgent: string): Promise<boolean> {
    try {
      if (!userAgent) return false;

      const deviceHash = CryptoUtils.generateHash(userAgent);
      const deviceKey = `device:${userId}:${deviceHash}`;

      const deviceExists = await redis.exists(deviceKey);
      const isKnown = Boolean(deviceExists);

      if (!isKnown) {
        // Check if this is a new device and record it
        const { AuditLog } = await import('../models/audit-log.model');
        const deviceHistory = await AuditLog.countDocuments({
          user_id: userId,
          user_agent: userAgent,
          created_at: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } // 7 days
        });

        if (deviceHistory >= 3) {
          // Mark as known device if used 3+ times in last 7 days
          await redis.set(deviceKey, '1', 30 * 24 * 3600); // 30 days
          return true;
        }
      }

      return isKnown;
    } catch (error) {
      logger.error('Error checking known device:', error);
      return false; // Fail secure - treat as unknown device
    }
  }

  private static async isUnusualTransactionAmount(userId: string, amount: number): Promise<boolean> {
    try {
      // Get user's transaction statistics
      const { Transaction } = await import('../models/transaction.model');
      const stats = await Transaction.aggregate([
        {
          $match: {
            user_id: userId,
            status: 'completed',
            created_at: { $gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) } // 90 days
          }
        },
        {
          $group: {
            _id: null,
            avgAmount: { $avg: '$amount' },
            maxAmount: { $max: '$amount' },
            stdDev: { $stdDevPop: '$amount' },
            count: { $sum: 1 }
          }
        }
      ]);

      if (!stats.length || stats[0].count < 5) {
        // Not enough transaction history, use conservative thresholds
        const maxSingleTransaction = await SystemConfig.getConfig('maxSingleTransactionNewUser') || 1000;
        return amount > maxSingleTransaction;
      }

      const { avgAmount, maxAmount, stdDev } = stats[0];

      // Consider unusual if:
      // 1. Amount > 3 standard deviations from average
      // 2. Amount > 2x their historical maximum
      // 3. Amount > configured absolute threshold
      const absoluteThreshold = await SystemConfig.getConfig('maxSingleTransactionAmount') || 50000;
      const deviationThreshold = avgAmount + (3 * stdDev);
      const historicalThreshold = maxAmount * 2;

      return amount > Math.min(absoluteThreshold, Math.max(deviationThreshold, historicalThreshold));
    } catch (error) {
      logger.error('Error checking unusual transaction amount:', error);
      // Fail secure - treat large amounts as unusual
      return amount > 10000;
    }
  }

  private static isSameSubnet(ip1: string, ip2: string): boolean {
    try {
      // Proper IP subnet checking with CIDR notation support
      if (!ip1 || !ip2) return false;

      // Handle IPv4 addresses
      if (this.isIPv4(ip1) && this.isIPv4(ip2)) {
        return this.isIPv4SameSubnet(ip1, ip2, 24); // /24 subnet mask
      }

      // Handle IPv6 addresses
      if (this.isIPv6(ip1) && this.isIPv6(ip2)) {
        return this.isIPv6SameSubnet(ip1, ip2, 64); // /64 subnet mask
      }

      return false;
    } catch (error) {
      logger.error('Error checking subnet:', error);
      return false;
    }
  }

  private static isIPv4(ip: string): boolean {
    const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
    if (!ipv4Regex.test(ip)) return false;

    const parts = ip.split('.');
    return parts.every(part => {
      const num = parseInt(part, 10);
      return num >= 0 && num <= 255;
    });
  }

  private static isIPv6(ip: string): boolean {
    const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
    return ipv6Regex.test(ip);
  }

  private static isIPv4SameSubnet(ip1: string, ip2: string, prefixLength: number): boolean {
    const ip1Parts = ip1.split('.').map(part => parseInt(part, 10));
    const ip2Parts = ip2.split('.').map(part => parseInt(part, 10));

    // Validate that we have 4 parts for each IP
    if (ip1Parts.length !== 4 || ip2Parts.length !== 4) {
      return false;
    }

    const ip1Binary = (ip1Parts[0]! << 24) | (ip1Parts[1]! << 16) | (ip1Parts[2]! << 8) | ip1Parts[3]!;
    const ip2Binary = (ip2Parts[0]! << 24) | (ip2Parts[1]! << 16) | (ip2Parts[2]! << 8) | ip2Parts[3]!;

    const mask = (0xFFFFFFFF << (32 - prefixLength)) >>> 0;

    return (ip1Binary & mask) === (ip2Binary & mask);
  }

  private static isIPv6SameSubnet(ip1: string, ip2: string, prefixLength: number): boolean {
    // Simplified IPv6 subnet check - compare first prefixLength bits
    const ip1Hex = ip1.replace(/:/g, '');
    const ip2Hex = ip2.replace(/:/g, '');

    const bitsToCompare = Math.floor(prefixLength / 4);
    return ip1Hex.substring(0, bitsToCompare) === ip2Hex.substring(0, bitsToCompare);
  }
}
