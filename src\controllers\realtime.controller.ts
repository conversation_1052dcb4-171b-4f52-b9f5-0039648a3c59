import { FastifyReply } from 'fastify';
import { AuthenticatedRequest } from '../types';
import { realtimeService } from '../services/realtime.service';
import { logger } from '../config/logger';

// establish SSE connection
export const connectSSE = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { subscriptions } = request.query as any;
    const userSubscriptions = subscriptions ? subscriptions.split(',') : [
      'balance_update',
      'transaction_notification',
      'dashboard_update',
      'system_notification'
    ];

    logger.info('Establishing SSE connection', {
      userId: request.user.id,
      subscriptions: userSubscriptions,
      userAgent: request.headers['user-agent'],
      ip: request.ip
    });

    // establish SSE connection
    await realtimeService.createSSEConnection(request.user.id, reply, userSubscriptions);

  } catch (error: any) {
    logger.error('Error establishing SSE connection:', error);
    
    if (!reply.sent) {
      return reply.status(500).send({
        success: false,
        message: 'failed to establish real-time connection'
      });
    }
  }
};

// trigger manual dashboard refresh
export const refreshDashboard = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    // emit dashboard refresh event
    realtimeService.emit('dashboard_refresh', request.user.id);

    logger.info('Dashboard refresh triggered', {
      userId: request.user.id,
      triggeredBy: 'manual_request'
    });

    return reply.status(200).send({
      success: true,
      message: 'dashboard refresh triggered',
      data: {
        userId: request.user.id,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error triggering dashboard refresh:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to trigger dashboard refresh'
    });
  }
};

// send test notif
export const sendTestNotification = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { type = 'test', message = 'Test notification' } = request.body as any;

    await realtimeService.sendSystemNotification(request.user.id, {
      id: `test_${Date.now()}`,
      title: 'Test Notification',
      message,
      category: 'system',
      priority: 'low',
      timestamp: new Date().toISOString()
    });

    logger.info('Test notification sent', {
      userId: request.user.id,
      type,
      message
    });

    return reply.status(200).send({
      success: true,
      message: 'test notification sent',
      data: {
        type,
        message,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error sending test notification:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to send test notification'
    });
  }
};

// get (admin only)
export const getConnectionStats = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {

    const { User } = await import('../models/user.model');
    const user = await User.findById(request.user.id);
    
    if (!user || (user.role !== 'admin' && user.role !== 'super_admin')) {
      return reply.status(403).send({
        success: false,
        message: 'admin access required'
      });
    }

    const stats = realtimeService.getStats();

    return reply.status(200).send({
      success: true,
      message: 'connection stats retrieved',
      data: {
        ...stats,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error getting connection stats:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve connection stats'
    });
  }
};

// broadcast system message (admin only)
export const broadcastMessage = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {

    const { User } = await import('../models/user.model');
    const user = await User.findById(request.user.id);
    
    if (!user || (user.role !== 'admin' && user.role !== 'super_admin')) {
      return reply.status(403).send({
        success: false,
        message: 'admin access required'
      });
    }

    const { title, message, category = 'system', priority = 'medium' } = request.body as any;

    if (!title || !message) {
      return reply.status(400).send({
        success: false,
        message: 'title and message are required'
      });
    }

    await realtimeService.broadcast({
      type: 'system_broadcast',
      data: {
        id: `broadcast_${Date.now()}`,
        title,
        message,
        category,
        priority,
        from: 'system',
        timestamp: new Date().toISOString()
      }
    });

    logger.info('System message broadcasted', {
      adminId: request.user.id,
      title,
      message,
      category,
      priority
    });

    return reply.status(200).send({
      success: true,
      message: 'system message broadcasted',
      data: {
        title,
        message,
        category,
        priority,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error broadcasting message:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to broadcast message'
    });
  }
};


export const triggerBalanceUpdate = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    // emit balance update event
    realtimeService.emit('balance_changed', {
      userId: request.user.id,
      walletData: {} // fetche in service
    });

    logger.info('Balance update triggered', {
      userId: request.user.id,
      triggeredBy: 'manual_request'
    });

    return reply.status(200).send({
      success: true,
      message: 'balance update triggered',
      data: {
        userId: request.user.id,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error triggering balance update:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to trigger balance update'
    });
  }
};

// health check
export const getRealtimeHealth = async (_request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  try {
    const stats = realtimeService.getStats();
    
    return reply.status(200).send({
      success: true,
      message: 'real-time service is healthy',
      data: {
        status: 'healthy',
        activeConnections: stats.totalConnections,
        uptime: process.uptime(),
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error checking real-time health:', error);
    return reply.status(500).send({
      success: false,
      message: 'real-time service health check failed'
    });
  }
};
