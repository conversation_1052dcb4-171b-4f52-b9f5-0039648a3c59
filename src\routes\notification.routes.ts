// import { FastifyInstance, FastifyPluginOptions } from 'fastify';
// import * as notificationController from '../controllers/notification.controller';
// import { AuthMiddleware } from '../middleware/auth.middleware';

// export async function notificationRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
//   fastify.get('/', { preHandler: AuthMiddleware.authenticateRequest }, notificationController.getNotifications as any);
//   fastify.get('/history', { preHandler: AuthMiddleware.authenticateRequest }, notificationController.getNotificationHistory as any);
//   fastify.get('/unread-count', { preHandler: AuthMiddleware.authenticateRequest }, notificationController.getUnreadCount as any);
//   fastify.put('/:notificationId/read', { preHandler: AuthMiddleware.authenticateRequest }, notificationController.markAsRead as any);
//   fastify.put('/mark-all-read', { preHandler: AuthMiddleware.authenticateRequest }, notificationController.markAllAsRead as any);
//   fastify.post('/', { preHandler: AuthMiddleware.authenticateRequest }, notificationController.createNotification as any);
//   fastify.delete('/:notificationId', { preHandler: AuthMiddleware.authenticateRequest }, notificationController.deleteNotification as any);
//   fastify.get('/settings', { preHandler: AuthMiddleware.authenticateRequest }, notificationController.getNotificationSettings as any);
//   fastify.put('/settings', { preHandler: AuthMiddleware.authenticateRequest }, notificationController.updateNotificationSettings as any);

//   fastify.get('/health', async () => {
//     return {
//       success: true,
//       message: 'notification service is running',
//       data: { status: 'ok' }
//     };
//   });
// }
