import mongoose, { Schema, Document } from 'mongoose';

export interface IFraudAlert extends Document {
  _id: mongoose.Types.ObjectId;
  alert_type: 'suspicious_transaction' | 'unusual_login' | 'velocity_check' | 'location_anomaly' | 'device_anomaly';
  severity: 'low' | 'medium' | 'high' | 'critical';
  user_id: mongoose.Types.ObjectId;
  transaction_ids?: mongoose.Types.ObjectId[];
  risk_score: number;
  status: 'pending' | 'investigating' | 'resolved' | 'false_positive';
  description: string;
  details: any;
  assigned_to?: mongoose.Types.ObjectId;
  resolution_notes?: string;
  resolved_at?: Date;
  created_at: Date;
  updated_at: Date;
}

const fraudAlertSchema = new Schema<IFraudAlert>({
  alert_type: {
    type: String,
    enum: ['suspicious_transaction', 'unusual_login', 'velocity_check', 'location_anomaly', 'device_anomaly'],
    required: true,
    
  },
  severity: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    required: true,
    
  },
  user_id: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    
  },
  transaction_ids: [{
    type: Schema.Types.ObjectId,
    ref: 'Transaction'
  }],
  risk_score: {
    type: Number,
    required: true,
    min: 0,
    max: 100,
    
  },
  status: {
    type: String,
    enum: ['pending', 'investigating', 'resolved', 'false_positive'],
    default: 'pending',
    
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  details: {
    type: Schema.Types.Mixed,
    required: true
  },
  assigned_to: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    sparse: true
  },
  resolution_notes: {
    type: String,
    trim: true
  },
  resolved_at: {
    type: Date,
    sparse: true
  },
  created_at: {
    type: Date,
    default: Date.now,
    
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  toJSON: {
    transform: function(_doc: any, ret: any) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// compound indexes
fraudAlertSchema.index({ status: 1, severity: 1, created_at: -1 });
fraudAlertSchema.index({ user_id: 1, created_at: -1 });
fraudAlertSchema.index({ assigned_to: 1, status: 1 });

// pre-save middleware
fraudAlertSchema.pre('save', function(next) {
  this.updated_at = new Date();
  
  if (this.status === 'resolved' && !this.resolved_at) {
    this.resolved_at = new Date();
  }
  
  next();
});

// static methods interface
interface IFraudAlertModel extends mongoose.Model<IFraudAlert> {
  createAlert(data: {
    alertType: string;
    severity: string;
    userId: string;
    transactionIds?: string[];
    riskScore: number;
    description: string;
    details: any;
  }): Promise<IFraudAlert>;
  getAlerts(filters: {
    status?: string;
    severity?: string;
    assignedTo?: string;
    dateFrom?: Date;
    dateTo?: Date;
    page?: number;
    limit?: number;
  }): Promise<{
    alerts: IFraudAlert[];
    total: number;
    page: number;
    pages: number;
  }>;
  assignAlert(alertId: string, assignedTo: string): Promise<IFraudAlert | null>;
  resolveAlert(alertId: string, resolutionNotes: string, resolvedBy: string): Promise<IFraudAlert | null>;
}

// static methods
fraudAlertSchema.statics.createAlert = async function(data: {
  alertType: string;
  severity: string;
  userId: string;
  transactionIds?: string[];
  riskScore: number;
  description: string;
  details: any;
}) {
  const alert = new this({
    alert_type: data.alertType,
    severity: data.severity,
    user_id: new mongoose.Types.ObjectId(data.userId),
    transaction_ids: data.transactionIds?.map(id => new mongoose.Types.ObjectId(id)),
    risk_score: data.riskScore,
    description: data.description,
    details: data.details
  });

  await alert.save();
  return alert;
};

fraudAlertSchema.statics.getAlerts = async function(filters: {
  status?: string;
  severity?: string;
  assignedTo?: string;
  dateFrom?: Date;
  dateTo?: Date;
  page?: number;
  limit?: number;
}) {
  const query: any = {};
  
  if (filters.status) query.status = filters.status;
  if (filters.severity) query.severity = filters.severity;
  if (filters.assignedTo) query.assigned_to = new mongoose.Types.ObjectId(filters.assignedTo);
  
  if (filters.dateFrom || filters.dateTo) {
    query.created_at = {};
    if (filters.dateFrom) query.created_at.$gte = filters.dateFrom;
    if (filters.dateTo) query.created_at.$lte = filters.dateTo;
  }

  const page = filters.page || 1;
  const limit = Math.min(filters.limit || 20, 100);
  const skip = (page - 1) * limit;

  const [alerts, total] = await Promise.all([
    this.find(query)
      .populate('user_id', 'first_name last_name email phone')
      .populate('assigned_to', 'first_name last_name email')
      .sort({ created_at: -1 })
      .skip(skip)
      .limit(limit)
      .lean(),
    this.countDocuments(query)
  ]);

  return {
    alerts,
    total,
    page,
    pages: Math.ceil(total / limit)
  };
};

fraudAlertSchema.statics.assignAlert = async function(alertId: string, assignedTo: string) {
  return this.findByIdAndUpdate(
    alertId,
    { 
      assigned_to: new mongoose.Types.ObjectId(assignedTo),
      status: 'investigating',
      updated_at: new Date()
    },
    { new: true }
  );
};

fraudAlertSchema.statics.resolveAlert = async function(alertId: string, resolutionNotes: string, _resolvedBy: string) {
  return this.findByIdAndUpdate(
    alertId,
    { 
      status: 'resolved',
      resolution_notes: resolutionNotes,
      resolved_at: new Date(),
      updated_at: new Date()
    },
    { new: true }
  );
};

export const FraudAlert = mongoose.model<IFraudAlert, IFraudAlertModel>('FraudAlert', fraudAlertSchema);
