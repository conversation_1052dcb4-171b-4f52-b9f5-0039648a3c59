{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/config/*": ["config/*"], "@/controllers/*": ["controllers/*"], "@/models/*": ["models/*"], "@/services/*": ["services/*"], "@/middleware/*": ["middleware/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"], "@/routes/*": ["routes/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}