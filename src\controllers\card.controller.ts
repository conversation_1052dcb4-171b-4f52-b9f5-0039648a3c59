import { FastifyReply } from 'fastify';
import { CardService } from '../services/card.service';
import { AuthenticatedRequest } from '../types';
import { logger } from '../config/logger';
import { CardType, CardBrand } from '../models/card.model';
import { CardExportService } from '../services/card-export.service';


export const createCard = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { cardType, cardBrand, walletId, deliveryAddress } = request.body as any;

    if (!cardType || !walletId) {
      return reply.status(400).send({
        success: false,
        message: 'card type and wallet id required'
      });
    }

    const card = await CardService.createCard({
      userId: request.user.id,
      walletId: walletId,
      cardType: cardType as CardType,
      cardBrand: cardBrand as Card<PERSON><PERSON>,
      deliveryAddress: deliveryAddress
    });

    return reply.status(201).send({
      success: true,
      message: 'card created successfully',
      data: {
        cardId: card._id,
        cardType: card.card_type,
        cardBrand: card.card_brand,
        status: card.status,
        maskedNumber: `****-****-****-${card.card_number.slice(-4)}`,
        holderName: card.card_holder_name,
        expiry: `${card.expiry_month.toString().padStart(2, '0')}/${card.expiry_year}`,
        currency: card.currency,
        limits: card.limits,
        settings: card.settings,
        createdAt: card.created_at
      }
    });
  } catch (error: any) {
    logger.error('Error creating card:', error);
    return reply.status(500).send({
      success: false,
      message: error.message || 'failed to create card'
    });
  }
};

export const getUserCards = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const cards = await CardService.getUserCards(request.user.id);

    const cardData = cards.map(card => ({
      cardId: card._id,
      cardType: card.card_type,
      cardBrand: card.card_brand,
      status: card.status,
      maskedNumber: `****-****-****-${card.card_number?.slice(-4) || '****'}`,
      holderName: card.card_holder_name,
      expiry: `${card.expiry_month.toString().padStart(2, '0')}/${card.expiry_year}`,
      currency: card.currency,
      limits: card.limits,
      settings: card.settings,
      security: {
        isLocked: card.security.is_locked,
        lockReason: card.security.lock_reason
      },
      usageStats: card.usage_stats,
      createdAt: card.created_at
    }));

    return reply.status(200).send({
      success: true,
      message: 'cards retrieved successfully',
      data: {
        cards: cardData,
        totalCards: cardData.length
      }
    });
  } catch (error: any) {
    logger.error('Error getting user cards:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to get cards'
    });
  }
};

export const getCardDetails = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { cardId } = request.params as any;

    const card = await CardService.getCardDetails(cardId, request.user.id);
    if (!card) {
      return reply.status(404).send({
        success: false,
        message: 'card not found'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'card details retrieved successfully',
      data: {
        cardId: card._id,
        cardType: card.card_type,
        cardBrand: card.card_brand,
        status: card.status,
        maskedNumber: `****-****-****-${card.card_number?.slice(-4) || '****'}`,
        holderName: card.card_holder_name,
        expiry: `${card.expiry_month.toString().padStart(2, '0')}/${card.expiry_year}`,
        currency: card.currency,
        limits: card.limits,
        settings: card.settings,
        security: card.security,
        usageStats: card.usage_stats,
        deliveryInfo: card.delivery_info,
        createdAt: card.created_at
      }
    });
  } catch (error: any) {
    logger.error('Error getting card details:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to get card details'
    });
  }
};

export const updateCardLimits = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { cardId } = request.params as any;
    const { limits } = request.body as any;

    if (!limits) {
      return reply.status(400).send({
        success: false,
        message: 'limits data required'
      });
    }

    const success = await CardService.updateCardLimits(cardId, request.user.id, limits);
    if (!success) {
      return reply.status(404).send({
        success: false,
        message: 'card not found or update failed'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'card limits updated successfully',
      data: { limits }
    });
  } catch (error: any) {
    logger.error('Error updating card limits:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to update card limits'
    });
  }
};

export const updateCardSettings = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { cardId } = request.params as any;
    const { settings } = request.body as any;

    if (!settings) {
      return reply.status(400).send({
        success: false,
        message: 'settings data required'
      });
    }

    const success = await CardService.updateCardSettings(cardId, request.user.id, settings);
    if (!success) {
      return reply.status(404).send({
        success: false,
        message: 'card not found or update failed'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'card settings updated successfully',
      data: { settings }
    });
  } catch (error: any) {
    logger.error('Error updating card settings:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to update card settings'
    });
  }
};

export const lockCard = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { cardId } = request.params as any;
    const { reason = 'user requested' } = request.body as any;

    const success = await CardService.lockCard(cardId, request.user.id, reason);
    if (!success) {
      return reply.status(404).send({
        success: false,
        message: 'card not found or lock failed'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'card locked successfully',
      data: { reason }
    });
  } catch (error: any) {
    logger.error('Error locking card:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to lock card'
    });
  }
};

export const unlockCard = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { cardId } = request.params as any;

    const success = await CardService.unlockCard(cardId, request.user.id);
    if (!success) {
      return reply.status(404).send({
        success: false,
        message: 'card not found or unlock failed'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'card unlocked successfully'
    });
  } catch (error: any) {
    logger.error('Error unlocking card:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to unlock card'
    });
  }
};

export const blockCard = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { cardId } = request.params as any;
    const { reason = 'user requested block' } = request.body as any;

    const success = await CardService.blockCard(cardId, request.user.id, reason);
    if (!success) {
      return reply.status(404).send({
        success: false,
        message: 'card not found or block failed'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'card blocked successfully',
      data: { reason }
    });
  } catch (error: any) {
    logger.error('Error blocking card:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to block card'
    });
  }
};

export const changeCardPin = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { cardId } = request.params as any;
    const { old_pin, new_pin } = request.body as any;

    if (!old_pin || !new_pin) {
      return reply.status(400).send({
        success: false,
        message: 'old pin and new pin required'
      });
    }

    if (new_pin.length !== 4 || !/^\d{4}$/.test(new_pin)) {
      return reply.status(400).send({
        success: false,
        message: 'pin must be 4 digits'
      });
    }

    const success = await CardService.changeCardPin(cardId, request.user.id, old_pin, new_pin);
    if (!success) {
      return reply.status(400).send({
        success: false,
        message: 'invalid old pin or card not found'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'card pin changed successfully'
    });
  } catch (error: any) {
    logger.error('Error changing card pin:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to change card pin'
    });
  }
};

export const toggleBalanceVisibility = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { cardId } = request.params as any;

    if (!cardId) {
      return reply.status(400).send({
        success: false,
        message: 'card id required'
      });
    }

    const result = await CardService.toggleBalanceVisibility(cardId, request.user.id);

    if (!result.success) {
      return reply.status(404).send(result);
    }

    logger.info('Balance visibility toggled', {
      userId: request.user.id,
      cardId,
      newVisibility: result.data?.balanceVisible
    });

    return reply.status(200).send({
      success: true,
      message: result.message,
      data: result.data
    });

  } catch (error: any) {
    logger.error('Error toggling balance visibility:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to toggle balance visibility'
    });
  }
};

export const getCardDisplayData = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { cardId } = request.params as any;

    if (!cardId) {
      return reply.status(400).send({
        success: false,
        message: 'card id required'
      });
    }

    const result = await CardService.getCardDisplayData(cardId, request.user.id);

    if (!result.success) {
      return reply.status(404).send(result);
    }

    return reply.status(200).send({
      success: true,
      message: result.message,
      data: result.data,
      metadata: {
        generatedAt: new Date().toISOString(),
        cardId
      }
    });

  } catch (error: any) {
    logger.error('Error getting card display data:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve card display data'
    });
  }
};

export const getCopyableCardData = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { cardId } = request.params as any;
    const { pin } = request.body as any;

    if (!cardId || !pin) {
      return reply.status(400).send({
        success: false,
        message: 'card id and transaction pin required'
      });
    }

    const result = await CardService.getCopyableCardData(cardId, request.user.id, pin);

    if (!result.success) {
      return reply.status(result.message === 'invalid transaction pin' ? 401 : 404).send(result);
    }

    return reply.status(200).send({
      success: true,
      message: result.message,
      data: result.data,
      metadata: {
        accessedAt: new Date().toISOString(),
        cardId,
        securityNote: 'This data is sensitive and should be handled securely'
      }
    });

  } catch (error: any) {
    logger.error('Error getting copyable card data:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve card data'
    });
  }
};

export const updateCardTheme = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { cardId } = request.params as any;
    const { theme } = request.body as any;

    if (!cardId || !theme) {
      return reply.status(400).send({
        success: false,
        message: 'card id and theme required'
      });
    }

    const result = await CardService.updateCardTheme(cardId, request.user.id, theme);

    if (!result.success) {
      return reply.status(400).send(result);
    }

    return reply.status(200).send({
      success: true,
      message: result.message,
      data: result.data
    });

  } catch (error: any) {
    logger.error('Error updating card theme:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to update card theme'
    });
  }
};

export const getUserCardsEnhanced = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const cards = await CardService.getUserCards(request.user.id);

    return reply.status(200).send({
      success: true,
      message: 'user cards retrieved successfully',
      data: {
        cards,
        totalCards: cards.length
      },
      metadata: {
        userId: request.user.id,
        generatedAt: new Date().toISOString(),
        totalCards: cards.length
      }
    });

  } catch (error: any) {
    logger.error('Error getting user cards:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve user cards'
    });
  }
};




export const exportUserCardData = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { pin, format = 'json' } = request.body as any;

    if (!pin) {
      return reply.status(400).send({
        success: false,
        message: 'transaction pin is required'
      });
    }

    if (!['json', 'csv'].includes(format)) {
      return reply.status(400).send({
        success: false,
        message: 'invalid format. supported formats: json, csv'
      });
    }

    const result = await CardExportService.exportUserCardData(request.user.id, pin, format);

    if (!result.success) {
      const statusCode = result.message === 'invalid transaction pin' ? 401 : 400;
      return reply.status(statusCode).send(result);
    }

    logger.info('User card data export initiated', {
      userId: request.user.id,
      format,
      cardCount: result.data?.cardCount
    });

    return reply.status(200).send({
      success: true,
      message: result.message,
      data: result.data,
      metadata: {
        userId: request.user.id,
        exportedAt: new Date().toISOString(),
        securityNote: 'This export contains sensitive card information. Handle securely and delete after use.'
      }
    });

  } catch (error: any) {
    logger.error('Error exporting user card data:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to export card data'
    });
  }
};

export const downloadCardExport = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { filename } = request.params as any;

    if (!filename) {
      return reply.status(400).send({
        success: false,
        message: 'filename is required'
      });
    }

    const result = await CardExportService.getCardExportFile(filename, request.user.id);

    if (!result.success) {
      const statusCode = result.message === 'unauthorized access to export file' ? 403 : 404;
      return reply.status(statusCode).send(result);
    }

    logger.info('Card export file downloaded', {
      userId: request.user.id,
      filename,
      fileSize: result.data?.size
    });

    reply.header('Content-Type', result.data?.mimeType);
    reply.header('Content-Disposition', `attachment; filename="${filename}"`);
    reply.header('Content-Length', result.data?.size);

    return reply.send(result.data?.content);

  } catch (error: any) {
    logger.error('Error downloading card export file:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to download export file'
    });
  }
};

export const getUserCardSummary = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const result = await CardExportService.getUserCardSummary(request.user.id);

    if (!result.success) {
      return reply.status(500).send(result);
    }

    return reply.status(200).send({
      success: true,
      message: result.message,
      data: result.data,
      metadata: {
        userId: request.user.id,
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error getting user card summary:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve card summary'
    });
  }
};

