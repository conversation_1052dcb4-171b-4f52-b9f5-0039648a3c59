import mongoose, { Schema, Document } from 'mongoose';

export interface ISystemConfig extends Document {
  _id: mongoose.Types.ObjectId;
  config_key: string;
  config_value: any;
  config_type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description: string;
  category: string;
  is_active: boolean;
  created_by: mongoose.Types.ObjectId;
  updated_by: mongoose.Types.ObjectId;
  created_at: Date;
  updated_at: Date;
}

const systemConfigSchema = new Schema<ISystemConfig>({
  config_key: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    
  },
  config_value: {
    type: Schema.Types.Mixed,
    required: true
  },
  config_type: {
    type: String,
    enum: ['string', 'number', 'boolean', 'object', 'array'],
    required: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  category: {
    type: String,
    required: true,
    trim: true,
    
  },
  is_active: {
    type: Boolean,
    default: true,
    
  },
  created_by: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updated_by: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  created_at: {
    type: Date,
    default: Date.now,
    
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  toJSON: {
    transform: function(_doc: any, ret: any) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

systemConfigSchema.index({ config_key: 1, is_active: 1 });
systemConfigSchema.index({ category: 1, is_active: 1 });
systemConfigSchema.pre('save', function(next) {
  this.updated_at = new Date();
  next();
});

interface ISystemConfigModel extends mongoose.Model<ISystemConfig> {
  getConfig(key: string): Promise<any>;
  setConfig(key: string, value: any, updatedBy: string): Promise<ISystemConfig | null>;
  getConfigsByCategory(category: string): Promise<ISystemConfig[]>;
}

systemConfigSchema.statics.getConfig = async function(key: string) {
  const config = await this.findOne({ config_key: key, is_active: true });
  return config ? config.config_value : null;
};

systemConfigSchema.statics.setConfig = async function(key: string, value: any, updatedBy: string) {
  return this.findOneAndUpdate(
    { config_key: key },
    {
      config_value: value,
      updated_by: updatedBy,
      updated_at: new Date()
    },
    { new: true, upsert: false }
  );
};

systemConfigSchema.statics.getConfigsByCategory = async function(category: string) {
  return this.find({ category, is_active: true }).sort({ config_key: 1 });
};

export const SystemConfig = mongoose.model<ISystemConfig, ISystemConfigModel>('SystemConfig', systemConfigSchema);
