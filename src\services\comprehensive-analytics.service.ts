import { User } from '../models/user.model';
import { Transaction } from '../models/transaction.model';
import { Agent } from '../models/agent.model';
import { Wallet } from '../models/wallet.model';
// import { Card } from '../models/card.model';
// import { Savings } from '../models/savings.model';
// import { Loan } from '../models/loan.model';
import { logger } from '../config/logger';
import { Platform } from '../types';
import mongoose from 'mongoose';

export class ComprehensiveAnalyticsService {
  
  static async getUserDashboardAnalytics(userId: string, platform: Platform = Platform.WEB): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      const user = await User.findById(userId)
        .populate('main_wallet_id')
        .lean();

      if (!user) {
        return {
          success: false,
          message: 'user not found'
        };
      }

      const [
        walletInfo,
        transactionSummary,
        cardInfo,
        savingsPortfolio,
        loanPortfolio,
        quickActions,
        notifications
      ] = await Promise.all([
        this.getUserWalletInfo(userId),
        this.getUserTransactionSummary(userId),
        this.getUserCardInfo(userId),
        this.getUserSavingsPortfolio(userId),
        this.getUserLoanPortfolio(userId),
        this.getQuickActions(user, platform),
        this.getNotificationsSummary(userId)
      ]);

      const dashboardData = {
        userInfo: {
          id: user._id.toString(),
          firstName: user.first_name,
          lastName: user.last_name,
          email: user.email,
          phone: user.phone,
          isVerified: user.is_verified,
          kycStatus: user.kyc_status,
          accountStatus: user.account_status
        },
        walletInfo,
        transactionSummary,
        cardInfo,
        savingsPortfolio,
        loanPortfolio,
        quickActions,
        notifications,
        systemInfo: {
          lastUpdated: new Date().toISOString(),
          platform,
          version: '1.0.0'
        }
      };

      return {
        success: true,
        data: dashboardData,
        message: 'user dashboard analytics retrieved successfully'
      };
    } catch (error: any) {
      logger.error('Error getting user dashboard analytics:', error);
      return {
        success: false,
        message: 'failed to retrieve user dashboard analytics'
      };
    }
  }


  static async getAdminDashboardAnalytics(timeRange: string = '30d'): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      const startDate = this.getStartDate(timeRange);

      const [
        userStats,
        transactionStats,
        agentStats,
        revenueStats,
        systemHealth
      ] = await Promise.all([
        this.getUserStatistics(startDate),
        this.getTransactionStatistics(startDate),
        this.getAgentStatistics(startDate),
        this.getRevenueStatistics(startDate),
        this.getSystemHealthMetrics()
      ]);

      return {
        success: true,
        data: {
          overview: {
            totalUsers: userStats.total,
            totalTransactions: transactionStats.total,
            totalAgents: agentStats.total,
            totalRevenue: revenueStats.total
          },
          userStats,
          transactionStats,
          agentStats,
          revenueStats,
          systemHealth,
          timeRange,
          generatedAt: new Date().toISOString()
        },
        message: 'admin dashboard analytics retrieved successfully'
      };
    } catch (error: any) {
      logger.error('Error getting admin dashboard analytics:', error);
      return {
        success: false,
        message: 'failed to retrieve admin dashboard analytics'
      };
    }
  }

  static async getTransactionAnalytics(options: {
    timeRange?: string;
    type?: string;
    status?: string;
    userId?: string;
    agentId?: string;
  } = {}): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      const { timeRange = '30d', type, status, userId, agentId } = options;
      const startDate = this.getStartDate(timeRange);

      const query: any = { created_at: { $gte: startDate } };
      if (type) query.type = type;
      if (status) query.status = status;
      if (userId) query.user_id = userId;
      if (agentId) query['metadata.agentId'] = agentId;

      const [
        volumeData,
        trendData,
        channelData,
        statusBreakdown,
        topTransactions
      ] = await Promise.all([
        this.getTransactionVolumeData(query),
        this.getTransactionTrendData(query),
        this.getTransactionChannelData(query),
        this.getTransactionStatusBreakdown(query),
        this.getTopTransactions(query, 10)
      ]);

      return {
        success: true,
        data: {
          volume: volumeData,
          trends: trendData,
          channels: channelData,
          statusBreakdown,
          topTransactions,
          timeRange,
          filters: { type, status, userId, agentId }
        },
        message: 'transaction analytics retrieved successfully'
      };
    } catch (error: any) {
      logger.error('Error getting transaction analytics:', error);
      return {
        success: false,
        message: 'failed to retrieve transaction analytics'
      };
    }
  }

  static async getAgentPerformanceAnalytics(agentId?: string, timeRange: string = '30d'): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      const startDate = this.getStartDate(timeRange);
      const query: any = { created_at: { $gte: startDate } };
      
      if (agentId) {
        query['metadata.agentId'] = agentId;
      }

      const [
        performanceMetrics,
        commissionData,
        transactionStats,
        topAgents,
        agentDistribution
      ] = await Promise.all([
        this.getAgentPerformanceMetrics(query, agentId),
        this.getAgentCommissionData(query, agentId),
        this.getAgentTransactionStats(query, agentId),
        agentId ? null : this.getTopPerformingAgents(startDate, 10),
        agentId ? null : this.getAgentDistribution()
      ]);

      return {
        success: true,
        data: {
          performance: performanceMetrics,
          commission: commissionData,
          transactions: transactionStats,
          topAgents,
          distribution: agentDistribution,
          timeRange,
          agentId
        },
        message: 'agent performance analytics retrieved successfully'
      };
    } catch (error: any) {
      logger.error('Error getting agent performance analytics:', error);
      return {
        success: false,
        message: 'failed to retrieve agent performance analytics'
      };
    }
  }

  // Revenue Analytics
  static async getRevenueAnalytics(timeRange: string = '30d'): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      const startDate = this.getStartDate(timeRange);

      const [
        revenueBreakdown,
        revenueGrowth,
        feeAnalysis,
        profitMargins
      ] = await Promise.all([
        this.getRevenueBreakdown(startDate),
        this.getRevenueGrowth(startDate),
        this.getFeeAnalysis(startDate),
        this.getProfitMargins(startDate)
      ]);

      return {
        success: true,
        data: {
          breakdown: revenueBreakdown,
          growth: revenueGrowth,
          fees: feeAnalysis,
          margins: profitMargins,
          timeRange
        },
        message: 'revenue analytics retrieved successfully'
      };
    } catch (error: any) {
      logger.error('Error getting revenue analytics:', error);
      return {
        success: false,
        message: 'failed to retrieve revenue analytics'
      };
    }
  }

  static async getUserAnalytics(timeRange: string = '30d'): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      const startDate = this.getStartDate(timeRange);

      const [
        userGrowth,
        userSegmentation,
        userActivity,
        kycStats,
        retentionMetrics
      ] = await Promise.all([
        this.getUserGrowth(startDate),
        this.getUserSegmentation(),
        this.getUserActivity(startDate),
        this.getKycStatistics(),
        this.getUserRetentionMetrics(startDate)
      ]);

      return {
        success: true,
        data: {
          growth: userGrowth,
          segmentation: userSegmentation,
          activity: userActivity,
          kyc: kycStats,
          retention: retentionMetrics,
          timeRange
        },
        message: 'user analytics retrieved successfully'
      };
    } catch (error: any) {
      logger.error('Error getting user analytics:', error);
      return {
        success: false,
        message: 'failed to retrieve user analytics'
      };
    }
  }

  // Helper Methods
  private static getStartDate(timeRange: string): Date {
    const now = new Date();
    switch (timeRange) {
      case '7d': return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      case '30d': return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      case '90d': return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      case '1y': return new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      default: return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }
  }

  private static async getUserWalletInfo(userId: string): Promise<any> {
    try {
      const wallets = await Wallet.find({ user_id: userId, status: 'active' });
      const mainWallet = wallets.find(w => w.wallet_type === 'main');
      const totalBalance = wallets.reduce((sum, wallet) => sum + wallet.balance, 0);

      return {
        mainBalance: mainWallet?.balance || 0,
        totalBalance,
        currency: mainWallet?.currency || 'USD',
        balanceVisible: true,
        wallets: wallets.map(wallet => ({
          id: wallet._id.toString(),
          type: wallet.wallet_type,
          balance: wallet.balance,
          currency: wallet.currency
        }))
      };
    } catch (error: any) {
      logger.error('Error getting user wallet info:', error);
      return { mainBalance: 0, totalBalance: 0, currency: 'USD', balanceVisible: true, wallets: [] };
    }
  }

  private static async getUserTransactionSummary(userId: string): Promise<any> {
    try {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      
      const transactions = await Transaction.find({
        user_id: userId,
        created_at: { $gte: thirtyDaysAgo },
        status: 'completed'
      }).sort({ created_at: -1 }).limit(10);

      const breakdown = await Transaction.aggregate([
        {
          $match: {
            user_id: new mongoose.Types.ObjectId(userId),
            created_at: { $gte: thirtyDaysAgo },
            status: 'completed'
          }
        },
        {
          $group: {
            _id: '$type',
            count: { $sum: 1 },
            amount: { $sum: '$amount' }
          }
        }
      ]);

      const totalTransactions = breakdown.reduce((sum, item) => sum + item.count, 0);
      const totalAmount = breakdown.reduce((sum, item) => sum + item.amount, 0);

      const stats = {
        totalTransactions,
        totalAmount: Math.round(totalAmount * 100) / 100,
        breakdown: breakdown.map(item => ({
          type: item._id,
          count: item.count,
          amount: Math.round(item.amount * 100) / 100,
          percentage: totalTransactions > 0 ? Math.round((item.count / totalTransactions) * 100) : 0
        }))
      };

      const recentTransactions = transactions.map(txn => ({
        id: txn._id.toString(),
        type: txn.type,
        amount: Math.round(txn.amount * 100) / 100,
        currency: txn.currency,
        status: txn.status,
        description: txn.description,
        createdAt: txn.created_at,
        reference: txn.external_reference || txn._id.toString()
      }));

      return { stats, breakdown: stats.breakdown, recentTransactions };
    } catch (error: any) {
      logger.error('Error getting user transaction summary:', error);
      return { stats: { totalTransactions: 0, totalAmount: 0, breakdown: [] }, recentTransactions: [] };
    }
  }

  private static async getUserCardInfo(userId: string): Promise<any> {
    try {
      const { Card } = await import('../models/card.model');
      const cards = await Card.find({ user_id: userId, status: 'active' });

      if (!cards.length) {
        return { hasCard: false, cardNumber: null, cardHolder: null, expiryDate: null, cardType: null, status: null, balanceVisible: true, tierInfo: null };
      }

      const primaryCard = cards[0];
      if (!primaryCard) {
        return { hasCard: false, cardNumber: null, cardHolder: null, expiryDate: null, cardType: null, status: null, balanceVisible: true, tierInfo: null };
      }

      return {
        hasCard: true,
        cardNumber: primaryCard.masked_number,
        cardHolder: primaryCard.card_holder_name,
        expiryDate: primaryCard.expiry_date,
        cardType: primaryCard.card_type,
        status: primaryCard.status,
        balanceVisible: primaryCard.ui_preferences?.show_balance || true,
        tierInfo: primaryCard.tier_info,
        theme: primaryCard.ui_preferences?.card_theme || 'default'
      };
    } catch (error: any) {
      logger.error('Error getting user card info:', error);
      return { hasCard: false, cardNumber: null, cardHolder: null, expiryDate: null, cardType: null, status: null, balanceVisible: true, tierInfo: null };
    }
  }

  private static async getUserSavingsPortfolio(userId: string): Promise<any> {
    try {
      const { Savings } = await import('../models/savings.model');
      const savings = await Savings.find({ user_id: userId, status: 'active' });

      const totalSavings = savings.reduce((sum, saving) => sum + saving.current_balance, 0);
      const totalInterest = savings.reduce((sum, saving) => sum + saving.total_interest_earned, 0);

      return {
        totalSavings: Math.round(totalSavings * 100) / 100,
        totalInterest: Math.round(totalInterest * 100) / 100,
        activePlans: savings.length,
        plans: savings.map(saving => ({
          id: saving._id.toString(),
          name: saving.plan_name,
          balance: Math.round(saving.current_balance * 100) / 100,
          interestRate: saving.interest_rate,
          maturityDate: saving.maturity_date
        }))
      };
    } catch (error: any) {
      logger.error('Error getting user savings portfolio:', error);
      return { totalSavings: 0, totalInterest: 0, activePlans: 0, plans: [] };
    }
  }

  private static async getUserLoanPortfolio(userId: string): Promise<any> {
    try {
      const { Loan } = await import('../models/loan.model');
      const loans = await Loan.find({ user_id: userId, status: { $in: ['active', 'approved'] } });

      const totalLoanAmount = loans.reduce((sum, loan) => sum + loan.loan_amount, 0);
      const totalOutstanding = loans.reduce((sum, loan) => sum + loan.outstanding_balance, 0);

      return {
        totalLoanAmount: Math.round(totalLoanAmount * 100) / 100,
        totalOutstanding: Math.round(totalOutstanding * 100) / 100,
        activeLoans: loans.length,
        loans: loans.map(loan => ({
          id: loan._id.toString(),
          amount: Math.round(loan.loan_amount * 100) / 100,
          outstanding: Math.round(loan.outstanding_balance * 100) / 100,
          interestRate: loan.interest_rate,
          nextPaymentDate: loan.next_payment_date,
          status: loan.status
        }))
      };
    } catch (error: any) {
      logger.error('Error getting user loan portfolio:', error);
      return { totalLoanAmount: 0, totalOutstanding: 0, activeLoans: 0, loans: [] };
    }
  }

  private static getQuickActions(user: any, platform: Platform): any[] {
    const actions = [
      { action: 'sendMoney', label: 'Send Money', available: user.transaction_pin_set, icon: 'send' },
      { action: 'addMoney', label: 'Add Money', available: true, icon: 'plus' },
      { action: 'payBills', label: 'Pay Bills', available: user.transaction_pin_set, icon: 'file-text' },
      { action: 'savings', label: 'Savings', available: true, icon: 'piggy-bank' },
      { action: 'loans', label: 'Loans', available: user.kyc_status === 'approved', icon: 'dollar-sign' }
    ];

    if (platform === Platform.APP) {
      actions.push({ action: 'scanQr', label: 'Scan QR', available: true, icon: 'qr-code' });
    }

    if (platform === Platform.WEB) {
      actions.push(
        { action: 'requestMoney', label: 'Request Money', available: true, icon: 'download' },
        { action: 'cardManagement', label: 'My Card', available: true, icon: 'credit-card' }
      );
    }

    return actions.filter(action => action.available);
  }

  private static async getNotificationsSummary(userId: string): Promise<any> {
    try {
      const { Notification } = await import('../models/notification.model');
      const unreadCount = await Notification.countDocuments({ user_id: userId, status: 'unread' });
      const recentNotifications = await Notification.find({ user_id: userId })
        .sort({ created_at: -1 })
        .limit(5)
        .select('title message category priority created_at');

      return {
        unreadCount,
        recent: recentNotifications.map(notif => ({
          id: notif._id.toString(),
          title: notif.title,
          message: notif.message,
          category: notif.category,
          priority: notif.priority,
          createdAt: notif.created_at
        }))
      };
    } catch (error: any) {
      logger.error('Error getting notifications summary:', error);
      return { unreadCount: 0, recent: [] };
    }
  }

  private static async getUserStatistics(startDate: Date): Promise<any> {
    try {
      const [totalUsers, newUsers, activeUsers, verifiedUsers] = await Promise.all([
        User.countDocuments(),
        User.countDocuments({ created_at: { $gte: startDate } }),
        User.countDocuments({ last_login: { $gte: startDate } }),
        User.countDocuments({ is_verified: true })
      ]);

      return {
        total: totalUsers,
        new: newUsers,
        active: activeUsers,
        verified: verifiedUsers,
        verificationRate: totalUsers > 0 ? Math.round((verifiedUsers / totalUsers) * 100) : 0
      };
    } catch (error: any) {
      logger.error('Error getting user statistics:', error);
      return { total: 0, new: 0, active: 0, verified: 0, verificationRate: 0 };
    }
  }

  private static async getTransactionStatistics(startDate: Date): Promise<any> {
    try {
      const [totalTransactions, newTransactions, completedTransactions, totalVolume] = await Promise.all([
        Transaction.countDocuments(),
        Transaction.countDocuments({ created_at: { $gte: startDate } }),
        Transaction.countDocuments({ status: 'completed', created_at: { $gte: startDate } }),
        Transaction.aggregate([
          { $match: { status: 'completed', created_at: { $gte: startDate } } },
          { $group: { _id: null, total: { $sum: '$amount' } } }
        ])
      ]);

      const volume = totalVolume[0]?.total || 0;
      const successRate = newTransactions > 0 ? Math.round((completedTransactions / newTransactions) * 100) : 0;

      return {
        total: totalTransactions,
        new: newTransactions,
        completed: completedTransactions,
        volume: Math.round(volume * 100) / 100,
        successRate
      };
    } catch (error: any) {
      logger.error('Error getting transaction statistics:', error);
      return { total: 0, new: 0, completed: 0, volume: 0, successRate: 0 };
    }
  }

  private static async getAgentStatistics(startDate: Date): Promise<any> {
    try {
      const [totalAgents, newAgents, activeAgents, approvedAgents] = await Promise.all([
        Agent.countDocuments(),
        Agent.countDocuments({ created_at: { $gte: startDate } }),
        Agent.countDocuments({ is_active: true, last_active_date: { $gte: startDate } }),
        Agent.countDocuments({ kyc_status: 'approved' })
      ]);

      return {
        total: totalAgents,
        new: newAgents,
        active: activeAgents,
        approved: approvedAgents,
        approvalRate: totalAgents > 0 ? Math.round((approvedAgents / totalAgents) * 100) : 0
      };
    } catch (error: any) {
      logger.error('Error getting agent statistics:', error);
      return { total: 0, new: 0, active: 0, approved: 0, approvalRate: 0 };
    }
  }

  private static async getRevenueStatistics(startDate: Date): Promise<any> {
    try {
      const revenueData = await Transaction.aggregate([
        { $match: { status: 'completed', created_at: { $gte: startDate } } },
        { $group: { _id: null, totalFees: { $sum: '$fee' }, totalCommissions: { $sum: '$metadata.agentCommission' } } }
      ]);

      const data = revenueData[0] || { totalFees: 0, totalCommissions: 0 };
      const total = data.totalFees + data.totalCommissions;

      return {
        total: Math.round(total * 100) / 100,
        fees: Math.round(data.totalFees * 100) / 100,
        commissions: Math.round(data.totalCommissions * 100) / 100
      };
    } catch (error: any) {
      logger.error('Error getting revenue statistics:', error);
      return { total: 0, fees: 0, commissions: 0 };
    }
  }

  private static async getSystemHealthMetrics(): Promise<any> {
    try {
      return {
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage(),
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      logger.error('Error getting system health metrics:', error);
      return { uptime: 0, memoryUsage: {}, cpuUsage: {}, timestamp: new Date().toISOString() };
    }
  }

  private static async getTransactionVolumeData(query: any): Promise<any> {
    try {
      const volumeData = await Transaction.aggregate([
        { $match: query },
        {
          $group: {
            _id: {
              year: { $year: '$created_at' },
              month: { $month: '$created_at' },
              day: { $dayOfMonth: '$created_at' }
            },
            totalVolume: { $sum: '$amount' },
            totalFees: { $sum: '$fee' },
            transactionCount: { $sum: 1 },
            avgTransactionSize: { $avg: '$amount' }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
      ]);

      return volumeData.map(item => ({
        date: new Date(item._id.year, item._id.month - 1, item._id.day).toISOString().split('T')[0],
        volume: Math.round(item.totalVolume * 100) / 100,
        fees: Math.round(item.totalFees * 100) / 100,
        count: item.transactionCount,
        avgSize: Math.round(item.avgTransactionSize * 100) / 100
      }));
    } catch (error: any) {
      logger.error('Error getting transaction volume data:', error);
      return [];
    }
  }

  private static async getTransactionTrendData(query: any): Promise<any> {
    try {
      const trendData = await Transaction.aggregate([
        { $match: query },
        {
          $group: {
            _id: {
              date: { $dateToString: { format: '%Y-%m-%d', date: '$created_at' } },
              status: '$status'
            },
            count: { $sum: 1 },
            volume: { $sum: '$amount' }
          }
        },
        {
          $group: {
            _id: '$_id.date',
            completed: { $sum: { $cond: [{ $eq: ['$_id.status', 'completed'] }, '$count', 0] } },
            pending: { $sum: { $cond: [{ $eq: ['$_id.status', 'pending'] }, '$count', 0] } },
            failed: { $sum: { $cond: [{ $eq: ['$_id.status', 'failed'] }, '$count', 0] } },
            totalTransactions: { $sum: '$count' },
            totalVolume: { $sum: '$volume' }
          }
        },
        { $sort: { _id: 1 } }
      ]);

      return trendData.map(day => ({
        date: day._id,
        completed: day.completed,
        pending: day.pending,
        failed: day.failed,
        total: day.totalTransactions,
        volume: Math.round(day.totalVolume * 100) / 100,
        successRate: day.totalTransactions > 0 ? Math.round((day.completed / day.totalTransactions) * 100) : 0
      }));
    } catch (error: any) {
      logger.error('Error getting transaction trend data:', error);
      return [];
    }
  }

  private static async getTransactionChannelData(query: any): Promise<any> {
    try {
      const channelData = await Transaction.aggregate([
        { $match: query },
        {
          $group: {
            _id: '$metadata.channel',
            transactionCount: { $sum: 1 },
            totalVolume: { $sum: '$amount' },
            totalFees: { $sum: '$fee' },
            avgTransactionSize: { $avg: '$amount' },
            uniqueUsers: { $addToSet: '$user_id' }
          }
        },
        { $sort: { totalVolume: -1 } }
      ]);

      const totalVolume = channelData.reduce((sum, channel) => sum + channel.totalVolume, 0);
      const totalTransactions = channelData.reduce((sum, channel) => sum + channel.transactionCount, 0);

      return channelData.map(channel => ({
        channel: channel._id || 'unknown',
        transactionCount: channel.transactionCount,
        volume: Math.round(channel.totalVolume * 100) / 100,
        fees: Math.round(channel.totalFees * 100) / 100,
        avgTransactionSize: Math.round(channel.avgTransactionSize * 100) / 100,
        uniqueUsers: channel.uniqueUsers.length,
        volumePercentage: totalVolume > 0 ? Math.round((channel.totalVolume / totalVolume) * 100) : 0,
        transactionPercentage: totalTransactions > 0 ? Math.round((channel.transactionCount / totalTransactions) * 100) : 0
      }));
    } catch (error: any) {
      logger.error('Error getting transaction channel data:', error);
      return [];
    }
  }

  private static async getTransactionStatusBreakdown(query: any): Promise<any> {
    try {
      const statusData = await Transaction.aggregate([
        { $match: query },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
            volume: { $sum: '$amount' }
          }
        }
      ]);

      const totalTransactions = statusData.reduce((sum, status) => sum + status.count, 0);
      const totalVolume = statusData.reduce((sum, status) => sum + status.volume, 0);

      return statusData.map(status => ({
        status: status._id,
        count: status.count,
        volume: Math.round(status.volume * 100) / 100,
        percentage: totalTransactions > 0 ? Math.round((status.count / totalTransactions) * 100) : 0,
        volumePercentage: totalVolume > 0 ? Math.round((status.volume / totalVolume) * 100) : 0
      }));
    } catch (error: any) {
      logger.error('Error getting transaction status breakdown:', error);
      return [];
    }
  }

  private static async getTopTransactions(query: any, limit: number): Promise<any> {
    try {
      const topTransactions = await Transaction.find(query)
        .populate('user_id', 'first_name last_name email')
        .sort({ amount: -1 })
        .limit(limit)
        .lean();

      return topTransactions.map(txn => ({
        id: txn._id.toString(),
        type: txn.type,
        amount: Math.round(txn.amount * 100) / 100,
        currency: txn.currency,
        status: txn.status,
        user: (txn.user_id as any) ? {
          name: `${(txn.user_id as any).first_name} ${(txn.user_id as any).last_name}`,
          email: (txn.user_id as any).email
        } : null,
        createdAt: txn.created_at,
        reference: txn.external_reference || txn._id.toString()
      }));
    } catch (error: any) {
      logger.error('Error getting top transactions:', error);
      return [];
    }
  }

  private static async getAgentPerformanceMetrics(_query: any, agentId?: string): Promise<any> {
    try {
      if (agentId) {
        const agent = await Agent.findById(agentId);
        if (!agent) return {};

        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

        const [monthlyStats, weeklyStats, dailyStats] = await Promise.all([
          Transaction.aggregate([
            { $match: { 'metadata.agentId': agent._id, created_at: { $gte: thirtyDaysAgo }, status: 'completed' } },
            { $group: { _id: null, totalTransactions: { $sum: 1 }, totalVolume: { $sum: '$amount' }, totalCommission: { $sum: '$metadata.agentCommission' } } }
          ]),
          Transaction.aggregate([
            { $match: { 'metadata.agentId': agent._id, created_at: { $gte: sevenDaysAgo }, status: 'completed' } },
            { $group: { _id: null, totalTransactions: { $sum: 1 }, totalVolume: { $sum: '$amount' }, totalCommission: { $sum: '$metadata.agentCommission' } } }
          ]),
          Transaction.aggregate([
            { $match: { 'metadata.agentId': agent._id, created_at: { $gte: new Date(new Date().setHours(0, 0, 0, 0)) }, status: 'completed' } },
            { $group: { _id: null, totalTransactions: { $sum: 1 }, totalVolume: { $sum: '$amount' }, totalCommission: { $sum: '$metadata.agentCommission' } } }
          ])
        ]);

        const monthly = monthlyStats[0] || { totalTransactions: 0, totalVolume: 0, totalCommission: 0 };
        const weekly = weeklyStats[0] || { totalTransactions: 0, totalVolume: 0, totalCommission: 0 };
        const daily = dailyStats[0] || { totalTransactions: 0, totalVolume: 0, totalCommission: 0 };

        return {
          monthly: {
            transactions: monthly.totalTransactions,
            volume: Math.round(monthly.totalVolume * 100) / 100,
            commission: Math.round(monthly.totalCommission * 100) / 100
          },
          weekly: {
            transactions: weekly.totalTransactions,
            volume: Math.round(weekly.totalVolume * 100) / 100,
            commission: Math.round(weekly.totalCommission * 100) / 100
          },
          daily: {
            transactions: daily.totalTransactions,
            volume: Math.round(daily.totalVolume * 100) / 100,
            commission: Math.round(daily.totalCommission * 100) / 100
          },
          performanceRating: agent.performance_rating || 0
        };
      }

      return {};
    } catch (error: any) {
      logger.error('Error getting agent performance metrics:', error);
      return {};
    }
  }

  private static async getAgentCommissionData(_query: any, agentId?: string): Promise<any> {
    try {
      if (agentId) {
        const agent = await Agent.findById(agentId);
        if (!agent) return {};

        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const commissionBreakdown = await Transaction.aggregate([
          { $match: { 'metadata.agentId': agent._id, created_at: { $gte: thirtyDaysAgo }, status: 'completed' } },
          { $group: { _id: '$type', totalCommission: { $sum: '$metadata.agentCommission' }, transactionCount: { $sum: 1 } } },
          { $sort: { totalCommission: -1 } }
        ]);

        const totalCommission = commissionBreakdown.reduce((sum, item) => sum + item.totalCommission, 0);

        return {
          availableBalance: agent.wallet_info.commission_balance,
          totalEarned: Math.round(totalCommission * 100) / 100,
          breakdown: commissionBreakdown.map(item => ({
            type: item._id,
            commission: Math.round(item.totalCommission * 100) / 100,
            transactions: item.transactionCount,
            percentage: totalCommission > 0 ? Math.round((item.totalCommission / totalCommission) * 100) : 0
          }))
        };
      }
      return {};
    } catch (error: any) {
      logger.error('Error getting agent commission data:', error);
      return {};
    }
  }

  private static async getAgentTransactionStats(_query: any, agentId?: string): Promise<any> {
    try {
      if (agentId) {
        const stats = await Transaction.aggregate([
          { $match: { 'metadata.agentId': new mongoose.Types.ObjectId(agentId) } },
          { $group: { _id: '$status', count: { $sum: 1 }, volume: { $sum: '$amount' } } }
        ]);

        const totalTransactions = stats.reduce((sum, stat) => sum + stat.count, 0);
        const totalVolume = stats.reduce((sum, stat) => sum + stat.volume, 0);

        return {
          total: totalTransactions,
          completed: stats.find(s => s._id === 'completed')?.count || 0,
          pending: stats.find(s => s._id === 'pending')?.count || 0,
          failed: stats.find(s => s._id === 'failed')?.count || 0,
          totalVolume: Math.round(totalVolume * 100) / 100,
          successRate: totalTransactions > 0 ? Math.round(((stats.find(s => s._id === 'completed')?.count || 0) / totalTransactions) * 100) : 0
        };
      }
      return {};
    } catch (error: any) {
      logger.error('Error getting agent transaction stats:', error);
      return {};
    }
  }

  private static async getTopPerformingAgents(startDate: Date, limit: number): Promise<any> {
    try {
      const topAgents = await Transaction.aggregate([
        { $match: { 'metadata.agentId': { $exists: true }, created_at: { $gte: startDate }, status: 'completed' } },
        { $group: { _id: '$metadata.agentId', totalVolume: { $sum: '$amount' }, totalTransactions: { $sum: 1 }, totalCommission: { $sum: '$metadata.agentCommission' } } },
        { $sort: { totalVolume: -1 } },
        { $limit: limit },
        { $lookup: { from: 'agents', localField: '_id', foreignField: '_id', as: 'agent' } },
        { $unwind: '$agent' }
      ]);

      return topAgents.map(agent => ({
        id: agent._id.toString(),
        agentCode: agent.agent.agent_code,
        businessName: agent.agent.business_name,
        totalVolume: Math.round(agent.totalVolume * 100) / 100,
        totalTransactions: agent.totalTransactions,
        totalCommission: Math.round(agent.totalCommission * 100) / 100,
        performanceRating: agent.agent.performance_rating || 0
      }));
    } catch (error: any) {
      logger.error('Error getting top performing agents:', error);
      return [];
    }
  }

  private static async getAgentDistribution(): Promise<any> {
    try {
      const distribution = await Agent.aggregate([
        { $group: { _id: '$location.state', count: { $sum: 1 }, activeCount: { $sum: { $cond: ['$is_active', 1, 0] } } } },
        { $sort: { count: -1 } }
      ]);

      return distribution.map(item => ({
        state: item._id || 'Unknown',
        totalAgents: item.count,
        activeAgents: item.activeCount,
        activePercentage: item.count > 0 ? Math.round((item.activeCount / item.count) * 100) : 0
      }));
    } catch (error: any) {
      logger.error('Error getting agent distribution:', error);
      return [];
    }
  }

  private static async getRevenueBreakdown(startDate: Date): Promise<any> {
    try {
      const breakdown = await Transaction.aggregate([
        { $match: { status: 'completed', created_at: { $gte: startDate } } },
        { $group: { _id: '$type', totalFees: { $sum: '$fee' }, totalCommissions: { $sum: '$metadata.agentCommission' }, transactionCount: { $sum: 1 } } }
      ]);

      return breakdown.map(item => ({
        type: item._id,
        fees: Math.round(item.totalFees * 100) / 100,
        commissions: Math.round(item.totalCommissions * 100) / 100,
        total: Math.round((item.totalFees + item.totalCommissions) * 100) / 100,
        transactionCount: item.transactionCount
      }));
    } catch (error: any) {
      logger.error('Error getting revenue breakdown:', error);
      return [];
    }
  }

  private static async getRevenueGrowth(startDate: Date): Promise<any> {
    try {
      const growth = await Transaction.aggregate([
        { $match: { status: 'completed', created_at: { $gte: startDate } } },
        {
          $group: {
            _id: { $dateToString: { format: '%Y-%m-%d', date: '$created_at' } },
            dailyFees: { $sum: '$fee' },
            dailyCommissions: { $sum: '$metadata.agentCommission' }
          }
        },
        { $sort: { _id: 1 } }
      ]);

      return growth.map(day => ({
        date: day._id,
        fees: Math.round(day.dailyFees * 100) / 100,
        commissions: Math.round(day.dailyCommissions * 100) / 100,
        total: Math.round((day.dailyFees + day.dailyCommissions) * 100) / 100
      }));
    } catch (error: any) {
      logger.error('Error getting revenue growth:', error);
      return [];
    }
  }

  private static async getFeeAnalysis(startDate: Date): Promise<any> {
    try {
      const feeAnalysis = await Transaction.aggregate([
        { $match: { status: 'completed', created_at: { $gte: startDate } } },
        {
          $group: {
            _id: null,
            totalFees: { $sum: '$fee' },
            avgFee: { $avg: '$fee' },
            minFee: { $min: '$fee' },
            maxFee: { $max: '$fee' },
            transactionCount: { $sum: 1 }
          }
        }
      ]);

      const data = feeAnalysis[0] || { totalFees: 0, avgFee: 0, minFee: 0, maxFee: 0, transactionCount: 0 };

      return {
        totalFees: Math.round(data.totalFees * 100) / 100,
        avgFee: Math.round(data.avgFee * 100) / 100,
        minFee: Math.round(data.minFee * 100) / 100,
        maxFee: Math.round(data.maxFee * 100) / 100,
        transactionCount: data.transactionCount
      };
    } catch (error: any) {
      logger.error('Error getting fee analysis:', error);
      return { totalFees: 0, avgFee: 0, minFee: 0, maxFee: 0, transactionCount: 0 };
    }
  }

  private static async getProfitMargins(startDate: Date): Promise<any> {
    try {
      const margins = await Transaction.aggregate([
        { $match: { status: 'completed', created_at: { $gte: startDate } } },
        {
          $group: {
            _id: null,
            totalRevenue: { $sum: { $add: ['$fee', '$metadata.agentCommission'] } },
            totalCosts: { $sum: '$metadata.agentCommission' },
            transactionCount: { $sum: 1 }
          }
        }
      ]);

      const data = margins[0] || { totalRevenue: 0, totalCosts: 0, transactionCount: 0 };
      const profit = data.totalRevenue - data.totalCosts;
      const margin = data.totalRevenue > 0 ? (profit / data.totalRevenue) * 100 : 0;

      return {
        totalRevenue: Math.round(data.totalRevenue * 100) / 100,
        totalCosts: Math.round(data.totalCosts * 100) / 100,
        profit: Math.round(profit * 100) / 100,
        margin: Math.round(margin * 100) / 100
      };
    } catch (error: any) {
      logger.error('Error getting profit margins:', error);
      return { totalRevenue: 0, totalCosts: 0, profit: 0, margin: 0 };
    }
  }

  private static async getUserGrowth(startDate: Date): Promise<any> {
    try {
      const growth = await User.aggregate([
        { $match: { created_at: { $gte: startDate } } },
        {
          $group: {
            _id: { $dateToString: { format: '%Y-%m-%d', date: '$created_at' } },
            newUsers: { $sum: 1 }
          }
        },
        { $sort: { _id: 1 } }
      ]);

      return growth.map(day => ({
        date: day._id,
        newUsers: day.newUsers
      }));
    } catch (error: any) {
      logger.error('Error getting user growth:', error);
      return [];
    }
  }

  private static async getUserSegmentation(): Promise<any> {
    try {
      const segmentation = await User.aggregate([
        {
          $group: {
            _id: '$role',
            count: { $sum: 1 },
            verified: { $sum: { $cond: ['$is_verified', 1, 0] } },
            active: { $sum: { $cond: [{ $gte: ['$last_login', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)] }, 1, 0] } }
          }
        }
      ]);

      return segmentation.map(segment => ({
        role: segment._id,
        totalUsers: segment.count,
        verifiedUsers: segment.verified,
        activeUsers: segment.active,
        verificationRate: segment.count > 0 ? Math.round((segment.verified / segment.count) * 100) : 0,
        activityRate: segment.count > 0 ? Math.round((segment.active / segment.count) * 100) : 0
      }));
    } catch (error: any) {
      logger.error('Error getting user segmentation:', error);
      return [];
    }
  }

  private static async getUserActivity(startDate: Date): Promise<any> {
    try {
      const activity = await User.aggregate([
        { $match: { last_login: { $gte: startDate } } },
        {
          $group: {
            _id: { $dateToString: { format: '%Y-%m-%d', date: '$last_login' } },
            activeUsers: { $sum: 1 }
          }
        },
        { $sort: { _id: 1 } }
      ]);

      return activity.map(day => ({
        date: day._id,
        activeUsers: day.activeUsers
      }));
    } catch (error: any) {
      logger.error('Error getting user activity:', error);
      return [];
    }
  }

  private static async getKycStatistics(): Promise<any> {
    try {
      const kycStats = await User.aggregate([
        {
          $group: {
            _id: '$kyc_status',
            count: { $sum: 1 }
          }
        }
      ]);

      const total = kycStats.reduce((sum, stat) => sum + stat.count, 0);

      return kycStats.map(stat => ({
        status: stat._id,
        count: stat.count,
        percentage: total > 0 ? Math.round((stat.count / total) * 100) : 0
      }));
    } catch (error: any) {
      logger.error('Error getting KYC statistics:', error);
      return [];
    }
  }

  private static async getUserRetentionMetrics(startDate: Date): Promise<any> {
    try {
      const totalUsers = await User.countDocuments({ created_at: { $lt: startDate } });
      const activeUsers = await User.countDocuments({
        created_at: { $lt: startDate },
        last_login: { $gte: startDate }
      });

      const retentionRate = totalUsers > 0 ? Math.round((activeUsers / totalUsers) * 100) : 0;

      return {
        totalUsers,
        activeUsers,
        retentionRate,
        churnRate: 100 - retentionRate
      };
    } catch (error: any) {
      logger.error('Error getting user retention metrics:', error);
      return { totalUsers: 0, activeUsers: 0, retentionRate: 0, churnRate: 0 };
    }
  }
}
