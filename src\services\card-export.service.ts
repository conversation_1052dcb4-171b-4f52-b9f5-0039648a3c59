import { Card } from '../models/card.model';
import { User } from '../models/user.model';
import { logger } from '../config/logger';
import { CryptoUtils } from '../utils/crypto';
import * as fs from 'fs/promises';
import * as path from 'path';

export class CardExportService {
  private static readonly EXPORT_DIR = path.join(process.cwd(), 'card-exports');

  private static async ensureExportDir(): Promise<void> {
    try {
      await fs.access(this.EXPORT_DIR);
    } catch {
      await fs.mkdir(this.EXPORT_DIR, { recursive: true });
    }
  }

  static async exportUserCardData(userId: string, pin: string, format: 'json' | 'csv' = 'json'): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      await this.ensureExportDir();

      const user = await User.findById(userId);
      if (!user) {
        return {
          success: false,
          message: 'user not found'
        };
      }

      const isPinValid = await CryptoUtils.verifyPassword(pin, user.transaction_pin || '');
      if (!isPinValid) {
        logger.warn('Invalid PIN attempt for card data export', {
          userId,
          timestamp: new Date()
        });

        return {
          success: false,
          message: 'invalid transaction pin'
        };
      }

      const cards = await Card.find({ user_id: userId });

      if (!cards.length) {
        return {
          success: false,
          message: 'no cards found for user'
        };
      }

      const exportData = cards.map(card => {
        try {
          const decryptedCardNumber = CryptoUtils.decrypt(card.card_number);
          const decryptedCvv = CryptoUtils.decrypt(card.cvv);

          return {
            cardId: card._id.toString(),
            cardNumber: decryptedCardNumber,
            cardHolderName: card.card_holder_name,
            expiryDate: card.expiry_date,
            cvv: decryptedCvv,
            cardType: card.card_type,
            cardBrand: card.card_brand,
            status: card.status,
            issuedDate: card.created_at?.toISOString(),
            limits: {
              dailySpendLimit: card.limits.daily_spend_limit,
              monthlySpendLimit: card.limits.monthly_spend_limit,
              atmWithdrawalLimit: card.limits.atm_withdrawal_limit
            },
            settings: {
              internationalTransactions: card.settings.international_transactions,
              onlineTransactions: card.settings.online_transactions,
              atmWithdrawals: card.settings.atm_withdrawals,
              posTransactions: card.settings.pos_transactions,
              contactlessPayments: card.settings.contactless_payments
            },
            tierInfo: {
              tierLevel: card.tier_info?.tier_level || 'basic',
              upgradeEligible: card.tier_info?.upgrade_eligible || false
            }
          };
        } catch (error) {
          logger.error('Error decrypting card data for export:', error);
          return {
            cardId: card._id.toString(),
            cardNumber: '****-****-****-****',
            cardHolderName: card.card_holder_name,
            expiryDate: card.expiry_date,
            cvv: '***',
            cardType: card.card_type,
            cardBrand: card.card_brand,
            status: card.status,
            issuedDate: card.created_at?.toISOString(),
            error: 'Failed to decrypt sensitive data'
          };
        }
      });

      const filename = `user_cards_${userId}_${Date.now()}.${format}`;
      const filepath = path.join(this.EXPORT_DIR, filename);

      if (format === 'csv') {
        const csvContent = this.convertToCSV(exportData);
        await fs.writeFile(filepath, csvContent, 'utf8');
      } else {
        await fs.writeFile(filepath, JSON.stringify({
          exportedAt: new Date().toISOString(),
          userId,
          totalCards: exportData.length,
          cards: exportData
        }, null, 2), 'utf8');
      }

      logger.info('User card data exported', {
        userId,
        cardCount: exportData.length,
        format,
        filename
      });

      return {
        success: true,
        data: {
          filename,
          filepath,
          cardCount: exportData.length,
          format,
          downloadUrl: `/api/v1/cards/export/download/${filename}`,
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
        },
        message: 'card data exported successfully'
      };

    } catch (error: any) {
      logger.error('Error exporting user card data:', error);
      return {
        success: false,
        message: 'failed to export card data'
      };
    }
  }

  static async getCardExportFile(filename: string, userId: string): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {

      if (!filename.includes(`user_cards_${userId}_`)) {
        return {
          success: false,
          message: 'unauthorized access to export file'
        };
      }

      const filepath = path.join(this.EXPORT_DIR, filename);
      
      try {
        await fs.access(filepath);
      } catch {
        return {
          success: false,
          message: 'export file not found or expired'
        };
      }

      const stats = await fs.stat(filepath);
      const fileContent = await fs.readFile(filepath);

      const fileAge = Date.now() - stats.mtime.getTime();
      if (fileAge > 24 * 60 * 60 * 1000) {
        // delete expired file
        await fs.unlink(filepath);
        return {
          success: false,
          message: 'export file has expired'
        };
      }

      return {
        success: true,
        data: {
          filename,
          content: fileContent,
          size: stats.size,
          mimeType: this.getMimeType(filename)
        },
        message: 'card export file retrieved successfully'
      };

    } catch (error: any) {
      logger.error('Error getting card export file:', error);
      return {
        success: false,
        message: 'failed to retrieve export file'
      };
    }
  }

  static async getUserCardSummary(userId: string): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      const cards = await Card.find({ user_id: userId }).select('-card_number -cvv');

      const summary = {
        totalCards: cards.length,
        activeCards: cards.filter(card => card.status === 'active').length,
        blockedCards: cards.filter(card => card.status === 'blocked').length,
        expiredCards: cards.filter(card => card.is_expired).length,
        cardTypes: cards.reduce((acc, card) => {
          acc[card.card_type] = (acc[card.card_type] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
        cardBrands: cards.reduce((acc, card) => {
          acc[card.card_brand] = (acc[card.card_brand] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
        cards: cards.map(card => ({
          id: card._id.toString(),
          maskedNumber: card.masked_number,
          cardHolderName: card.card_holder_name,
          cardType: card.card_type,
          cardBrand: card.card_brand,
          status: card.status,
          expiryDate: card.expiry_date,
          isExpired: card.is_expired,
          tierLevel: card.tier_info?.tier_level || 'basic',
          createdAt: card.created_at
        }))
      };

      return {
        success: true,
        data: summary,
        message: 'user card summary retrieved successfully'
      };

    } catch (error: any) {
      logger.error('Error getting user card summary:', error);
      return {
        success: false,
        message: 'failed to retrieve card summary'
      };
    }
  }

  private static convertToCSV(data: any[]): string {
    if (!data.length) return '';

    const headers = Object.keys(data[0]);
    const csvRows = [headers.join(',')];

    for (const row of data) {
      const values = headers.map(header => {
        const value = row[header];
        if (typeof value === 'object' && value !== null) {
          return JSON.stringify(value).replace(/"/g, '""');
        }
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value || '';
      });
      csvRows.push(values.join(','));
    }

    return csvRows.join('\n');
  }

  private static getMimeType(filename: string): string {
    const ext = path.extname(filename).toLowerCase();
    switch (ext) {
      case '.csv': return 'text/csv';
      case '.json': return 'application/json';
      default: return 'application/octet-stream';
    }
  }

  static async cleanupExpiredExports(): Promise<void> {
    try {
      await this.ensureExportDir();
      const files = await fs.readdir(this.EXPORT_DIR);
      const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24 hours

      for (const file of files) {
        const filepath = path.join(this.EXPORT_DIR, file);
        const stats = await fs.stat(filepath);
        
        if (stats.mtime.getTime() < cutoffTime) {
          await fs.unlink(filepath);
          logger.info('Cleaned up expired card export file', { filename: file });
        }
      }
    } catch (error: any) {
      logger.error('Error cleaning up expired card exports:', error);
    }
  }
}
