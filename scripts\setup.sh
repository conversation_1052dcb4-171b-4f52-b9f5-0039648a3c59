#!/bin/bash

# AeTrust Backend Setup Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 AeTrust Backend Setup${NC}"
echo -e "${BLUE}This script will set up your development environment${NC}"

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    echo -e "${RED}❌ Please don't run this script as root${NC}"
    exit 1
fi

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check system requirements
echo -e "${YELLOW}🔍 Checking system requirements...${NC}"

# Check Node.js
if command_exists node; then
    NODE_VERSION=$(node --version)
    echo -e "${GREEN}✅ Node.js: $NODE_VERSION${NC}"
    
    # Check if version is 18 or higher
    NODE_MAJOR=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
    if [ "$NODE_MAJOR" -lt 18 ]; then
        echo -e "${YELLOW}⚠️ Node.js 18+ recommended, you have $NODE_VERSION${NC}"
    fi
else
    echo -e "${RED}❌ Node.js not found${NC}"
    echo -e "${YELLOW}Please install Node.js 18+ from https://nodejs.org${NC}"
    exit 1
fi

# Check npm
if command_exists npm; then
    NPM_VERSION=$(npm --version)
    echo -e "${GREEN}✅ npm: $NPM_VERSION${NC}"
else
    echo -e "${RED}❌ npm not found${NC}"
    exit 1
fi

# Check Docker
if command_exists docker; then
    DOCKER_VERSION=$(docker --version)
    echo -e "${GREEN}✅ Docker: $DOCKER_VERSION${NC}"
    
    # Check if Docker daemon is running
    if ! docker info > /dev/null 2>&1; then
        echo -e "${YELLOW}⚠️ Docker daemon is not running${NC}"
        echo -e "${YELLOW}Please start Docker and run this script again${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ Docker not found${NC}"
    echo -e "${YELLOW}Please install Docker from https://docker.com${NC}"
    exit 1
fi

# Check Docker Compose
if command_exists docker-compose; then
    COMPOSE_VERSION=$(docker-compose --version)
    echo -e "${GREEN}✅ Docker Compose: $COMPOSE_VERSION${NC}"
else
    echo -e "${RED}❌ Docker Compose not found${NC}"
    echo -e "${YELLOW}Please install Docker Compose${NC}"
    exit 1
fi

# Check Git
if command_exists git; then
    GIT_VERSION=$(git --version)
    echo -e "${GREEN}✅ Git: $GIT_VERSION${NC}"
else
    echo -e "${YELLOW}⚠️ Git not found (optional but recommended)${NC}"
fi

echo -e "${GREEN}✅ All system requirements met${NC}"

# Create necessary directories
echo -e "${YELLOW}📁 Creating project directories...${NC}"
mkdir -p logs
mkdir -p backups
mkdir -p ssl
mkdir -p config/nginx
mkdir -p config/grafana/provisioning
mkdir -p tests/load
mkdir -p scripts

echo -e "${GREEN}✅ Directories created${NC}"

# Install Node.js dependencies
echo -e "${YELLOW}📦 Installing Node.js dependencies...${NC}"
npm install

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Dependencies installed${NC}"
else
    echo -e "${RED}❌ Failed to install dependencies${NC}"
    exit 1
fi

# Create environment file if it doesn't exist
if [ ! -f .env ]; then
    echo -e "${YELLOW}⚙️ Creating environment file...${NC}"
    cp .env.example .env
    echo -e "${GREEN}✅ Environment file created${NC}"
    echo -e "${YELLOW}⚠️ Please update .env with your configuration${NC}"
else
    echo -e "${BLUE}ℹ️ Environment file already exists${NC}"
fi

# Generate SSL certificates for development
if [ ! -f ssl/cert.pem ]; then
    echo -e "${YELLOW}🔐 Generating SSL certificates for development...${NC}"
    
    if command_exists openssl; then
        openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem -days 365 -nodes \
            -subj "/C=RW/ST=Kigali/L=Kigali/O=AeTrust/CN=localhost"
        echo -e "${GREEN}✅ SSL certificates generated${NC}"
    else
        echo -e "${YELLOW}⚠️ OpenSSL not found, skipping SSL certificate generation${NC}"
    fi
fi

# Make scripts executable
echo -e "${YELLOW}🔧 Making scripts executable...${NC}"
chmod +x scripts/*.sh
echo -e "${GREEN}✅ Scripts made executable${NC}"

# Build the application
echo -e "${YELLOW}🔨 Building application...${NC}"
npm run build

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Application built successfully${NC}"
else
    echo -e "${RED}❌ Build failed${NC}"
    exit 1
fi

# Pull Docker images
echo -e "${YELLOW}🐳 Pulling Docker images...${NC}"
docker-compose pull

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Docker images pulled${NC}"
else
    echo -e "${YELLOW}⚠️ Some Docker images failed to pull${NC}"
fi

# Initialize database (optional)
echo -e "${YELLOW}❓ Do you want to start the development environment now? (y/N)${NC}"
read -r start_dev

if [[ $start_dev =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}🚀 Starting development environment...${NC}"
    docker-compose up -d
    
    # Wait for services to be ready
    echo -e "${YELLOW}⏳ Waiting for services to start...${NC}"
    sleep 30
    
    # Check if API is responding
    if curl -f -s "http://localhost:3000/health" > /dev/null; then
        echo -e "${GREEN}✅ API is running at http://localhost:3000${NC}"
    else
        echo -e "${YELLOW}⚠️ API might still be starting up${NC}"
    fi
    
    # Show running containers
    echo -e "${BLUE}📊 Running containers:${NC}"
    docker-compose ps
fi

# Setup complete
echo -e "${GREEN}🎉 Setup completed successfully!${NC}"
echo ""
echo -e "${BLUE}📋 Next Steps:${NC}"
echo -e "${BLUE}1. Update .env file with your configuration${NC}"
echo -e "${BLUE}2. Start development: npm run dev${NC}"
echo -e "${BLUE}3. Start with Docker: docker-compose up -d${NC}"
echo -e "${BLUE}4. Run tests: npm test${NC}"
echo -e "${BLUE}5. Deploy: ./scripts/deploy.sh production${NC}"
echo ""
echo -e "${BLUE}📚 Useful URLs:${NC}"
echo -e "${BLUE}- API: http://localhost:3000${NC}"
echo -e "${BLUE}- Health: http://localhost:3000/health${NC}"
echo -e "${BLUE}- MongoDB Admin: http://localhost:8081${NC}"
echo -e "${BLUE}- Redis Admin: http://localhost:8082${NC}"
echo ""
echo -e "${BLUE}🔧 Useful Commands:${NC}"
echo -e "${BLUE}- Start dev: npm run dev${NC}"
echo -e "${BLUE}- Build: npm run build${NC}"
echo -e "${BLUE}- Test: npm test${NC}"
echo -e "${BLUE}- Lint: npm run lint${NC}"
echo -e "${BLUE}- Deploy: ./scripts/deploy.sh [environment]${NC}"
echo -e "${BLUE}- Backup: ./scripts/backup.sh${NC}"
echo -e "${BLUE}- Restore: ./scripts/restore.sh [backup_file]${NC}"
echo ""
echo -e "${GREEN}Happy coding! 🚀${NC}"
