import { FastifyRequest, FastifyReply } from 'fastify';
import { AuthenticatedRequest } from '../types';
import { AgentService } from '../services/agent.service';
import { ComprehensiveAnalyticsService } from '../services/comprehensive-analytics.service';
import { Agent } from '../models/agent.model';
import { User } from '../models/user.model';
import { CryptoUtils } from '../utils/crypto';
import { logger } from '../config/logger';
import { v4 as uuidv4 } from 'uuid';

export const registerAgent = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required',
      errorCode: 'AUTH_REQUIRED'
    });
  }

  try {
    const data = request.body as any;

    const agent = await AgentService.registerAgent({
      userId: request.user.id,
      ...data
    });

    return reply.status(201).send({
      success: true,
      message: 'agent registration submitted successfully',
      data: {
        agentRegistration: {
          agentId: agent._id.toString(),
          agentCode: agent.agent_code,
          businessName: agent.business_name,
          businessType: agent.business_type,
          agentStatus: agent.agent_status,
          kycStatus: agent.kyc_status,
          registeredAt: agent.created_at
        },
        businessInfo: {
          businessName: agent.business_name,
          businessType: agent.business_type,
          registrationNumber: agent.business_registration_number,
          taxId: agent.tax_id
        },
        locationDetails: {
          address: agent.location.address,
          city: agent.location.city,
          state: agent.location.state,
          country: agent.location.country,
          coordinates: agent.location.coordinates
        },
        nextSteps: {
          kycVerificationRequired: true,
          approvalPending: true,
          estimatedApprovalTime: "2-3 business days"
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        timestamp: new Date().toISOString(),
        serviceType: "agent_registration"
      }
    });
  } catch (error: any) {
    logger.error('agent registration error:', error);
    
    const errorMessages: Record<string, string> = {
      'user already has an agent account': 'you already have an active agent account',
      'user not found': 'user account not found'
    };

    const message = errorMessages[error.message] || 'agent registration failed';
    
    return reply.status(400).send({
      success: false,
      message,
      errorCode: 'REGISTRATION_FAILED',
      errorDetails: {
        reason: error.message,
        timestamp: new Date().toISOString()
      }
    });
  }
};

export const performCashIn = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'agent authentication required'
    });
  }

  try {
    const data = request.body as any;

    // get agent
    const agent = await Agent.findOne({ user_id: request.user.id });
    if (!agent) {
      return reply.status(404).send({
        success: false,
        message: 'agent account not found'
      });
    }

    const result = await AgentService.performCashIn({
      agentId: agent._id.toString(),
      ...data
    });

    return reply.status(200).send({
      success: true,
      message: 'cash in transaction completed successfully',
      data: {
        transactionDetails: {
          transactionId: result.transactionId,
          transactionType: "cash_in",
          amount: data.amount,
          currency: data.currency,
          commission: result.commission,
          status: "completed",
          processedAt: new Date().toISOString()
        },
        customerInfo: {
          customerId: data.customerId,
          newBalance: result.customerBalance
        },
        agentInfo: {
          agentId: agent._id.toString(),
          newBalance: result.agentBalance,
          commissionEarned: result.commission
        },
        transactionSummary: {
          customerReceived: data.amount,
          agentCommission: result.commission,
          agentBalanceChange: -data.amount
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        processingTimeMs: Date.now() - ((request as any).startTime || Date.now()),
        transactionType: "cash_in"
      }
    });
  } catch (error: any) {
    logger.error('cash in transaction error:', error);
    
    return reply.status(400).send({
      success: false,
      message: 'cash in transaction failed',
      error: error.message,
      errorCode: 'CASH_IN_FAILED'
    });
  }
};

export const performCashOut = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'agent authentication required'
    });
  }

  try {
    const data = request.body as any;

    // get agent
    const agent = await Agent.findOne({ user_id: request.user.id });
    if (!agent) {
      return reply.status(404).send({
        success: false,
        message: 'agent account not found'
      });
    }

    const result = await AgentService.performCashOut({
      agentId: agent._id.toString(),
      ...data
    });

    return reply.status(200).send({
      success: true,
      message: 'cash out transaction completed successfully',
      data: {
        transactionDetails: {
          transactionId: result.transactionId,
          transactionType: "cash_out",
          amount: data.amount,
          currency: data.currency,
          commission: result.commission,
          status: "completed",
          processedAt: new Date().toISOString()
        },
        customerInfo: {
          customerId: data.customerId,
          newBalance: result.customerBalance
        },
        agentInfo: {
          agentId: agent._id.toString(),
          newBalance: result.agentBalance,
          commissionEarned: result.commission
        },
        transactionSummary: {
          customerWithdrew: data.amount,
          agentCommission: result.commission,
          agentBalanceChange: data.amount
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        processingTimeMs: Date.now() - ((request as any).startTime || Date.now()),
        transactionType: "cash_out"
      }
    });
  } catch (error: any) {
    logger.error('cash out transaction error:', error);
    
    return reply.status(400).send({
      success: false,
      message: 'cash out transaction failed',
      error: error.message,
      errorCode: 'CASH_OUT_FAILED'
    });
  }
};

export const getAgentProfile = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const agent = await AgentService.getAgentById(request.user.id);
    
    if (!agent) {
      return reply.status(404).send({
        success: false,
        message: 'agent profile not found'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'agent profile retrieved successfully',
      data: {
        agentProfile: {
          agentId: agent._id.toString(),
          agentCode: agent.agent_code,
          businessName: agent.business_name,
          businessType: agent.business_type,
          agentStatus: agent.agent_status,
          kycStatus: agent.kyc_status,
          registeredAt: agent.created_at
        },
        businessDetails: {
          businessName: agent.business_name,
          businessType: agent.business_type,
          registrationNumber: agent.business_registration_number,
          taxId: agent.tax_id
        },
        locationInfo: {
          address: agent.location.address,
          city: agent.location.city,
          state: agent.location.state,
          country: agent.location.country,
          fullLocation: (agent as any).full_location
        },
        contactDetails: {
          phone: agent.contact_info.phone,
          email: agent.contact_info.email,
          whatsapp: agent.contact_info.whatsapp
        },
        walletInfo: {
          availableBalance: agent.wallet_info.available_balance,
          commissionBalance: agent.wallet_info.commission_balance,
          floatBalance: agent.wallet_info.float_balance
        },
        performanceStats: {
          totalTransactions: agent.statistics.total_transactions,
          totalVolume: agent.statistics.total_volume,
          totalCommissionEarned: agent.statistics.total_commission_earned,
          cashInCount: agent.statistics.cash_in_count,
          cashOutCount: agent.statistics.cash_out_count,
          lastTransactionDate: agent.statistics.last_transaction_date,
          performanceMetrics: (agent as any).performance_metrics
        },
        commissionStructure: {
          cashInRate: `${(agent.commission_structure.cash_in_rate * 100).toFixed(2)}%`,
          cashOutRate: `${(agent.commission_structure.cash_out_rate * 100).toFixed(2)}%`,
          billPaymentRate: `${(agent.commission_structure.bill_payment_rate * 100).toFixed(2)}%`,
          transferRate: `${(agent.commission_structure.transfer_rate * 100).toFixed(2)}%`,
          minimumCommission: agent.commission_structure.minimum_commission
        }
      }
    });
  } catch (error: any) {
    logger.error('get agent profile error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve agent profile'
    });
  }
};

export const searchAgents = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const query = request.query as any;

    const result = await AgentService.searchAgents(query);

    return reply.status(200).send({
      success: true,
      message: 'agents retrieved successfully',
      data: {
        agentsList: result.agents.map(agent => ({
          agentId: agent._id.toString(),
          agentCode: agent.agent_code,
          businessName: agent.business_name,
          businessType: agent.business_type,
          agentStatus: agent.agent_status,
          kycStatus: agent.kyc_status,
          location: {
            city: agent.location.city,
            state: agent.location.state,
            country: agent.location.country
          },
          contactInfo: {
            phone: agent.contact_info.phone,
            email: agent.contact_info.email
          },
          statistics: {
            totalTransactions: agent.statistics.total_transactions,
            totalVolume: agent.statistics.total_volume,
            totalCommissionEarned: agent.statistics.total_commission_earned
          },
          registeredAt: agent.created_at
        })),
        pagination: {
          currentPage: result.page,
          totalPages: result.pages,
          totalItems: result.total,
          itemsPerPage: query.limit || 20,
          hasNext: result.page < result.pages,
          hasPrevious: result.page > 1
        }
      },
      metadata: {
        searchFilters: {
          search: query.search || null,
          status: query.status || null,
          kycStatus: query.kycStatus || null,
          city: query.city || null,
          state: query.state || null,
          businessType: query.businessType || null
        },
        resultCount: result.agents.length
      }
    });
  } catch (error: any) {
    logger.error('search agents error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'failed to search agents'
    });
  }
};

export const getAgentByCode = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const { agentCode } = request.params as any;

    const agent = await AgentService.getAgentByCode(agentCode);
    
    if (!agent) {
      return reply.status(404).send({
        success: false,
        message: 'agent not found',
        errorCode: 'AGENT_NOT_FOUND'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'agent found',
      data: {
        agentInfo: {
          agentId: agent._id.toString(),
          agentCode: agent.agent_code,
          businessName: agent.business_name,
          businessType: agent.business_type,
          agentStatus: agent.agent_status,
          kycStatus: agent.kyc_status
        },
        locationInfo: {
          address: agent.location.address,
          city: agent.location.city,
          state: agent.location.state,
          country: agent.location.country
        },
        contactInfo: {
          phone: agent.contact_info.phone,
          email: agent.contact_info.email
        },
        serviceCapabilities: {
          cashInAvailable: agent.agent_status === 'active',
          cashOutAvailable: agent.agent_status === 'active',
          billPaymentAvailable: agent.agent_status === 'active'
        }
      }
    });
  } catch (error: any) {
    logger.error('get agent by code error:', error);

    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve agent information'
    });
  }
};

// Agent Dash
export const getAgentDashboard = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const agent = await Agent.findOne({ user_id: request.user.id });

    if (!agent) {
      return reply.status(404).send({
        success: false,
        message: 'agent account not found'
      });
    }

    const reference = `AGENT_DASH_${uuidv4()}_${Date.now()}`;
    const result = await AgentService.getAgentDashboard(agent._id.toString());

    if (!result.success) {
      return reply.status(500).send(result);
    }

    logger.info('Agent dashboard retrieved', {
      agentId: agent._id.toString(),
      userId: request.user.id,
      agentCode: agent.agent_code,
      reference
    });

    return reply.status(200).send({
      success: true,
      message: result.message,
      data: result.data,
      metadata: {
        reference,
        agentId: agent._id.toString(),
        agentCode: agent.agent_code,
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error getting agent dashboard:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve agent dashboard'
    });
  }
};

export const getAgentPerformanceMetrics = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const agent = await Agent.findOne({ user_id: request.user.id });

    if (!agent) {
      return reply.status(404).send({
        success: false,
        message: 'agent account not found'
      });
    }

    const { timeRange = '30d' } = request.query as any;
    const reference = `AGENT_PERF_${uuidv4()}_${Date.now()}`;

    const result = await ComprehensiveAnalyticsService.getAgentPerformanceAnalytics(agent._id.toString(), timeRange);

    if (!result.success) {
      return reply.status(500).send(result);
    }

    return reply.status(200).send({
      success: true,
      message: result.message,
      data: result.data,
      metadata: {
        reference,
        agentId: agent._id.toString(),
        timeRange,
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error getting agent performance metrics:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve agent performance metrics'
    });
  }
};

export const getAgentCommissionData = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const agent = await Agent.findOne({ user_id: request.user.id });

    if (!agent) {
      return reply.status(404).send({
        success: false,
        message: 'agent account not found'
      });
    }

    const reference = `AGENT_COMM_${uuidv4()}_${Date.now()}`;
    const commissionData = await AgentService.getAgentCommissionData(agent._id.toString());

    return reply.status(200).send({
      success: true,
      message: 'agent commission data retrieved successfully',
      data: commissionData,
      metadata: {
        reference,
        agentId: agent._id.toString(),
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error getting agent commission data:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve agent commission data'
    });
  }
};

export const getAgentTransactionStats = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const agent = await Agent.findOne({ user_id: request.user.id });

    if (!agent) {
      return reply.status(404).send({
        success: false,
        message: 'agent account not found'
      });
    }

    const reference = `AGENT_TXN_${uuidv4()}_${Date.now()}`;
    const transactionStats = await AgentService.getAgentTransactionStats(agent._id.toString());

    return reply.status(200).send({
      success: true,
      message: 'agent transaction statistics retrieved successfully',
      data: transactionStats,
      metadata: {
        reference,
        agentId: agent._id.toString(),
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error getting agent transaction stats:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve agent transaction statistics'
    });
  }
};

export const getAgentRecentTransactions = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { limit = 10 } = request.query as any;
    const agent = await Agent.findOne({ user_id: request.user.id });

    if (!agent) {
      return reply.status(404).send({
        success: false,
        message: 'agent account not found'
      });
    }

    const reference = `AGENT_RECENT_${uuidv4()}_${Date.now()}`;
    const recentTransactions = await AgentService.getAgentRecentTransactions(agent._id.toString(), parseInt(limit));

    return reply.status(200).send({
      success: true,
      message: 'agent recent transactions retrieved successfully',
      data: {
        transactions: recentTransactions,
        count: recentTransactions.length
      },
      metadata: {
        reference,
        agentId: agent._id.toString(),
        limit: parseInt(limit),
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error getting agent recent transactions:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve agent recent transactions'
    });
  }
};

export const getAgentFloatBalance = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const agent = await Agent.findOne({ user_id: request.user.id });

    if (!agent) {
      return reply.status(404).send({
        success: false,
        message: 'agent account not found'
      });
    }

    const reference = `AGENT_FLOAT_${uuidv4()}_${Date.now()}`;
    const floatBalance = await AgentService.getAgentFloatBalance(agent._id.toString());

    return reply.status(200).send({
      success: true,
      message: 'agent float balance retrieved successfully',
      data: floatBalance,
      metadata: {
        reference,
        agentId: agent._id.toString(),
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error getting agent float balance:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve agent float balance'
    });
  }
};

export const processCashIn = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { customerPhone, amount, currency = 'USD', pin } = request.body as any;

    if (!customerPhone || !amount || !pin) {
      return reply.status(400).send({
        success: false,
        message: 'customer phone, amount, and transaction pin are required'
      });
    }

    const agent = await Agent.findOne({ user_id: request.user.id });

    if (!agent) {
      return reply.status(404).send({
        success: false,
        message: 'agent account not found'
      });
    }

    if (!agent.is_active) {
      return reply.status(403).send({
        success: false,
        message: 'agent account is not active'
      });
    }

    const user = await User.findById(request.user.id);

    if (!user) {
      return reply.status(404).send({
        success: false,
        message: 'user not found'
      });
    }

    const isPinValid = await CryptoUtils.verifyPassword(pin, user.transaction_pin || '');

    if (!isPinValid) {
      return reply.status(401).send({
        success: false,
        message: 'invalid transaction pin'
      });
    }

    const reference = `CASHIN_${uuidv4()}_${Date.now()}`;

    logger.info('Cash-in transaction request received', {
      agentId: agent._id.toString(),
      agentCode: agent.agent_code,
      customerPhone,
      amount: parseFloat(amount),
      currency,
      reference
    });

    return reply.status(200).send({
      success: true,
      message: 'cash-in transaction request received and will be processed',
      data: {
        reference,
        agentId: agent._id.toString(),
        customerPhone,
        amount: parseFloat(amount),
        currency,
        status: 'pending',
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error processing cash-in transaction:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to process cash-in transaction'
    });
  }
};
