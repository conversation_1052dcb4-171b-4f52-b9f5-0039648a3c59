@echo off
echo.
echo ========================================
echo  AeTrust Backend - Windows Startup
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo Please install Node.js 18+ from https://nodejs.org
    pause
    exit /b 1
)

REM Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] npm is not available
    pause
    exit /b 1
)

echo [INFO] Node.js and npm are available
echo.

REM Check if .env file exists
if not exist ".env" (
    echo [INFO] Creating .env file from .env.example...
    copy ".env.example" ".env"
    echo [WARNING] Please update .env file with your configuration
    echo.
)

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo [INFO] Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to install dependencies
        pause
        exit /b 1
    )
    echo [SUCCESS] Dependencies installed
    echo.
)

REM Build the application
echo [INFO] Building application...
npm run build
if %errorlevel% neq 0 (
    echo [ERROR] Build failed
    pause
    exit /b 1
)
echo [SUCCESS] Application built successfully
echo.

REM Create necessary directories
echo [INFO] Creating necessary directories...
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads
if not exist "backups" mkdir backups
echo [SUCCESS] Directories created
echo.

REM Check if MongoDB is running (optional check)
echo [INFO] Checking MongoDB connection...
timeout /t 2 /nobreak >nul

REM Check if Memurai/Redis is running
echo [INFO] Checking Memurai/Redis connection...
timeout /t 2 /nobreak >nul

REM Start the application
echo [INFO] Starting AeTrust Backend...
echo [INFO] API will be available at: http://localhost:3000
echo [INFO] Health check: http://localhost:3000/health
echo [INFO] Press Ctrl+C to stop the server
echo.
echo ========================================
echo  Server Starting...
echo ========================================
echo.

npm run dev
