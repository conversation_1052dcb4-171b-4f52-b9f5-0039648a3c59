import { FastifyReply } from 'fastify';
import { AuthenticatedRequest } from '../types';
import { CashInService } from '../services/cash-in.service';
import { TransactionPinService } from '../services/transaction-pin.service';
import { logger } from '../config/logger';

export const getCashInOptions = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { amount } = request.query as { amount?: string };
    const cashInAmount = amount ? parseFloat(amount) : 100;

    const uiData = await CashInService.getCashInUIData(request.user.id, cashInAmount);

    return reply.status(200).send({
      success: true,
      message: 'cash-in options retrieved successfully',
      data: uiData
    });
  } catch (error: any) {
    logger.error('Error getting cash-in options:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve cash-in options'
    });
  }
};

export const calculateFees = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { amount, agentId } = request.body as { amount: number; agentId?: string };

    if (!amount || amount <= 0) {
      return reply.status(400).send({
        success: false,
        message: 'valid amount is required'
      });
    }

    const feeCalculation = await CashInService.calculateCashInFees(amount, agentId);

    return reply.status(200).send({
      success: true,
      message: 'fees calculated successfully',
      data: feeCalculation
    });
  } catch (error: any) {
    logger.error('Error calculating cash-in fees:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to calculate fees'
    });
  }
};

export const initiateCashIn = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const {
      amount,
      currency,
      agentId,
      recipientPhone,
      recipientName,
      description,
      pin
    } = request.body as any;

    if (!amount || !currency || !pin) {
      return reply.status(400).send({
        success: false,
        message: 'amount, currency, and PIN are required'
      });
    }

    if (amount <= 0) {
      return reply.status(400).send({
        success: false,
        message: 'amount must be greater than 0'
      });
    }

    // Verify transaction PIN
    const pinVerification = await TransactionPinService.verifyPinForTransaction(
      request.user.id,
      pin,
      amount,
      'app' as any,
      request.ip
    );

    if (!pinVerification.authorized) {
      return reply.status(400).send({
        success: false,
        message: pinVerification.message
      });
    }

    const result = await CashInService.initiateCashIn({
      userId: request.user.id,
      amount,
      currency,
      agentId,
      recipientPhone,
      recipientName,
      description,
      pin,
      metadata: {
        ipAddress: request.ip,
        userAgent: request.headers['user-agent'],
        platform: 'app',
        transactionId: pinVerification.transactionId
      }
    });

    return reply.status(201).send({
      success: true,
      message: 'cash-in transaction initiated successfully',
      data: {
        transactionRef: result.transactionRef,
        amount: result.amount,
        currency,
        status: result.status,
        estimatedCompletion: result.estimatedCompletion,
        fees: result.fees,
        agentInfo: result.agentInfo,
        instructions: result.instructions,
        nextSteps: [
          'Visit the agent location',
          'Present transaction reference',
          'Complete cash payment',
          'Receive confirmation'
        ]
      }
    });
  } catch (error: any) {
    logger.error('Error initiating cash-in:', error);
    
    const errorMessages: Record<string, string> = {
      'User not found': 'user account not found',
      'Wallet not found': 'wallet not found for this currency',
      'Insufficient balance': 'insufficient wallet balance'
    };

    return reply.status(500).send({
      success: false,
      message: errorMessages[error.message] || 'failed to initiate cash-in transaction'
    });
  }
};

export const getCashInStatus = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { transactionRef } = request.params as { transactionRef: string };

    if (!transactionRef) {
      return reply.status(400).send({
        success: false,
        message: 'transaction reference is required'
      });
    }

    const status = await CashInService.getCashInStatus(transactionRef, request.user.id);

    return reply.status(200).send({
      success: true,
      message: 'cash-in status retrieved successfully',
      data: {
        transactionRef,
        status: status.status,
        amount: status.amount,
        currency: status.currency,
        createdAt: status.createdAt,
        completedAt: status.completedAt,
        agentInfo: status.agentInfo,
        instructions: status.instructions,
        statusMessage: getStatusMessage(status.status),
        canCancel: status.status === 'pending',
        estimatedCompletion: status.status === 'pending' ? '15-30 minutes' : null
      }
    });
  } catch (error: any) {
    logger.error('Error getting cash-in status:', error);
    
    if (error.message === 'Transaction not found') {
      return reply.status(404).send({
        success: false,
        message: 'transaction not found'
      });
    }

    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve transaction status'
    });
  }
};

const getStatusMessage = (status: string): string => {
  const statusMessages: Record<string, string> = {
    'pending': 'Waiting for agent confirmation',
    'processing': 'Transaction is being processed',
    'completed': 'Cash-in completed successfully',
    'failed': 'Transaction failed',
    'cancelled': 'Transaction was cancelled'
  };
  return statusMessages[status] || 'Unknown status';
};

export const completeCashIn = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { transactionRef } = request.params as { transactionRef: string };
    const { agentId } = request.body as { agentId: string };

    if (!transactionRef || !agentId) {
      return reply.status(400).send({
        success: false,
        message: 'transaction reference and agent ID are required'
      });
    }

    // Note: In production, this should verify that the user is an authorized agent
    const result = await CashInService.completeCashIn(transactionRef, agentId);

    return reply.status(200).send({
      success: true,
      message: result.message,
      data: {
        transactionRef,
        status: 'completed',
        newBalance: result.newBalance,
        completedAt: new Date().toISOString()
      }
    });
  } catch (error: any) {
    logger.error('Error completing cash-in:', error);
    
    const errorMessages: Record<string, string> = {
      'Transaction not found or already completed': 'transaction not found or already completed',
      'Wallet not found': 'wallet not found'
    };

    return reply.status(error.message.includes('not found') ? 404 : 500).send({
      success: false,
      message: errorMessages[error.message] || 'failed to complete cash-in transaction'
    });
  }
};

export const getCashInHistory = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { page = '1', limit = '20' } = request.query as {
      page?: string;
      limit?: string;
      status?: string;
    };

    // This would typically use TransactionService.getUserTransactions with cash-in filter
    // For now, we'll return a mock response structure
    const transactions = [
      {
        id: 'cashin_001',
        transactionRef: 'CASHIN_20241201_001',
        amount: 100,
        currency: 'USD',
        status: 'completed',
        agentName: 'QuickCash Store',
        createdAt: new Date().toISOString(),
        completedAt: new Date().toISOString()
      }
    ];

    return reply.status(200).send({
      success: true,
      message: 'cash-in history retrieved successfully',
      data: {
        transactions,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: transactions.length,
          pages: 1
        }
      }
    });
  } catch (error: any) {
    logger.error('Error getting cash-in history:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve cash-in history'
    });
  }
};
