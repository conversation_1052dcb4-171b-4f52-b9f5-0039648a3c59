// import { FastifyInstance, FastifyPluginOptions } from 'fastify';
// import * as cashInController from '../controllers/cash-in.controller';
// import { AuthMiddleware } from '../middleware/auth.middleware';

// export async function cashInRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
//   fastify.get('/options', { preHandler: AuthMiddleware.authenticateRequest }, cashInController.getCashInOptions as any);
//   fastify.post('/calculate-fees', { preHandler: AuthMiddleware.authenticateRequest }, cashInController.calculateFees as any);
//   fastify.post('/initiate', { preHandler: AuthMiddleware.authenticateRequest }, cashInController.initiateCashIn as any);
//   fastify.get('/status/:transactionRef', { preHandler: AuthMiddleware.authenticateRequest }, cashInController.getCashInStatus as any);
//   fastify.post('/complete/:transactionRef', { preHandler: AuthMiddleware.authenticateRequest }, cashInController.completeCashIn as any);
//   fastify.get('/history', { preHandler: AuthMiddleware.authenticateRequest }, cashInController.getCashInHistory as any);

//   fastify.get('/health', async () => {
//     return {
//       success: true,
//       message: 'cash-in service is running',
//       data: { status: 'ok' }
//     };
//   });
// }
