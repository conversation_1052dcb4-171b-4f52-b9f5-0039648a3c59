import dotenv from 'dotenv';
import path from 'path';

dotenv.config();

const requiredEnvVars = [
  'JWT_SECRET',
  'JWT_REFRESH_SECRET',
  'MONGODB_URI'
];

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
if (missingEnvVars.length > 0) {
  throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
}

export const config = {
  server: {
    port: parseInt(process.env.PORT || '3000'),
    host: process.env.HOST || '0.0.0.0',
    env: process.env.NODE_ENV || 'development',
    apiVersion: process.env.API_VERSION || 'v1'
  },

  database: {
    uri: process.env.MONGODB_URI!,
    testUri: process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/aetrust_test',
    poolSize: parseInt(process.env.DB_CONNECTION_POOL_SIZE || '10'),
    timeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '30000')
  },

  redis: {
    enabled: process.env.REDIS_DISABLED !== 'true' && !!process.env.REDIS_URL,
    url: process.env.REDIS_URL,
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0')
  },

  jwt: {
    secret: process.env.JWT_SECRET!,
    refreshSecret: process.env.JWT_REFRESH_SECRET!,
    expiresIn: process.env.JWT_EXPIRES_IN || '15m',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d'
  },

  security: {
    encryptionKey: process.env.ENCRYPTION_KEY || 'default-key-change-in-production',
    saltRounds: parseInt(process.env.HASH_SALT_ROUNDS || '12'),
    cookieSecret: process.env.COOKIE_SECRET || 'default-cookie-secret',
    sessionSecret: process.env.SESSION_SECRET || 'default-session-secret'
  },

  rateLimit: {
    max: parseInt(process.env.RATE_LIMIT_MAX || '100'),
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW || '900000') 
  },

  cors: {
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
    credentials: true
  },

  email: {
    smtp: {
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    },
    from: process.env.EMAIL_FROM || '<EMAIL>'
  },

  sms: {
    twilio: {
      accountSid: process.env.TWILIO_ACCOUNT_SID,
      authToken: process.env.TWILIO_AUTH_TOKEN,
      phoneNumber: process.env.TWILIO_PHONE_NUMBER
    }
  },

  payment: {
    gateway: {
      apiKey: process.env.PAYMENT_GATEWAY_API_KEY,
      secret: process.env.PAYMENT_GATEWAY_SECRET,
      webhookSecret: process.env.PAYMENT_GATEWAY_WEBHOOK_SECRET
    }
  },

  kyc: {
    apiKey: process.env.KYC_SERVICE_API_KEY,
    baseUrl: process.env.KYC_SERVICE_BASE_URL || 'https://api.kyc-service.com'
  },

  upload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '5242880'), // 5MB
    uploadPath: path.resolve(process.env.UPLOAD_PATH || './uploads'),
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf']
  },

  // Logging configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    filePath: process.env.LOG_FILE_PATH || './logs'
  },

  // Monitoring configuration
  monitoring: {
    sentryDsn: process.env.SENTRY_DSN,
    healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL || '30000')
  },

  // External APIs
  external: {
    exchangeRate: {
      apiKey: process.env.EXCHANGE_RATE_API_KEY
    },
    blockchain: {
      rpcUrl: process.env.BLOCKCHAIN_RPC_URL,
      privateKey: process.env.BLOCKCHAIN_PRIVATE_KEY
    }
  },

  // Business configuration
  business: {
    agent: {
      commissionRate: parseFloat(process.env.AGENT_COMMISSION_RATE || '0.02'),
      maxDailyLimit: parseInt(process.env.MAX_DAILY_TRANSACTION_LIMIT || '100000'),
      minWalletBalance: parseInt(process.env.MIN_WALLET_BALANCE || '1000')
    },
    loan: {
      defaultInterestRate: parseFloat(process.env.DEFAULT_INTEREST_RATE || '0.15'),
      maxLoanAmount: parseInt(process.env.MAX_LOAN_AMOUNT || '50000'),
      minCreditScore: parseInt(process.env.MIN_CREDIT_SCORE || '300')
    }
  }
};

export default config;
