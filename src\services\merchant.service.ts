import { Merchant, IMerchant } from '../models/merchant.model';
import { Transaction } from '../models/transaction.model';
import { User } from '../models/user.model';
import { WalletService } from './wallet.service';
import { UserService } from './user.service';
import { KycStatus, AccountStatus, Currency, WalletType, UserRole } from '../types';
import { CryptoUtils } from '../utils/crypto';
import { logger } from '../config/logger';
import mongoose from 'mongoose';

export class MerchantService {
  static async createMerchant(data: {
    userId: string;
    businessName: string;
    businessType: string;
    businessRegistrationNumber: string;
    taxId: string;
    businessAddress: any;
    contactInfo: any;
    acceptedCurrencies?: Currency[];
  }): Promise<IMerchant> {
    try {
      const existingMerchant = await Merchant.findOne({ user_id: data.userId });
      if (existingMerchant) {
        throw new Error('user already has a merchant account');
      }

      const existingBusiness = await Merchant.findOne({
        business_registration_number: data.businessRegistrationNumber
      });
      if (existingBusiness) {
        throw new Error('business registration number already exists');
      }

      const merchant = new Merchant({
        user_id: data.userId,
        business_name: data.businessName,
        business_type: data.businessType,
        business_registration_number: data.businessRegistrationNumber,
        tax_id: data.taxId,
        business_address: data.businessAddress,
        contact_info: data.contactInfo,
        payment_settings: {
          accepted_currencies: data.acceptedCurrencies || [Currency.USD],
          settlement_currency: Currency.USD,
          settlement_schedule: 'daily',
          auto_settlement: true,
          minimum_settlement_amount: 100
        },
        api_credentials: {
          api_key: CryptoUtils.generateApiKey(32),
          secret_key: CryptoUtils.generateSecureToken(64),
          webhook_secret: CryptoUtils.generateSecureToken(32)
        },
        qr_code: {
          static_qr: CryptoUtils.generateTransactionRef('QR'),
          dynamic_qr_enabled: true
        }
      });

      await merchant.save();

      try {
        const user = await UserService.getUserById(data.userId);
        if (user) {
          await User.findByIdAndUpdate(
            data.userId,
            { $set: { role: UserRole.MERCHANT } }
          );
        }
      } catch (error) {
        logger.error('Failed to update user role to merchant:', error);
      }

      for (const currency of merchant.payment_settings.accepted_currencies) {
        await WalletService.createWallet({
          userId: data.userId,
          currency,
          walletType: WalletType.MAIN,
          isDefault: currency === merchant.payment_settings.settlement_currency
        });
      }

      logger.info('merchant account created', {
        merchantId: merchant._id,
        userId: data.userId,
        businessName: data.businessName
      });

      return merchant;
    } catch (error: any) {
      logger.error('error creating merchant:', error);
      throw error;
    }
  }

  static async getMerchantById(merchantId: string): Promise<IMerchant | null> {
    try {
      if (!mongoose.Types.ObjectId.isValid(merchantId)) {
        return null;
      }

      const merchant = await Merchant.findById(merchantId)
        .populate('user_id', 'first_name last_name email phone');

      return merchant;
    } catch (error: any) {
      logger.error('error getting merchant by id:', error);
      return null;
    }
  }

  static async getMerchantByUserId(userId: string): Promise<IMerchant | null> {
    try {
      const merchant = await Merchant.findOne({ user_id: userId })
        .populate('user_id', 'first_name last_name email phone');

      return merchant;
    } catch (error: any) {
      logger.error('error getting merchant by user id:', error);
      return null;
    }
  }

  static async getMerchantByApiKey(apiKey: string): Promise<IMerchant | null> {
    try {
      const merchant = await Merchant.findOne({
        'api_credentials.api_key': apiKey,
        account_status: AccountStatus.ACTIVE
      });

      return merchant;
    } catch (error: any) {
      logger.error('error getting merchant by api key:', error);
      return null;
    }
  }

  static async updateMerchantKyc(
    merchantId: string,
    status: KycStatus,
    reason?: string
  ): Promise<boolean> {
    try {
      const merchant = await Merchant.findByIdAndUpdate(
        merchantId,
        { 
          $set: { 
            kyc_status: status,
            ...(status === KycStatus.APPROVED && { account_status: AccountStatus.ACTIVE })
          }
        },
        { new: true }
      );

      if (merchant) {
        logger.info('merchant kyc status updated', {
          merchantId,
          status,
          reason
        });
        return true;
      }

      return false;
    } catch (error: any) {
      logger.error('error updating merchant kyc:', error);
      return false;
    }
  }

  static async updateMerchantSettings(
    merchantId: string,
    settings: {
      paymentSettings?: any;
      fees?: any;
      limits?: any;
      webhookUrl?: string;
    }
  ): Promise<IMerchant | null> {
    try {
      const updateData: any = {};

      if (settings.paymentSettings) {
        updateData.payment_settings = settings.paymentSettings;
      }
      if (settings.fees) {
        updateData.fees = settings.fees;
      }
      if (settings.limits) {
        updateData.limits = settings.limits;
      }
      if (settings.webhookUrl) {
        updateData['api_credentials.webhook_url'] = settings.webhookUrl;
      }

      const merchant = await Merchant.findByIdAndUpdate(
        merchantId,
        { $set: updateData },
        { new: true, runValidators: true }
      );

      if (merchant) {
        logger.info('merchant settings updated', {
          merchantId,
          updatedFields: Object.keys(updateData)
        });
      }

      return merchant;
    } catch (error: any) {
      logger.error('error updating merchant settings:', error);
      throw error;
    }
  }

  static async regenerateApiCredentials(merchantId: string): Promise<{
    apiKey: string;
    secretKey: string;
  } | null> {
    try {
      const newApiKey = CryptoUtils.generateApiKey(32);
      const newSecretKey = CryptoUtils.generateSecureToken(64);

      const merchant = await Merchant.findByIdAndUpdate(
        merchantId,
        {
          $set: {
            'api_credentials.api_key': newApiKey,
            'api_credentials.secret_key': newSecretKey
          }
        },
        { new: true }
      );

      if (merchant) {
        logger.info('merchant api credentials regenerated', {
          merchantId,
          newApiKey: CryptoUtils.maskSensitiveData(newApiKey)
        });

        return {
          apiKey: newApiKey,
          secretKey: newSecretKey
        };
      }

      return null;
    } catch (error: any) {
      logger.error('error regenerating api credentials:', error);
      return null;
    }
  }

  static async searchMerchants(query: {
    search?: string;
    kycStatus?: KycStatus;
    accountStatus?: AccountStatus;
    businessType?: string;
    page?: number;
    limit?: number;
  }): Promise<{
    merchants: IMerchant[];
    total: number;
    page: number;
    pages: number;
  }> {
    try {
      const page = query.page || 1;
      const limit = Math.min(query.limit || 20, 100);
      const skip = (page - 1) * limit;

      const filter: any = {};

      if (query.search) {
        filter.$or = [
          { business_name: { $regex: query.search, $options: 'i' } },
          { business_registration_number: { $regex: query.search, $options: 'i' } },
          { 'contact_info.email': { $regex: query.search, $options: 'i' } }
        ];
      }

      if (query.kycStatus) filter.kyc_status = query.kycStatus;
      if (query.accountStatus) filter.account_status = query.accountStatus;
      if (query.businessType) filter.business_type = query.businessType;

      const [merchants, total] = await Promise.all([
        Merchant.find(filter)
          .sort({ created_at: -1 })
          .skip(skip)
          .limit(limit)
          .populate('user_id', 'first_name last_name email')
          .lean(),
        Merchant.countDocuments(filter)
      ]);

      return {
        merchants: merchants as IMerchant[],
        total,
        page,
        pages: Math.ceil(total / limit)
      };
    } catch (error: any) {
      logger.error('error searching merchants:', error);
      throw error;
    }
  }

  static async getMerchantStats(merchantId: string): Promise<{
    totalTransactions: number;
    totalVolume: number;
    successRate: number;
    avgTransactionAmount: number;
    monthlyVolume: number;
    dailyVolume: number;
  } | null> {
    try {
      const merchant = await Merchant.findById(merchantId);
      if (!merchant) {
        return null;
      }


      const now = new Date();
      const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      const [totalStats, dailyStats, monthlyStats, successStats] = await Promise.all([
        Transaction.aggregate([
          { $match: { user_id: merchant.user_id, status: 'completed' } },
          { $group: { _id: null, count: { $sum: 1 }, volume: { $sum: '$amount' } } }
        ]),
        Transaction.aggregate([
          {
            $match: {
              user_id: merchant.user_id,
              status: 'completed',
              created_at: { $gte: startOfDay }
            }
          },
          { $group: { _id: null, volume: { $sum: '$amount' } } }
        ]),
        Transaction.aggregate([
          {
            $match: {
              user_id: merchant.user_id,
              status: 'completed',
              created_at: { $gte: startOfMonth }
            }
          },
          { $group: { _id: null, volume: { $sum: '$amount' } } }
        ]),
        Transaction.aggregate([
          { $match: { user_id: merchant.user_id } },
          {
            $group: {
              _id: '$status',
              count: { $sum: 1 }
            }
          }
        ])
      ]);

      const totalTransactions = totalStats[0]?.count || 0;
      const totalVolume = totalStats[0]?.volume || 0;
      const dailyVolume = dailyStats[0]?.volume || 0;
      const monthlyVolume = monthlyStats[0]?.volume || 0;
      const completedCount = successStats.find(s => s._id === 'completed')?.count || 0;
      const totalCount = successStats.reduce((sum, s) => sum + s.count, 0);
      const successRate = totalCount > 0 ? (completedCount / totalCount) * 100 : 0;

      const stats = {
        totalTransactions: totalTransactions,
        totalVolume: Math.round(totalVolume * 100) / 100,
        successRate: Math.round(successRate * 100) / 100,
        avgTransactionAmount: totalTransactions > 0
          ? Math.round((totalVolume / totalTransactions) * 100) / 100
          : 0,
        monthlyVolume: Math.round(monthlyVolume * 100) / 100,
        dailyVolume: Math.round(dailyVolume * 100) / 100
      };

      return stats;
    } catch (error: any) {
      logger.error('error getting merchant stats:', error);
      return null;
    }
  }

  static async suspendMerchant(merchantId: string, reason: string): Promise<boolean> {
    try {
      const merchant = await Merchant.findByIdAndUpdate(
        merchantId,
        { $set: { account_status: AccountStatus.SUSPENDED } },
        { new: true }
      );

      if (merchant) {
        logger.info('merchant suspended', {
          merchantId,
          reason
        });
        return true;
      }

      return false;
    } catch (error: any) {
      logger.error('error suspending merchant:', error);
      return false;
    }
  }

  static async reactivateMerchant(merchantId: string): Promise<boolean> {
    try {
      const merchant = await Merchant.findByIdAndUpdate(
        merchantId,
        { $set: { account_status: AccountStatus.ACTIVE } },
        { new: true }
      );

      if (merchant) {
        logger.info('merchant reactivated', {
          merchantId
        });
        return true;
      }

      return false;
    } catch (error: any) {
      logger.error('error reactivating merchant:', error);
      return false;
    }
  }

  static async generateUSSDCode(merchantId: string): Promise<string | null> {
    try {
      // generate unique USSD code
      let ussdCode: string;
      let isUnique = false;
      let attempts = 0;

      do {
        ussdCode = `*${Math.floor(Math.random() * 900) + 100}*${Math.floor(Math.random() * 9000) + 1000}#`;
        const existing = await Merchant.findOne({ ussd_code: ussdCode });
        isUnique = !existing;
        attempts++;
      } while (!isUnique && attempts < 10);

      if (!isUnique) {
        throw new Error('failed to generate unique USSD code');
      }

      const merchant = await Merchant.findByIdAndUpdate(
        merchantId,
        { $set: { ussd_code: ussdCode } },
        { new: true }
      );

      if (merchant) {
        logger.info('USSD code generated for merchant', {
          merchantId,
          ussdCode
        });
        return ussdCode;
      }

      return null;
    } catch (error: any) {
      logger.error('error generating USSD code:', error);
      return null;
    }
  }
}
