#!/bin/bash

# AeTrust Backend Restore Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKUP_DIR=${BACKUP_DIR:-./backups}
BACKUP_FILE=${1:-latest}

echo -e "${BLUE}🔄 Starting AeTrust Backend Restore${NC}"

# Function to find latest backup
find_latest_backup() {
    if [ "$BACKUP_FILE" = "latest" ]; then
        LATEST_BACKUP=$(ls -t $BACKUP_DIR/aetrust_backup_*.tar.gz 2>/dev/null | head -1)
        if [ -z "$LATEST_BACKUP" ]; then
            echo -e "${RED}❌ No backup files found${NC}"
            exit 1
        fi
        BACKUP_FILE=$(basename "$LATEST_BACKUP")
    fi
}

# Validate backup file
validate_backup() {
    if [ ! -f "$BACKUP_DIR/$BACKUP_FILE" ]; then
        echo -e "${RED}❌ Backup file not found: $BACKUP_DIR/$BACKUP_FILE${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Backup file found: $BACKUP_FILE${NC}"
}

# Extract backup
extract_backup() {
    echo -e "${YELLOW}📦 Extracting backup...${NC}"
    
    cd $BACKUP_DIR
    
    if [[ $BACKUP_FILE == *.tar.gz ]]; then
        tar -xzf "$BACKUP_FILE"
        EXTRACT_DIR=$(echo "$BACKUP_FILE" | sed 's/.tar.gz$//')
    else
        EXTRACT_DIR="$BACKUP_FILE"
    fi
    
    echo -e "${GREEN}✅ Backup extracted${NC}"
}

# Restore MongoDB
restore_mongodb() {
    echo -e "${YELLOW}🗄️ Restoring MongoDB...${NC}"
    
    # Check if MongoDB container is running
    if ! docker ps | grep -q "mongodb"; then
        echo -e "${RED}❌ MongoDB container not running${NC}"
        exit 1
    fi
    
    MONGO_CONTAINER=$(docker ps --format "table {{.Names}}" | grep mongodb | head -1)
    
    # Find MongoDB backup directory
    MONGO_BACKUP_DIR=$(find $BACKUP_DIR -name "mongodb_*" -type d | head -1)
    
    if [ -z "$MONGO_BACKUP_DIR" ]; then
        echo -e "${RED}❌ MongoDB backup directory not found${NC}"
        exit 1
    fi
    
    # Copy backup to container
    docker cp "$MONGO_BACKUP_DIR" $MONGO_CONTAINER:/tmp/
    
    # Restore MongoDB
    MONGO_BACKUP_NAME=$(basename "$MONGO_BACKUP_DIR")
    docker exec $MONGO_CONTAINER mongorestore --drop /tmp/$MONGO_BACKUP_NAME
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ MongoDB restore completed${NC}"
    else
        echo -e "${RED}❌ MongoDB restore failed${NC}"
        exit 1
    fi
}

# Restore Redis
restore_redis() {
    echo -e "${YELLOW}🔴 Restoring Redis...${NC}"
    
    # Check if Redis container is running
    if ! docker ps | grep -q "redis"; then
        echo -e "${RED}❌ Redis container not running${NC}"
        exit 1
    fi
    
    REDIS_CONTAINER=$(docker ps --format "table {{.Names}}" | grep redis | head -1)
    
    # Find Redis backup directory
    REDIS_BACKUP_DIR=$(find $BACKUP_DIR -name "redis_*" -type d | head -1)
    
    if [ -z "$REDIS_BACKUP_DIR" ]; then
        echo -e "${RED}❌ Redis backup directory not found${NC}"
        exit 1
    fi
    
    # Stop Redis to restore data
    docker exec $REDIS_CONTAINER redis-cli SHUTDOWN NOSAVE || true
    sleep 2
    
    # Copy backup files
    if [ -f "$REDIS_BACKUP_DIR/dump.rdb" ]; then
        docker cp "$REDIS_BACKUP_DIR/dump.rdb" $REDIS_CONTAINER:/data/
    fi
    
    if [ -f "$REDIS_BACKUP_DIR/appendonly.aof" ]; then
        docker cp "$REDIS_BACKUP_DIR/appendonly.aof" $REDIS_CONTAINER:/data/
    fi
    
    # Restart Redis container
    docker restart $REDIS_CONTAINER
    
    # Wait for Redis to start
    sleep 5
    
    # Test Redis connection
    if docker exec $REDIS_CONTAINER redis-cli ping > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Redis restore completed${NC}"
    else
        echo -e "${RED}❌ Redis restore failed${NC}"
        exit 1
    fi
}

# Restore application files
restore_app_files() {
    echo -e "${YELLOW}📁 Restoring application files...${NC}"
    
    # Find app backup directory
    APP_BACKUP_DIR=$(find $BACKUP_DIR -name "app_*" -type d | head -1)
    
    if [ -z "$APP_BACKUP_DIR" ]; then
        echo -e "${YELLOW}⚠️ Application backup directory not found, skipping${NC}"
        return
    fi
    
    # Restore logs
    if [ -d "$APP_BACKUP_DIR/logs" ]; then
        cp -r "$APP_BACKUP_DIR/logs" ./
        echo -e "${GREEN}✅ Logs restored${NC}"
    fi
    
    # Restore config files (be careful not to overwrite current config)
    if [ -d "$APP_BACKUP_DIR/config" ]; then
        echo -e "${YELLOW}⚠️ Config backup found. Manual review recommended.${NC}"
        echo -e "${BLUE}Backup config location: $APP_BACKUP_DIR/config${NC}"
    fi
    
    echo -e "${GREEN}✅ Application files restore completed${NC}"
}

# Verify restore
verify_restore() {
    echo -e "${YELLOW}🔍 Verifying restore...${NC}"
    
    # Test API health
    sleep 10
    if curl -f -s "http://localhost:3000/health" > /dev/null; then
        echo -e "${GREEN}✅ API health check passed${NC}"
    else
        echo -e "${RED}❌ API health check failed${NC}"
        exit 1
    fi
    
    # Test database connection
    if docker exec $(docker ps --format "table {{.Names}}" | grep mongodb | head -1) \
       mongosh --eval "db.adminCommand('ping')" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Database connection verified${NC}"
    else
        echo -e "${RED}❌ Database connection failed${NC}"
        exit 1
    fi
    
    # Test Redis connection
    if docker exec $(docker ps --format "table {{.Names}}" | grep redis | head -1) \
       redis-cli ping > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Redis connection verified${NC}"
    else
        echo -e "${RED}❌ Redis connection failed${NC}"
        exit 1
    fi
}

# Cleanup extracted files
cleanup() {
    echo -e "${YELLOW}🧹 Cleaning up temporary files...${NC}"
    
    # Remove extracted directories
    find $BACKUP_DIR -name "mongodb_*" -type d -exec rm -rf {} \; 2>/dev/null || true
    find $BACKUP_DIR -name "redis_*" -type d -exec rm -rf {} \; 2>/dev/null || true
    find $BACKUP_DIR -name "app_*" -type d -exec rm -rf {} \; 2>/dev/null || true
    
    echo -e "${GREEN}✅ Cleanup completed${NC}"
}

# Main execution
echo -e "${BLUE}📋 Restore Configuration:${NC}"
echo -e "${BLUE}- Backup Directory: $BACKUP_DIR${NC}"
echo -e "${BLUE}- Backup File: $BACKUP_FILE${NC}"

# Confirmation prompt
echo -e "${YELLOW}⚠️ This will overwrite existing data. Are you sure? (y/N)${NC}"
read -r confirmation
if [[ ! $confirmation =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}Restore cancelled${NC}"
    exit 0
fi

# Execute restore steps
find_latest_backup
validate_backup
extract_backup
restore_mongodb
restore_redis
restore_app_files
verify_restore
cleanup

echo -e "${GREEN}🎉 Restore completed successfully!${NC}"
echo -e "${GREEN}Backup file: $BACKUP_FILE${NC}"
echo -e "${GREEN}Restore time: $(date)${NC}"

# Send notification if configured
if [ ! -z "$SLACK_WEBHOOK_URL" ]; then
    curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"🔄 AeTrust Backend restore completed successfully\\nBackup: $BACKUP_FILE\"}" \
        $SLACK_WEBHOOK_URL
fi

echo -e "${BLUE}📋 Restore Summary:${NC}"
echo -e "${BLUE}- MongoDB: Restored${NC}"
echo -e "${BLUE}- Redis: Restored${NC}"
echo -e "${BLUE}- Application Files: Restored${NC}"
echo -e "${BLUE}- Health Checks: Passed${NC}"
