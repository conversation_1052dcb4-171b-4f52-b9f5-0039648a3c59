import twilio from 'twilio';
import nodemailer from 'nodemailer';
import { config } from '../config';
import { logger, securityLogger } from '../config/logger';
import { Platform } from '../types';

interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

interface SmsOptions {
  to: string;
  message: string;
  platform?: Platform;
  userId?: string;
}

export class CommunicationService {
  private static twilioClient = twilio(
    config.sms.twilio.accountSid,
    config.sms.twilio.authToken
  );

  private static emailTransporter = nodemailer.createTransport({
    host: config.email.smtp.host,
    port: config.email.smtp.port,
    secure: config.email.smtp.port === 465,
    auth: {
      user: config.email.smtp.user,
      pass: config.email.smtp.pass
    },
    pool: true,
    maxConnections: 5,
    maxMessages: 100,
    rateDelta: 1000,
    rateLimit: 5
  });

  static async sendSms(options: SmsOptions): Promise<{
    success: boolean;
    messageId?: string;
    message: string;
  }> {
    try {
      if (!config.sms.twilio.accountSid || !config.sms.twilio.authToken) {
        logger.warn('SMS service not configured, skipping SMS send');
        return {
          success: false,
          message: 'SMS service not configured'
        };
      }

      const message = await this.twilioClient.messages.create({
        body: options.message,
        from: config.sms.twilio.phoneNumber!,
        to: options.to
      });

      securityLogger.info('SMS sent successfully', {
        messageId: message.sid,
        to: options.to,
        platform: options.platform,
        userId: options.userId
      });

      return {
        success: true,
        messageId: message.sid,
        message: 'SMS sent successfully'
      };
    } catch (error: any) {
      logger.error('SMS send failed:', error);
      
      return {
        success: false,
        message: 'Failed to send SMS'
      };
    }
  }

  static async sendEmail(options: EmailOptions): Promise<{
    success: boolean;
    messageId?: string;
    message: string;
  }> {
    try {
      if (!config.email.smtp.user || !config.email.smtp.pass) {
        logger.warn('Email service not configured, skipping email send');
        return {
          success: false,
          message: 'Email service not configured'
        };
      }

      const mailOptions = {
        from: config.email.from,
        to: options.to,
        subject: options.subject,
        html: options.html,
        text: options.text || options.html.replace(/<[^>]*>/g, '')
      };

      const info = await this.emailTransporter.sendMail(mailOptions);

      logger.info('Email sent successfully', {
        messageId: info.messageId,
        to: options.to,
        subject: options.subject
      });

      return {
        success: true,
        messageId: info.messageId,
        message: 'Email sent successfully'
      };
    } catch (error: any) {
      logger.error('Email send failed:', error);
      
      return {
        success: false,
        message: 'Failed to send email'
      };
    }
  }

  static async sendVerificationSms(phone: string, code: string, platform?: Platform, userId?: string): Promise<{
    success: boolean;
    messageId?: string;
    message: string;
  }> {
    const message = `Your AeTrust verification code is: ${code}. This code expires in 10 minutes. Do not share this code with anyone.`;
    
    return this.sendSms({
      to: phone,
      message,
      ...(platform && { platform }),
      ...(userId && { userId })
    });
  }

  static async sendVerificationEmail(email: string, code: string, firstName?: string): Promise<{
    success: boolean;
    messageId?: string;
    message: string;
  }> {
    const subject = 'Verify Your AeTrust Account';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Verify Your AeTrust Account</h2>
        <p>Hello ${firstName || 'there'},</p>
        <p>Thank you for registering with AeTrust. Please use the verification code below to complete your account setup:</p>
        <div style="background: #f3f4f6; padding: 20px; text-align: center; margin: 20px 0;">
          <h1 style="color: #1f2937; font-size: 32px; margin: 0;">${code}</h1>
        </div>
        <p>This code will expire in 10 minutes for security reasons.</p>
        <p>If you didn't request this verification, please ignore this email.</p>
        <hr style="margin: 30px 0;">
        <p style="color: #6b7280; font-size: 14px;">
          Best regards,<br>
          The AeTrust Team
        </p>
      </div>
    `;

    return this.sendEmail({
      to: email,
      subject,
      html
    });
  }

  static async sendTransactionNotification(
    phone: string, 
    email: string, 
    transactionType: string, 
    amount: number, 
    currency: string,
    firstName?: string
  ): Promise<void> {
    const smsMessage = `AeTrust: ${transactionType} of ${currency} ${amount.toFixed(2)} completed successfully. Thank you for using AeTrust.`;
    
    const emailSubject = `Transaction Confirmation - ${transactionType}`;
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Transaction Confirmation</h2>
        <p>Hello ${firstName || 'there'},</p>
        <p>Your ${transactionType.toLowerCase()} has been completed successfully.</p>
        <div style="background: #f3f4f6; padding: 20px; margin: 20px 0;">
          <p><strong>Transaction Type:</strong> ${transactionType}</p>
          <p><strong>Amount:</strong> ${currency} ${amount.toFixed(2)}</p>
          <p><strong>Date:</strong> ${new Date().toLocaleString()}</p>
        </div>
        <p>Thank you for using AeTrust!</p>
        <hr style="margin: 30px 0;">
        <p style="color: #6b7280; font-size: 14px;">
          Best regards,<br>
          The AeTrust Team
        </p>
      </div>
    `;

    // Send both SMS and email notifications
    await Promise.allSettled([
      this.sendSms({ to: phone, message: smsMessage }),
      this.sendEmail({ to: email, subject: emailSubject, html: emailHtml })
    ]);
  }

  static async sendSecurityAlert(
    phone: string,
    email: string,
    alertType: string,
    details: string,
    firstName?: string
  ): Promise<void> {
    const smsMessage = `AeTrust Security Alert: ${alertType}. ${details}. If this wasn't you, contact support immediately.`;
    
    const emailSubject = `Security Alert - ${alertType}`;
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #dc2626;">Security Alert</h2>
        <p>Hello ${firstName || 'there'},</p>
        <p>We detected the following security event on your account:</p>
        <div style="background: #fef2f2; border: 1px solid #fecaca; padding: 20px; margin: 20px 0;">
          <p><strong>Alert Type:</strong> ${alertType}</p>
          <p><strong>Details:</strong> ${details}</p>
          <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
        </div>
        <p>If this was you, no action is needed. If you didn't authorize this activity, please contact our support team immediately.</p>
        <hr style="margin: 30px 0;">
        <p style="color: #6b7280; font-size: 14px;">
          Best regards,<br>
          The AeTrust Security Team
        </p>
      </div>
    `;

    await Promise.allSettled([
      this.sendSms({ to: phone, message: smsMessage }),
      this.sendEmail({ to: email, subject: emailSubject, html: emailHtml })
    ]);
  }

  static async testConnection(): Promise<{
    sms: boolean;
    email: boolean;
  }> {
    const results = {
      sms: false,
      email: false
    };

    try {
      if (config.sms.twilio.accountSid && config.sms.twilio.authToken) {
        await this.twilioClient.api.accounts(config.sms.twilio.accountSid).fetch();
        results.sms = true;
      }
    } catch (error) {
      logger.error('SMS service connection test failed:', error);
    }

    try {
      if (config.email.smtp.user && config.email.smtp.pass) {
        await this.emailTransporter.verify();
        results.email = true;
      }
    } catch (error) {
      logger.error('Email service connection test failed:', error);
    }

    return results;
  }
}
