import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { AuthMiddleware } from '../middleware/auth.middleware';
import * as adminController from '../controllers/admin.controller';

export async function adminRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  fastify.get('/dashboard', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getDashboardStats as any);
  fastify.get('/health', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getSystemHealth as any);
  fastify.get('/audit-logs', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getAuditLogs as any);
  fastify.get('/fraud-alerts', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getFraudAlerts as any);
  fastify.get('/reports', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getReports as any);

  fastify.get('/kyc/pending', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getPendingKyc as any);
  fastify.post('/kyc/:userId/approve', { preHandler: AuthMiddleware.authenticateRequest }, adminController.approveKyc as any);
  fastify.post('/kyc/:userId/reject', { preHandler: AuthMiddleware.authenticateRequest }, adminController.rejectKyc as any);

  fastify.get('/security/config', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getSecurityConfig as any);
  fastify.put('/security/config', { preHandler: AuthMiddleware.authenticateRequest }, adminController.updateSecurityConfig as any);
  fastify.get('/security/monitoring', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getSecurityMonitoring as any);

  fastify.get('/analytics/dashboard', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getAnalyticsDashboard as any);
  fastify.get('/analytics/users', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getUserAnalytics as any);
  fastify.get('/analytics/transactions', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getTransactionAnalytics as any);
  fastify.get('/analytics/revenue', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getRevenueAnalytics as any);
  fastify.get('/analytics/agents', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getAgentAnalytics as any);
  fastify.get('/analytics/system', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getSystemPerformance as any);
  fastify.get('/analytics/export', { preHandler: AuthMiddleware.authenticateRequest }, adminController.exportAnalyticsReport as any);
  fastify.get('/analytics/advanced', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getAdvancedAnalytics as any);
  fastify.get('/merchants', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getMerchants as any);
  fastify.get('/credit-cards', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getCreditCards as any);
  fastify.get('/loans', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getLoansAdmin as any);

  fastify.get('/service-health', async () => {
    return {
      success: true,
      message: 'admin service running',
      data: { 
        status: 'ok',
        services: ['dashboard', 'monitoring', 'audit_logs', 'fraud_detection', 'reporting']
      }
    };
  });
}
