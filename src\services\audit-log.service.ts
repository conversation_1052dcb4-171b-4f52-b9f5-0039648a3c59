import { AuditLog } from '../models/audit-log.model';
import { logger, securityLogger } from '../config/logger';
import { AuditAction, Platform } from '../types';

export interface AuditLogData {
  userId?: string;
  action: AuditAction;
  resource: string;
  resourceId?: string;
  details: any;
  platform: Platform;
  ipAddress: string;
  userAgent?: string | undefined;
  sessionId?: string;
  success: boolean;
  errorMessage?: string | undefined;
  metadata?: any;
}

export class AuditLogService {

  static async logAction(data: AuditLogData): Promise<void> {
    try {
      const auditEntry = new AuditLog({
        user_id: data.userId,
        action: data.action,
        resource_type: data.resource,
        resource_id: data.resourceId,
        details: data.details,
        ip_address: data.ipAddress,
        user_agent: data.userAgent || 'unknown',
        request_id: `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        metadata: {
          platform: data.platform,
          success: data.success,
          error_message: data.errorMessage,
          ...data.metadata,
          timestamp: new Date().toISOString(),
          server_time: Date.now()
        },
        created_at: new Date()
      });

      await auditEntry.save();


      if (data.success) {
        securityLogger.info(`Audit: ${data.action}`, {
          userId: data.userId,
          action: data.action,
          resource: data.resource,
          platform: data.platform,
          ipAddress: data.ipAddress,
          details: data.details
        });
      } else {
        securityLogger.warn(`Audit Failed: ${data.action}`, {
          userId: data.userId,
          action: data.action,
          resource: data.resource,
          platform: data.platform,
          ipAddress: data.ipAddress,
          error: data.errorMessage,
          details: data.details
        });
      }

    } catch (error: any) {
      logger.error('Failed to create audit log:', error);
      securityLogger.error('Audit log creation failed', {
        originalData: data,
        error: error.message
      });
    }
  }

  static async logUserLogin(userId: string, platform: Platform, ipAddress: string, userAgent?: string, success: boolean = true, errorMessage?: string): Promise<void> {
    await this.logAction({
      userId,
      action: AuditAction.USER_LOGIN,
      resource: 'user_session',
      details: {
        login_method: 'phone_password',
        platform,
        success
      },
      platform,
      ipAddress,
      userAgent,
      success,
      errorMessage
    });
  }

  static async logUserLogout(userId: string, platform: Platform, ipAddress: string, userAgent?: string): Promise<void> {
    await this.logAction({
      userId,
      action: AuditAction.USER_LOGOUT,
      resource: 'user_session',
      details: {
        logout_type: 'manual',
        platform
      },
      platform,
      ipAddress,
      userAgent,
      success: true
    });
  }

  static async logUserRegistration(userId: string, platform: Platform, ipAddress: string, step: string, userAgent?: string, success: boolean = true, errorMessage?: string): Promise<void> {
    await this.logAction({
      userId,
      action: AuditAction.USER_REGISTER,
      resource: 'user_account',
      details: {
        registration_step: step,
        platform,
        success
      },
      platform,
      ipAddress,
      userAgent,
      success,
      errorMessage
    });
  }

  static async logTransaction(userId: string, transactionId: string, transactionType: string, amount: number, currency: string, platform: Platform, ipAddress: string, success: boolean = true, errorMessage?: string): Promise<void> {
    await this.logAction({
      userId,
      action: AuditAction.TRANSACTION_CREATE,
      resource: 'transaction',
      resourceId: transactionId,
      details: {
        transaction_type: transactionType,
        amount,
        currency,
        platform,
        success
      },
      platform,
      ipAddress,
      success,
      errorMessage,
      metadata: {
        financial_data: true,
        compliance_required: true
      }
    });
  }

  static async logKycSubmission(userId: string, kycType: string, platform: Platform, ipAddress: string, success: boolean = true, errorMessage?: string): Promise<void> {
    await this.logAction({
      userId,
      action: AuditAction.KYC_SUBMISSION,
      resource: 'kyc_document',
      details: {
        kyc_type: kycType,
        platform,
        success
      },
      platform,
      ipAddress,
      success,
      errorMessage,
      metadata: {
        compliance_required: true,
        sensitive_data: true
      }
    });
  }

  static async logKycApproval(userId: string, adminId: string, platform: Platform, ipAddress: string): Promise<void> {
    await this.logAction({
      userId,
      action: AuditAction.KYC_APPROVAL,
      resource: 'kyc_document',
      details: {
        approved_by: adminId,
        platform
      },
      platform,
      ipAddress,
      success: true,
      metadata: {
        compliance_required: true,
        admin_action: true
      }
    });
  }

  static async logKycRejection(userId: string, adminId: string, reason: string, platform: Platform, ipAddress: string): Promise<void> {
    await this.logAction({
      userId,
      action: AuditAction.KYC_REJECTION,
      resource: 'kyc_document',
      details: {
        rejected_by: adminId,
        rejection_reason: reason,
        platform
      },
      platform,
      ipAddress,
      success: true,
      metadata: {
        compliance_required: true,
        admin_action: true
      }
    });
  }

  static async logSecurityEvent(userId: string, eventType: string, details: any, platform: Platform, ipAddress: string, success: boolean = true): Promise<void> {
    await this.logAction({
      userId,
      action: 'SECURITY_EVENT' as AuditAction,
      resource: 'security',
      details: {
        event_type: eventType,
        ...details,
        platform
      },
      platform,
      ipAddress,
      success,
      metadata: {
        security_event: true,
        alert_required: !success
      }
    });
  }

  static async logAdminAction(adminId: string, action: string, targetUserId: string, details: any, platform: Platform, ipAddress: string): Promise<void> {
    await this.logAction({
      userId: adminId,
      action: AuditAction.ADMIN_ACTION,
      resource: 'admin_operation',
      resourceId: targetUserId,
      details: {
        admin_action: action,
        target_user: targetUserId,
        ...details,
        platform
      },
      platform,
      ipAddress,
      success: true,
      metadata: {
        admin_action: true,
        compliance_required: true
      }
    });
  }

  static async logDataAccess(userId: string, dataType: string, operation: string, platform: Platform, ipAddress: string): Promise<void> {
    await this.logAction({
      userId,
      action: 'DATA_ACCESS' as AuditAction,
      resource: 'user_data',
      details: {
        data_type: dataType,
        operation,
        platform
      },
      platform,
      ipAddress,
      success: true,
      metadata: {
        data_access: true,
        privacy_relevant: true
      }
    });
  }

  static async logSuspiciousActivity(userId: string, activityType: string, riskScore: number, details: any, platform: Platform, ipAddress: string): Promise<void> {
    await this.logAction({
      userId,
      action: 'SUSPICIOUS_ACTIVITY' as AuditAction,
      resource: 'fraud_detection',
      details: {
        activity_type: activityType,
        risk_score: riskScore,
        ...details,
        platform
      },
      platform,
      ipAddress,
      success: true,
      metadata: {
        fraud_detection: true,
        risk_score: riskScore,
        alert_required: riskScore > 70
      }
    });
  }

  static async getAuditTrail(userId: string, startDate: Date, endDate: Date, actions?: AuditAction[]): Promise<any[]> {
    try {
      const query: any = {
        user_id: userId,
        created_at: {
          $gte: startDate,
          $lte: endDate
        }
      };

      if (actions && actions.length > 0) {
        query.action = { $in: actions };
      }

      const auditLogs = await AuditLog.find(query)
        .sort({ created_at: -1 })
        .lean();

      return auditLogs.map(log => ({
        id: log._id.toString(),
        action: log.action,
        resource: log.resource_type,
        details: log.details,
        platform: log.metadata?.platform || 'unknown',
        ipAddress: log.ip_address,
        timestamp: log.created_at,
        success: log.metadata?.success || false
      }));

    } catch (error: any) {
      logger.error('Error retrieving audit trail:', error);
      return [];
    }
  }

  static async getSecurityMetrics(timeframe: 'hour' | 'day' | 'week' = 'day'): Promise<any> {
    try {
      const now = new Date();
      let startDate: Date;

      switch (timeframe) {
        case 'hour':
          startDate = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      }

      const [
        totalEvents,
        failedLogins,
        suspiciousActivities,
        transactionEvents,
        adminActions
      ] = await Promise.all([
        AuditLog.countDocuments({ created_at: { $gte: startDate } }),
        AuditLog.countDocuments({ 
          action: AuditAction.USER_LOGIN, 
          success: false, 
          created_at: { $gte: startDate } 
        }),
        AuditLog.countDocuments({ 
          action: 'SUSPICIOUS_ACTIVITY', 
          created_at: { $gte: startDate } 
        }),
        AuditLog.countDocuments({ 
          action: AuditAction.TRANSACTION_CREATE, 
          created_at: { $gte: startDate } 
        }),
        AuditLog.countDocuments({ 
          action: AuditAction.ADMIN_ACTION, 
          created_at: { $gte: startDate } 
        })
      ]);

      return {
        timeframe,
        period: { start: startDate, end: now },
        metrics: {
          totalEvents,
          failedLogins,
          suspiciousActivities,
          transactionEvents,
          adminActions,
          securityScore: this.calculateSecurityScore(failedLogins, suspiciousActivities, totalEvents)
        }
      };

    } catch (error: any) {
      logger.error('Error getting security metrics:', error);
      return null;
    }
  }

  // backward compatibility
  static async log(data: AuditLogData): Promise<void> {
    return this.logAction(data);
  }

  private static calculateSecurityScore(failedLogins: number, suspiciousActivities: number, totalEvents: number): number {
    if (totalEvents === 0) return 100;

    const failureRate = (failedLogins + suspiciousActivities) / totalEvents;
    const score = Math.max(0, 100 - (failureRate * 100));

    return Math.round(score);
  }
}
