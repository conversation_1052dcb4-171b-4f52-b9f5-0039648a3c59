// import { FastifyInstance, FastifyPluginOptions } from 'fastify';
// import { AuthMiddleware } from '../middleware/auth.middleware';
// import * as exportController from '../controllers/export.controller';

// export async function exportRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
//   fastify.get('/users', { preHandler: AuthMiddleware.authenticateRequest }, exportController.exportUsers as any);
//   fastify.get('/transactions', { preHandler: AuthMiddleware.authenticateRequest }, exportController.exportTransactions as any);
//   fastify.get('/agents', { preHandler: AuthMiddleware.authenticateRequest }, exportController.exportAgents as any);
//   fastify.get('/report', { preHandler: AuthMiddleware.authenticateRequest }, exportController.generateAdminReport as any);
//   fastify.get('/download/:filename', { preHandler: AuthMiddleware.authenticateRequest }, exportController.downloadExport as any);

//   fastify.get('/health', async () => {
//     return {
//       success: true,
//       message: 'export service running',
//       data: { 
//         status: 'ok',
//         features: ['csv_export', 'json_export', 'admin_reports', 'file_download']
//       }
//     };
//   });
// }
