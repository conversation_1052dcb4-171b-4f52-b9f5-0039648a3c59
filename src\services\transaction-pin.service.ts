import { User } from '../models/user.model';
import { CryptoUtils } from '../utils/crypto';
import { logger, securityLogger } from '../config/logger';
import { Platform } from '../types';

export class TransactionPinService {
  private static pinAttemptCache = new Map<string, { attempts: number; lastAttempt: Date; lockedUntil?: Date }>();
  
  // rate limiting - max 3 attempts per 30 minutes
  private static readonly MAX_ATTEMPTS = 3;
  private static readonly LOCKOUT_DURATION = 30 * 60 * 1000; // 30 minutes
  private static readonly ATTEMPT_WINDOW = 30 * 60 * 1000; // 30 minutes
  
  static async validatePin(userId: string, pin: string, platform: Platform, ipAddress?: string): Promise<{ 
    isValid: boolean; 
    message: string; 
    attemptsRemaining?: number;
    lockedUntil?: Date;
  }> {
    try {
      // check rate limiting
      const rateLimitCheck = this.checkRateLimit(userId);
      if (!rateLimitCheck.allowed) {
        securityLogger.warn('Transaction PIN rate limit exceeded', {
          userId,
          platform,
          ipAddress,
          attempts: rateLimitCheck.attempts,
          lockedUntil: rateLimitCheck.lockedUntil
        });
        
        return {
          isValid: false,
          message: 'too many failed attempts, account temporarily locked',
          ...(rateLimitCheck.lockedUntil && { lockedUntil: rateLimitCheck.lockedUntil })
        };
      }
      
      const user = await User.findById(userId);
      if (!user) {
        securityLogger.warn('Transaction PIN validation failed - user not found', {
          userId,
          platform,
          ipAddress
        });
        
        return {
          isValid: false,
          message: 'user not found'
        };
      }
      
      if (!user.transaction_pin_set || !user.transaction_pin) {
        return {
          isValid: false,
          message: 'transaction PIN not set'
        };
      }
      
      const isValid = await CryptoUtils.verifyPassword(pin, user.transaction_pin);
      
      if (!isValid) {
        // increment failed attempts
        this.incrementFailedAttempts(userId);
        
        const cacheEntry = this.pinAttemptCache.get(userId);
        const attemptsRemaining = this.MAX_ATTEMPTS - (cacheEntry?.attempts || 0);
        
        securityLogger.warn('Transaction PIN validation failed', {
          userId,
          platform,
          ipAddress,
          attemptsRemaining
        });
        
        return {
          isValid: false,
          message: 'invalid transaction PIN',
          attemptsRemaining: Math.max(0, attemptsRemaining)
        };
      }
      
      // clear failed attempts on successful validation
      this.pinAttemptCache.delete(userId);
      
      securityLogger.info('Transaction PIN validated successfully', {
        userId,
        platform,
        ipAddress
      });
      
      return {
        isValid: true,
        message: 'transaction PIN validated'
      };
      
    } catch (error: any) {
      logger.error('Transaction PIN validation error:', error);
      return {
        isValid: false,
        message: 'validation failed'
      };
    }
  }
  
  static async changePin(userId: string, currentPin: string, newPin: string, platform: Platform, ipAddress?: string): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      // validate current PIN first
      const currentPinValidation = await this.validatePin(userId, currentPin, platform, ipAddress);
      
      if (!currentPinValidation.isValid) {
        return {
          success: false,
          message: currentPinValidation.message
        };
      }
      
      // validate new PIN format
      if (newPin.length !== 4 || !/^\d{4}$/.test(newPin)) {
        return {
          success: false,
          message: 'new PIN must be exactly 4 digits'
        };
      }
      
      // check if new PIN is different from current
      if (currentPin === newPin) {
        return {
          success: false,
          message: 'new PIN must be different from current PIN'
        };
      }
      
      const user = await User.findById(userId);
      if (!user) {
        return {
          success: false,
          message: 'user not found'
        };
      }
      
      const hashedNewPin = await CryptoUtils.hashPassword(newPin);
      
      user.transaction_pin = hashedNewPin;
      await user.save();
      this.pinAttemptCache.delete(userId);
      
      securityLogger.info('Transaction PIN changed successfully', {
        userId,
        platform,
        ipAddress
      });
      
      return {
        success: true,
        message: 'transaction PIN changed successfully'
      };
      
    } catch (error: any) {
      logger.error('Transaction PIN change error:', error);
      return {
        success: false,
        message: 'failed to change PIN'
      };
    }
  }
  
  static async resetPin(userId: string, newPin: string, platform: Platform, ipAddress?: string): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      // validate new PIN format
      if (newPin.length !== 4 || !/^\d{4}$/.test(newPin)) {
        return {
          success: false,
          message: 'PIN must be exactly 4 digits'
        };
      }
      
      const user = await User.findById(userId);
      if (!user) {
        return {
          success: false,
          message: 'user not found'
        };
      }
      
      // hash new PIN
      const hashedNewPin = await CryptoUtils.hashPassword(newPin);
      
      user.transaction_pin = hashedNewPin;
      user.transaction_pin_set = true;
      await user.save();
      this.pinAttemptCache.delete(userId);
      
      securityLogger.info('Transaction PIN reset successfully', {
        userId,
        platform,
        ipAddress
      });
      
      return {
        success: true,
        message: 'transaction PIN reset successfully'
      };
      
    } catch (error: any) {
      logger.error('Transaction PIN reset error:', error);
      return {
        success: false,
        message: 'failed to reset PIN'
      };
    }
  }
  
  static async verifyPinForTransaction(userId: string, pin: string, transactionAmount: number, platform: Platform, ipAddress?: string): Promise<{
    authorized: boolean;
    message: string;
    transactionId?: string;
  }> {
    try {
      const validation = await this.validatePin(userId, pin, platform, ipAddress);
      
      if (!validation.isValid) {
        return {
          authorized: false,
          message: validation.message
        };
      }
      
      const transactionId = `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      securityLogger.info('Transaction authorized with PIN', {
        userId,
        transactionAmount,
        transactionId,
        platform,
        ipAddress
      });
      
      return {
        authorized: true,
        message: 'transaction authorized',
        transactionId
      };
      
    } catch (error: any) {
      logger.error('Transaction PIN authorization error:', error);
      return {
        authorized: false,
        message: 'authorization failed'
      };
    }
  }
  
  static isPinLocked(userId: string): boolean {
    const cacheEntry = this.pinAttemptCache.get(userId);
    if (!cacheEntry || !cacheEntry.lockedUntil) {
      return false;
    }
    
    return cacheEntry.lockedUntil > new Date();
  }
  
  static getPinLockInfo(userId: string): { isLocked: boolean; lockedUntil?: Date; attemptsRemaining: number } {
    const cacheEntry = this.pinAttemptCache.get(userId);
    
    if (!cacheEntry) {
      return { isLocked: false, attemptsRemaining: this.MAX_ATTEMPTS };
    }
    
    const isLocked = this.isPinLocked(userId);
    const attemptsRemaining = Math.max(0, this.MAX_ATTEMPTS - cacheEntry.attempts);
    
    return {
      isLocked,
      attemptsRemaining,
      ...(cacheEntry.lockedUntil && { lockedUntil: cacheEntry.lockedUntil })
    };
  }
  
  private static checkRateLimit(userId: string): { allowed: boolean; attempts: number; lockedUntil?: Date } {
    const cacheEntry = this.pinAttemptCache.get(userId);
    
    if (!cacheEntry) {
      return { allowed: true, attempts: 0 };
    }
    
    if (cacheEntry.lockedUntil && cacheEntry.lockedUntil > new Date()) {
      return { allowed: false, attempts: cacheEntry.attempts, lockedUntil: cacheEntry.lockedUntil };
    }
    
    const timeSinceLastAttempt = Date.now() - cacheEntry.lastAttempt.getTime();
    if (timeSinceLastAttempt > this.ATTEMPT_WINDOW) {
      this.pinAttemptCache.delete(userId);
      return { allowed: true, attempts: 0 };
    }
    
    if (cacheEntry.attempts >= this.MAX_ATTEMPTS) {
      const lockedUntil = new Date(Date.now() + this.LOCKOUT_DURATION);
      cacheEntry.lockedUntil = lockedUntil;
      return { allowed: false, attempts: cacheEntry.attempts, lockedUntil };
    }
    
    return { allowed: true, attempts: cacheEntry.attempts };
  }
  
  private static incrementFailedAttempts(userId: string): void {
    const cacheEntry = this.pinAttemptCache.get(userId);
    
    if (cacheEntry) {
      cacheEntry.attempts += 1;
      cacheEntry.lastAttempt = new Date();
      
      if (cacheEntry.attempts >= this.MAX_ATTEMPTS) {
        cacheEntry.lockedUntil = new Date(Date.now() + this.LOCKOUT_DURATION);
      }
    } else {
      this.pinAttemptCache.set(userId, {
        attempts: 1,
        lastAttempt: new Date()
      });
    }
  }
  
  static cleanupExpiredEntries(): void {
    const now = new Date();
    
    for (const [userId, entry] of this.pinAttemptCache.entries()) {
      const timeSinceLastAttempt = now.getTime() - entry.lastAttempt.getTime();
      
      if (timeSinceLastAttempt > this.ATTEMPT_WINDOW && (!entry.lockedUntil || entry.lockedUntil < now)) {
        this.pinAttemptCache.delete(userId);
      }
    }
  }
}

// cleanup expired entries every 15 minutes
setInterval(() => {
  TransactionPinService.cleanupExpiredEntries();
}, 15 * 60 * 1000);
