import { User } from '../models/user.model';
import { Transaction } from '../models/transaction.model';
import { Agent } from '../models/agent.model';
import { logger } from '../config/logger';
import * as fs from 'fs/promises';
import * as path from 'path';

interface ExportOptions {
  format: 'csv' | 'json' | 'excel';
  dateFrom?: string;
  dateTo?: string;
  filters?: any;
  includeHeaders?: boolean;
}

export class ExportService {
  private static readonly EXPORT_DIR = path.join(process.cwd(), 'exports');

  private static async ensureExportDir(): Promise<void> {
    try {
      await fs.access(this.EXPORT_DIR);
    } catch {
      await fs.mkdir(this.EXPORT_DIR, { recursive: true });
    }
  }

  static async exportUsers(options: ExportOptions): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      await this.ensureExportDir();

      const { dateFrom, dateTo, filters = {}, format = 'csv' } = options;

      const query: any = { ...filters };
      if (dateFrom || dateTo) {
        query.created_at = {};
        if (dateFrom) query.created_at.$gte = new Date(dateFrom);
        if (dateTo) query.created_at.$lte = new Date(dateTo);
      }

      const users = await User.find(query)
        .select('-password -transaction_pin')
        .sort({ created_at: -1 })
        .lean();

      const exportData = users.map(user => ({
        ID: user._id.toString(),
        'First Name': user.first_name,
        'Last Name': user.last_name,
        Email: user.email,
        Phone: user.phone,
        Username: user.username,
        Role: user.role,
        'Account Status': user.account_status,
        'Is Verified': user.is_verified ? 'Yes' : 'No',
        'KYC Status': user.kyc_status,
        'Wallet Balance': user.wallet_balance || 0,
        'Created At': user.created_at?.toISOString(),
        'Last Login': user.last_login?.toISOString() || 'Never',
        Country: user.address?.country || '',
        City: user.address?.city || ''
      }));

      const filename = `users_export_${Date.now()}.${format}`;
      const filepath = path.join(this.EXPORT_DIR, filename);

      if (format === 'csv') {
        const csvContent = this.convertToCSV(exportData);
        await fs.writeFile(filepath, csvContent, 'utf8');
      } else if (format === 'json') {
        await fs.writeFile(filepath, JSON.stringify(exportData, null, 2), 'utf8');
      }

      logger.info('Users export completed', {
        format,
        recordCount: exportData.length,
        filename
      });

      return {
        success: true,
        data: {
          filename,
          filepath,
          recordCount: exportData.length,
          format,
          downloadUrl: `/api/v1/exports/download/${filename}`
        },
        message: 'users export completed successfully'
      };
    } catch (error: any) {
      logger.error('Error exporting users:', error);
      return {
        success: false,
        message: 'failed to export users data'
      };
    }
  }

  static async exportTransactions(options: ExportOptions): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      await this.ensureExportDir();

      const { dateFrom, dateTo, filters = {}, format = 'csv' } = options;

      const query: any = { ...filters };
      if (dateFrom || dateTo) {
        query.created_at = {};
        if (dateFrom) query.created_at.$gte = new Date(dateFrom);
        if (dateTo) query.created_at.$lte = new Date(dateTo);
      }

      const transactions = await Transaction.find(query)
        .populate('user_id', 'first_name last_name email')
        .populate('recipient_id', 'first_name last_name email')
        .sort({ created_at: -1 })
        .lean();

      const exportData = transactions.map(txn => ({
        ID: txn._id.toString(),
        Type: txn.type,
        Amount: txn.amount,
        Currency: txn.currency,
        Status: txn.status,
        Description: txn.description,
        Reference: txn.external_reference || txn._id.toString(),
        Fee: txn.fee || 0,
        'User Name': (txn.user_id as any) ? 
          `${(txn.user_id as any).first_name} ${(txn.user_id as any).last_name}` : 'Unknown',
        'User Email': (txn.user_id as any)?.email || '',
        'Recipient Name': (txn.recipient_id as any) ? 
          `${(txn.recipient_id as any).first_name} ${(txn.recipient_id as any).last_name}` : '',
        'Recipient Email': (txn.recipient_id as any)?.email || '',
        'Created At': txn.created_at?.toISOString(),
        'Updated At': txn.updated_at?.toISOString(),
        Channel: (txn.metadata as any)?.channel || 'web',
        'Agent Commission': (txn.metadata as any)?.agentCommission || 0
      }));

      const filename = `transactions_export_${Date.now()}.${format}`;
      const filepath = path.join(this.EXPORT_DIR, filename);

      if (format === 'csv') {
        const csvContent = this.convertToCSV(exportData);
        await fs.writeFile(filepath, csvContent, 'utf8');
      } else if (format === 'json') {
        await fs.writeFile(filepath, JSON.stringify(exportData, null, 2), 'utf8');
      }

      logger.info('Transactions export completed', {
        format,
        recordCount: exportData.length,
        filename
      });

      return {
        success: true,
        data: {
          filename,
          filepath,
          recordCount: exportData.length,
          format,
          downloadUrl: `/api/v1/exports/download/${filename}`
        },
        message: 'transactions export completed successfully'
      };
    } catch (error: any) {
      logger.error('Error exporting transactions:', error);
      return {
        success: false,
        message: 'failed to export transactions data'
      };
    }
  }

  static async exportAgents(options: ExportOptions): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      await this.ensureExportDir();

      const { dateFrom, dateTo, filters = {}, format = 'csv' } = options;

      const query: any = { ...filters };
      if (dateFrom || dateTo) {
        query.created_at = {};
        if (dateFrom) query.created_at.$gte = new Date(dateFrom);
        if (dateTo) query.created_at.$lte = new Date(dateTo);
      }

      const agents = await Agent.find(query)
        .populate('user_id', 'first_name last_name email')
        .sort({ created_at: -1 })
        .lean();

      const exportData = agents.map(agent => ({
        ID: agent._id.toString(),
        'Agent Code': agent.agent_code,
        'Business Name': agent.business_name,
        'Business Type': agent.business_type,
        'Registration Number': agent.business_registration_number || '',
        'Tax ID': agent.tax_id || '',
        Status: agent.agent_status,
        'Is Active': agent.is_active ? 'Yes' : 'No',
        'KYC Status': agent.kyc_status,
        'Performance Rating': agent.performance_rating || 0,
        'Contact Phone': agent.contact_info.phone,
        'Contact Email': agent.contact_info.email,
        'WhatsApp': agent.contact_info.whatsapp || '',
        Address: agent.location.address,
        City: agent.location.city,
        State: agent.location.state,
        Country: agent.location.country,
        'Float Balance': agent.wallet_info.float_balance,
        'Commission Balance': agent.wallet_info.commission_balance,
        'Total Transactions': agent.statistics.total_transactions,
        'Total Volume': agent.statistics.total_volume,
        'Total Commission': agent.statistics.total_commission_earned,
        'Cash In Rate': agent.commission_structure.cash_in_rate,
        'Cash Out Rate': agent.commission_structure.cash_out_rate,
        'Daily Cash In Limit': agent.transaction_limits.daily_cash_in_limit,
        'Daily Cash Out Limit': agent.transaction_limits.daily_cash_out_limit,
        'User Name': (agent.user_id as any) ? 
          `${(agent.user_id as any).first_name} ${(agent.user_id as any).last_name}` : 'Unknown',
        'User Email': (agent.user_id as any)?.email || '',
        'Created At': agent.created_at?.toISOString(),
        'Last Active': agent.last_active_date?.toISOString() || 'Never'
      }));

      const filename = `agents_export_${Date.now()}.${format}`;
      const filepath = path.join(this.EXPORT_DIR, filename);

      if (format === 'csv') {
        const csvContent = this.convertToCSV(exportData);
        await fs.writeFile(filepath, csvContent, 'utf8');
      } else if (format === 'json') {
        await fs.writeFile(filepath, JSON.stringify(exportData, null, 2), 'utf8');
      }

      logger.info('Agents export completed', {
        format,
        recordCount: exportData.length,
        filename
      });

      return {
        success: true,
        data: {
          filename,
          filepath,
          recordCount: exportData.length,
          format,
          downloadUrl: `/api/v1/exports/download/${filename}`
        },
        message: 'agents export completed successfully'
      };
    } catch (error: any) {
      logger.error('Error exporting agents:', error);
      return {
        success: false,
        message: 'failed to export agents data'
      };
    }
  }

  private static convertToCSV(data: any[]): string {
    if (!data.length) return '';

    const headers = Object.keys(data[0]);
    const csvRows = [headers.join(',')];

    for (const row of data) {
      const values = headers.map(header => {
        const value = row[header];
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value || '';
      });
      csvRows.push(values.join(','));
    }

    return csvRows.join('\n');
  }

  static async getExportFile(filename: string): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      const filepath = path.join(this.EXPORT_DIR, filename);
      
      try {
        await fs.access(filepath);
      } catch {
        return {
          success: false,
          message: 'export file not found'
        };
      }

      const stats = await fs.stat(filepath);
      const fileContent = await fs.readFile(filepath);

      return {
        success: true,
        data: {
          filename,
          content: fileContent,
          size: stats.size,
          mimeType: this.getMimeType(filename)
        },
        message: 'export file retrieved successfully'
      };
    } catch (error: any) {
      logger.error('Error getting export file:', error);
      return {
        success: false,
        message: 'failed to retrieve export file'
      };
    }
  }

  private static getMimeType(filename: string): string {
    const ext = path.extname(filename).toLowerCase();
    switch (ext) {
      case '.csv': return 'text/csv';
      case '.json': return 'application/json';
      case '.xlsx': return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case '.pdf': return 'application/pdf';
      default: return 'application/octet-stream';
    }
  }

  static async cleanupOldExports(maxAgeHours: number = 24): Promise<void> {
    try {
      await this.ensureExportDir();
      const files = await fs.readdir(this.EXPORT_DIR);
      const cutoffTime = Date.now() - (maxAgeHours * 60 * 60 * 1000);

      for (const file of files) {
        const filepath = path.join(this.EXPORT_DIR, file);
        const stats = await fs.stat(filepath);
        
        if (stats.mtime.getTime() < cutoffTime) {
          await fs.unlink(filepath);
          logger.info('Cleaned up old export file', { filename: file });
        }
      }
    } catch (error: any) {
      logger.error('Error cleaning up old exports:', error);
    }
  }
}
