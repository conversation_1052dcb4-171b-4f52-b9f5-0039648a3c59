console.log('Starting test...');

try {
  console.log('Testing config import...');
  const { config } = require('./src/config');
  console.log('Config loaded successfully');
  
  console.log('Testing database import...');
  const { db } = require('./src/config/database');
  console.log('Database config loaded successfully');
  
  console.log('Testing logger import...');
  const { logger } = require('./src/config/logger');
  console.log('Logger loaded successfully');
  
  console.log('All imports successful!');
} catch (error) {
  console.error('Import error:', error);
}
