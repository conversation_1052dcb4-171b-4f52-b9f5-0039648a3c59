import Joi from 'joi';
import { CardType, CardBrand } from '../models/card.model';
import { Currency } from '../types';

export const createCardRequest = Joi.object({
  card_type: Joi.string().valid(...Object.values(CardType)).required(),
  card_brand: Joi.string().valid(...Object.values(CardBrand)).optional(),
  wallet_id: Joi.string().required(),
  delivery_address: Joi.object({
    street: Joi.string().required(),
    city: Joi.string().required(),
    state: Joi.string().required(),
    country: Joi.string().required(),
    postal_code: Joi.string().required()
  }).when('card_type', {
    is: CardType.PHYSICAL,
    then: Joi.required(),
    otherwise: Joi.optional()
  })
});

export const updateCardLimitsRequest = Joi.object({
  limits: Joi.object({
    daily_spend_limit: Joi.number().min(0).optional(),
    monthly_spend_limit: Joi.number().min(0).optional(),
    atm_withdrawal_limit: Joi.number().min(0).optional(),
    online_transaction_limit: Joi.number().min(0).optional(),
    pos_transaction_limit: Joi.number().min(0).optional()
  }).required()
});

export const updateCardSettingsRequest = Joi.object({
  settings: Joi.object({
    international_transactions: Joi.boolean().optional(),
    online_transactions: Joi.boolean().optional(),
    atm_withdrawals: Joi.boolean().optional(),
    pos_transactions: Joi.boolean().optional(),
    contactless_payments: Joi.boolean().optional()
  }).required()
});

export const lockCardRequest = Joi.object({
  reason: Joi.string().max(200).optional()
});

export const blockCardRequest = Joi.object({
  reason: Joi.string().max(200).optional()
});

export const changeCardPinRequest = Joi.object({
  old_pin: Joi.string().pattern(/^\d{4}$/).required(),
  new_pin: Joi.string().pattern(/^\d{4}$/).required()
});

export const bankTransferRequest = Joi.object({
  amount: Joi.number().min(100).max(1000000).required(),
  currency: Joi.string().valid(...Object.values(Currency)).required(),
  bank_code: Joi.string().length(3).required(),
  platform: Joi.string().valid('web', 'mobile', 'api').optional()
});

export const cardPaymentRequest = Joi.object({
  amount: Joi.number().min(100).max(500000).required(),
  currency: Joi.string().valid(...Object.values(Currency)).required(),
  card_number: Joi.string().pattern(/^\d{16}$/).required(),
  expiry_month: Joi.string().pattern(/^(0[1-9]|1[0-2])$/).required(),
  expiry_year: Joi.string().pattern(/^\d{4}$/).required(),
  cvv: Joi.string().pattern(/^\d{3,4}$/).required(),
  card_holder_name: Joi.string().min(2).max(50).required(),
  platform: Joi.string().valid('web', 'mobile', 'api').optional()
});

export const agentDepositRequest = Joi.object({
  amount: Joi.number().min(500).max(200000).required(),
  currency: Joi.string().valid(...Object.values(Currency)).required(),
  agent_id: Joi.string().required(),
  platform: Joi.string().valid('web', 'mobile', 'api').optional()
});

export const findAgentsRequest = Joi.object({
  latitude: Joi.number().min(-90).max(90).required(),
  longitude: Joi.number().min(-180).max(180).required(),
  radius: Joi.number().min(1000).max(50000).optional(),
  limit: Joi.number().min(1).max(50).optional()
});
