import { v2 as cloudinary } from 'cloudinary';
import { FastifyRequest } from 'fastify';
import fs from 'fs';
import path from 'path';
import { extractPublicId } from 'cloudinary-build-url';
import { logger } from '../config/logger';

if (process.env.CLOUDINARY_CLOUD_NAME && process.env.CLOUDINARY_API_KEY && process.env.CLOUDINARY_API_SECRET) {
  cloudinary.config({
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET,
  });
}

const tmpDir = process.env.NODE_ENV === 'production' ? '/tmp' : path.resolve(__dirname, '../uploads/temp');

if (!fs.existsSync(tmpDir)) {
  fs.mkdirSync(tmpDir, { recursive: true });
}

export type UploadType =
  | 'profilePicture'
  | 'idDocumentFront'
  | 'idDocumentBack'
  | 'selfiePhoto'
  | 'documents'
  | 'chatMedia';

export const validateConfig = (): boolean => {
  const { cloud_name, api_key, api_secret } = cloudinary.config();
  return !!(cloud_name && api_key && api_secret);
};

export const uploadFile = async (filePath: string, folder: string): Promise<string> => {
  try {
    const result = await cloudinary.uploader.upload(filePath, {
      folder,
      resource_type: 'auto',
      allowed_formats: ['jpg', 'png', 'jpeg', 'gif', 'webp', 'pdf'],
    });

    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    return result.secure_url;
  } catch (error) {
    logger.error('Cloudinary upload failed:', error);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    throw new Error('File upload failed');
  }
};

export const uploadBuffer = async (
  buffer: Buffer,
  options: {
    folder?: string;
    resource_type?: 'auto' | 'raw' | 'image' | 'video';
    public_id?: string;
  } = {}
): Promise<{
  secure_url: string;
  public_id: string;
  resource_type: string;
  format: string;
  bytes: number;
}> => {
  try {
    return new Promise((resolve, reject) => {
      const uploadOptions: any = {
        folder: options.folder || process.env.CLOUDINARY_FOLDER || 'aetrust',
        resource_type: (options.resource_type as any) || 'auto',
        allowed_formats: ['jpg', 'png', 'jpeg', 'gif', 'webp', 'pdf', 'mp4', 'mov', 'avi'],
      };

      if (options.public_id) {
        uploadOptions.public_id = options.public_id;
      }

      cloudinary.uploader.upload_stream(
        uploadOptions,
        (error, result) => {
          if (error) {
            logger.error('Cloudinary buffer upload failed:', error);
            reject(new Error('File upload failed'));
          } else if (result) {
            resolve({
              secure_url: result.secure_url,
              public_id: result.public_id,
              resource_type: result.resource_type,
              format: result.format,
              bytes: result.bytes
            });
          } else {
            reject(new Error('Upload result is undefined'));
          }
        }
      ).end(buffer);
    });
  } catch (error) {
    logger.error('Cloudinary buffer upload failed:', error);
    throw new Error('File upload failed');
  }
};

export const deleteFile = async (imageUrlOrPublicId: string): Promise<boolean> => {
  try {
    let publicId: string;

    if (imageUrlOrPublicId.startsWith('http://') || imageUrlOrPublicId.startsWith('https://')) {
      publicId = extractPublicId(imageUrlOrPublicId);
      if (!publicId) {
        throw new Error('Invalid image URL - could not extract public ID');
      }
    } else {
      publicId = imageUrlOrPublicId;
    }

    if (!publicId) {
      throw new Error('Invalid image URL or public ID');
    }

    const result = await cloudinary.uploader.destroy(publicId);
    return result.result === 'ok';
  } catch (error) {
    logger.error('Cloudinary delete failed:', error);
    return false;
  }
};

export const deleteByPublicId = async (publicId: string): Promise<boolean> => {
  try {
    if (!publicId) {
      throw new Error('Public ID is required');
    }

    const result = await cloudinary.uploader.destroy(publicId);
    return result.result === 'ok';
  } catch (error) {
    logger.error('Cloudinary delete by public ID failed:', error);
    return false;
  }
};

export const getOptimizedUrl = (
  imageUrl: string,
  options: {
    width?: number;
    height?: number;
    quality?: string;
    format?: string;
    crop?: string;
  } = {}
): string => {
  try {
    const publicId = extractPublicId(imageUrl);
    if (!publicId) return imageUrl;

    const transformations: string[] = [];

    if (options.width) transformations.push(`w_${options.width}`);
    if (options.height) transformations.push(`h_${options.height}`);
    if (options.quality) transformations.push(`q_${options.quality}`);
    if (options.format) transformations.push(`f_${options.format}`);
    if (options.crop) transformations.push(`c_${options.crop}`);

    const transformationString = transformations.join(',');

    return cloudinary.url(publicId, {
      transformation: transformationString || undefined,
      secure: true
    });
  } catch (error) {
    logger.error('Error generating optimized URL:', error);
    return imageUrl;
  }
};

export const uploadSingleFile = async (request: FastifyRequest, uploadType: UploadType): Promise<string | null> => {
  try {
    const data = await request.file();
    if (!data) {
      return null;
    }

    const folderMap: Record<UploadType, string> = {
      profilePicture: `${process.env.CLOUDINARY_FOLDER || 'aetrust'}/profile_pictures`,
      idDocumentFront: `${process.env.CLOUDINARY_FOLDER || 'aetrust'}/kyc/id_documents`,
      idDocumentBack: `${process.env.CLOUDINARY_FOLDER || 'aetrust'}/kyc/id_documents`,
      selfiePhoto: `${process.env.CLOUDINARY_FOLDER || 'aetrust'}/kyc/selfies`,
      documents: `${process.env.CLOUDINARY_FOLDER || 'aetrust'}/documents`,
      chatMedia: `${process.env.CLOUDINARY_FOLDER || 'aetrust'}/chat_media`,
    };

    const folder = folderMap[uploadType];

    const result = await uploadStream(data.file, {
      folder,
      uploadType,
      mimetype: data.mimetype,
      filename: data.filename
    });

    return result.secure_url;
  } catch (error) {
    logger.error('Failed to upload file:', error);
    return null;
  }
};

export const uploadStream = async (
  fileStream: NodeJS.ReadableStream,
  options: {
    folder: string;
    uploadType: UploadType;
    mimetype?: string;
    filename?: string;
  }
): Promise<{
  secure_url: string;
  public_id: string;
  resource_type: string;
  format: string;
  bytes: number;
  duration?: number;
}> => {
  return new Promise((resolve, reject) => {
    const isVideo = !!(options.mimetype?.startsWith('video/') ||
                      options.filename?.match(/\.(mp4|mov|avi|mkv|webm)$/i));

    const uploadOptions: any = {
      folder: options.folder,
      resource_type: isVideo ? 'video' : 'auto',
      allowed_formats: isVideo
        ? ['mp4', 'mov', 'avi', 'mkv', 'webm']
        : ['jpg', 'png', 'jpeg', 'gif', 'webp', 'pdf'],
      transformation: getTransformationForType(options.uploadType, isVideo),
      chunk_size: 6000000, // 6MB chunks for better streaming
    };

    if (isVideo) {
      uploadOptions.video_codec = 'h264';
      uploadOptions.audio_codec = 'aac';
      uploadOptions.quality = 'auto:good';
    }

    if (!isVideo && (options.uploadType === 'profilePicture' || options.uploadType === 'selfiePhoto')) {
      uploadOptions.transformation = [
        { width: 400, height: 400, crop: 'fill', gravity: 'face' },
        { quality: 'auto:good', format: 'auto' }
      ];
    }

    const uploadStream = cloudinary.uploader.upload_stream(
      uploadOptions,
      (error, result) => {
        if (error) {
          logger.error('Cloudinary stream upload failed:', error);
          reject(new Error(`Upload failed: ${error.message}`));
        } else if (result) {
          logger.info(`File uploaded successfully: ${result.public_id}, Size: ${result.bytes} bytes`);
          resolve({
            secure_url: result.secure_url,
            public_id: result.public_id,
            resource_type: result.resource_type,
            format: result.format,
            bytes: result.bytes,
            duration: result.duration
          });
        } else {
          reject(new Error('Upload result is undefined'));
        }
      }
    );

    fileStream.pipe(uploadStream);

    fileStream.on('error', (error) => {
      logger.error('File stream error:', error);
      reject(new Error(`Stream error: ${error.message}`));
    });
  });
};

const getTransformationForType = (uploadType: UploadType, isVideo: boolean): any => {
  if (isVideo) {
    return { quality: 'auto:good' };
  } else {
    switch (uploadType) {
      case 'profilePicture':
      case 'selfiePhoto':
        return [
          { width: 400, height: 400, crop: 'fill', gravity: 'face' },
          { quality: 'auto:good', format: 'auto' }
        ];
      case 'idDocumentFront':
      case 'idDocumentBack':
        return [
          { width: 1200, height: 800, crop: 'limit' },
          { quality: 'auto:best', format: 'auto' }
        ];
      default:
        return { quality: 'auto:good', format: 'auto' };
    }
  }
};
