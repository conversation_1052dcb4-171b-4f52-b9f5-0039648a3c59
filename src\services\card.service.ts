import { Card, ICard, CardType, CardStatus, CardBrand } from '../models/card.model';
import { User } from '../models/user.model';
import { Wallet } from '../models/wallet.model';
import { CryptoUtils } from '../utils/crypto';
import { logger, securityLogger } from '../config/logger';
import mongoose from 'mongoose';

export class CardService {
  private static readonly CARD_LOCK_DURATION = 24 * 60 * 60 * 1000; // 24 hours

  static async createCard(data: {
    userId: string;
    walletId: string;
    cardType: CardType;
    cardBrand?: CardBrand;
    deliveryAddress?: any;
  }): Promise<ICard> {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();

      const user = await User.findById(data.userId).session(session);
      if (!user) {
        throw new Error('user not found');
      }

      const wallet = await Wallet.findById(data.walletId).session(session);
      if (!wallet || wallet.user_id.toString() !== data.userId) {
        throw new Error('wallet not found or not owned by user');
      }

      const existingCards = await Card.countDocuments({
        user_id: data.userId,
        status: { $in: [CardStatus.ACTIVE, CardStatus.PENDING] }
      }).session(session);

      if (existingCards >= 5) {
        throw new Error('maximum card limit reached');
      }

      const cardNumber = this.generateCardNumber();
      const cvv = this.generateCVV();
      const expiryDate = this.generateExpiryDate();

      const userEncryptionKey = this.generateUserCardKey(data.userId);

      const card = new Card({
        user_id: data.userId,
        wallet_id: data.walletId,
        card_type: data.cardType,
        card_brand: data.cardBrand || CardBrand.VISA,
        card_number: CryptoUtils.encrypt(cardNumber, userEncryptionKey),
        card_holder_name: `${user.first_name} ${user.last_name}`.toUpperCase(),
        expiry_month: expiryDate.month,
        expiry_year: expiryDate.year,
        cvv: CryptoUtils.encrypt(cvv, userEncryptionKey),
        pin: CryptoUtils.encrypt('0000', userEncryptionKey), // default pin
        status: data.cardType === CardType.VIRTUAL ? CardStatus.ACTIVE : CardStatus.PENDING,
        currency: wallet.currency,
        delivery_info: data.cardType === CardType.PHYSICAL ? {
          address: data.deliveryAddress,
          delivery_status: 'pending'
        } : undefined
      });

      await card.save({ session });
      await session.commitTransaction();

      logger.info('Card created successfully', {
        userId: data.userId,
        cardId: card._id,
        cardType: data.cardType
      });

      return card;
    } catch (error: any) {
      await session.abortTransaction();
      logger.error('Error creating card:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async addUserCard(userId: string, cardData: {
    card_number: string;
    card_holder_name: string;
    expiry_month: number;
    expiry_year: number;
    cvv: string;
    pin: string;
  }): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        return {
          success: false,
          message: 'user not found'
        };
      }

      // Validate card number and detect brand
      const cardBrand = this.detectCardBrand(cardData.card_number);
      if (!cardBrand) {
        return {
          success: false,
          message: 'invalid card number'
        };
      }

      // Encrypt card number for duplicate check
      const userEncryptionKey = this.generateUserCardKey(userId);
      const encryptedCardNumber = CryptoUtils.encrypt(cardData.card_number, userEncryptionKey);

      // Check if card already exists
      const existingCard = await Card.findOne({
        card_number: encryptedCardNumber
      });

      if (existingCard) {
        return {
          success: false,
          message: 'card already exists in system'
        };
      }

      const existingCards = await Card.countDocuments({
        user_id: userId,
        status: { $in: [CardStatus.ACTIVE, CardStatus.BLOCKED] }
      });

      if (existingCards >= 5) {
        return {
          success: false,
          message: 'maximum card limit reached'
        };
      }

      // Encrypt sensitive card data with user-specific key
      const encryptedCvv = CryptoUtils.encrypt(cardData.cvv, userEncryptionKey);
      const encryptedPin = CryptoUtils.encrypt(cardData.pin, userEncryptionKey);
      const maskedNumber = this.maskCardNumber(cardData.card_number);

      // Get user's wallet
      const wallet = await Wallet.findOne({ user_id: userId });
      if (!wallet) {
        return {
          success: false,
          message: 'user wallet not found'
        };
      }

      const newCard = new Card({
        user_id: userId,
        wallet_id: wallet._id,
        card_type: this.determineCardType(cardData.card_number),
        card_brand: cardBrand,
        card_number: encryptedCardNumber,
        masked_number: maskedNumber,
        card_holder_name: cardData.card_holder_name.toUpperCase(),
        expiry_month: cardData.expiry_month,
        expiry_year: cardData.expiry_year,
        expiry_date: `${cardData.expiry_month.toString().padStart(2, '0')}/${cardData.expiry_year.toString().slice(-2)}`,
        is_expired: this.isCardExpired(cardData.expiry_month, cardData.expiry_year),
        cvv: encryptedCvv,
        pin: encryptedPin,
        status: CardStatus.ACTIVE,
        currency: 'NGN',
        tier_info: {
          current_tier: 'basic',
          tier_benefits: this.getTierBenefits('basic'),
          upgrade_eligible: true,
          next_tier: 'silver'
        },
        limits: this.getDefaultLimits(this.determineCardType(cardData.card_number)),
        settings: {
          contactless_enabled: true,
          online_transactions_enabled: true,
          international_transactions_enabled: false,
          atm_withdrawals_enabled: true
        },
        ui_preferences: {
          show_balance: true,
          card_theme: 'default'
        },
        security: {
          pin_attempts: 0,
          locked_until: null,
          last_pin_change: new Date()
        },
        created_at: new Date(),
        updated_at: new Date()
      });

      await newCard.save();

      return {
        success: true,
        data: {
          cardId: newCard._id.toString(),
          maskedNumber,
          cardBrand,
          cardType: newCard.card_type,
          status: CardStatus.ACTIVE
        },
        message: 'card added successfully'
      };
    } catch (error: any) {
      logger.error('Error adding card:', error);
      return {
        success: false,
        message: 'failed to add card'
      };
    }
  }

  static async getUserCards(userId: string): Promise<ICard[]> {
    try {
      const cards = await Card.find({
        user_id: userId,
        status: { $in: [CardStatus.ACTIVE, CardStatus.BLOCKED] }
      })
        .populate('wallet_id', 'currency balance wallet_type')
        .sort({ created_at: -1 })
        .lean();

      return cards.map((card: any) => ({
        ...card,
        card_number: undefined, 
        cvv: undefined,
        pin: undefined
      })) as ICard[];
    } catch (error: any) {
      logger.error('Error getting user cards:', error);
      throw error;
    }
  }

  static async getCardDetails(cardId: string, userId: string): Promise<ICard | null> {
    try {
      const card = await Card.findOne({
        _id: cardId,
        user_id: userId
      }).populate('wallet_id', 'currency balance wallet_type');

      if (!card) {
        return null;
      }

      const cardObj = card.toObject();
      return {
        ...cardObj,
        card_number: undefined,
        cvv: undefined,
        pin: undefined
      } as any;
    } catch (error: any) {
      logger.error('Error getting card details:', error);
      return null;
    }
  }

  static async updateCardLimits(cardId: string, userId: string, limits: any): Promise<boolean> {
    try {
      const card = await Card.findOneAndUpdate(
        { _id: cardId, user_id: userId },
        { $set: { limits } },
        { new: true }
      );

      if (!card) {
        return false;
      }

      logger.info('Card limits updated', {
        userId,
        cardId,
        limits
      });

      return true;
    } catch (error: any) {
      logger.error('Error updating card limits:', error);
      return false;
    }
  }

  static async updateCardSettings(cardId: string, userId: string, settings: any): Promise<boolean> {
    try {
      const card = await Card.findOneAndUpdate(
        { _id: cardId, user_id: userId },
        { $set: { settings } },
        { new: true }
      );

      if (!card) {
        return false;
      }

      logger.info('Card settings updated', {
        userId,
        cardId,
        settings
      });

      return true;
    } catch (error: any) {
      logger.error('Error updating card settings:', error);
      return false;
    }
  }

  static async lockCard(cardId: string, userId: string, reason: string): Promise<boolean> {
    try {
      const card = await Card.findOneAndUpdate(
        { _id: cardId, user_id: userId },
        {
          $set: {
            'security.is_locked': true,
            'security.lock_reason': reason,
            'security.locked_until': new Date(Date.now() + this.CARD_LOCK_DURATION)
          }
        },
        { new: true }
      );

      if (!card) {
        return false;
      }

      securityLogger.warn('Card locked', {
        userId,
        cardId,
        reason
      });

      return true;
    } catch (error: any) {
      logger.error('Error locking card:', error);
      return false;
    }
  }

  static async unlockCard(cardId: string, userId: string): Promise<boolean> {
    try {
      const card = await Card.findOneAndUpdate(
        { _id: cardId, user_id: userId },
        {
          $set: {
            'security.is_locked': false,
            'security.lock_reason': '',
            'security.failed_pin_attempts': 0
          },
          $unset: {
            'security.locked_until': 1,
            'security.last_failed_attempt': 1
          }
        },
        { new: true }
      );

      if (!card) {
        return false;
      }

      logger.info('Card unlocked', {
        userId,
        cardId
      });

      return true;
    } catch (error: any) {
      logger.error('Error unlocking card:', error);
      return false;
    }
  }

  static async blockCard(cardId: string, userId: string, reason: string): Promise<boolean> {
    try {
      const card = await Card.findOneAndUpdate(
        { _id: cardId, user_id: userId },
        {
          $set: {
            status: CardStatus.BLOCKED,
            'security.lock_reason': reason
          }
        },
        { new: true }
      );

      if (!card) {
        return false;
      }

      securityLogger.warn('Card blocked', {
        userId,
        cardId,
        reason
      });

      return true;
    } catch (error: any) {
      logger.error('Error blocking card:', error);
      return false;
    }
  }

  static async changeCardPin(cardId: string, userId: string, oldPin: string, newPin: string): Promise<boolean> {
    try {
      const card = await Card.findOne({ _id: cardId, user_id: userId });
      if (!card) {
        return false;
      }

      const userEncryptionKey = this.generateUserCardKey(userId);
      const decryptedPin = CryptoUtils.decrypt(card.pin, userEncryptionKey);
      if (decryptedPin !== oldPin) {
        return false;
      }

      const encryptedNewPin = CryptoUtils.encrypt(newPin, userEncryptionKey);
      await Card.findByIdAndUpdate(cardId, {
        $set: { pin: encryptedNewPin }
      });

      logger.info('Card PIN changed', {
        userId,
        cardId
      });

      return true;
    } catch (error: any) {
      logger.error('Error changing card PIN:', error);
      return false;
    }
  }

  private static generateUserCardKey(userId: string): string {
    // generate user-specific encryption key for card data
    // this ensures only the user can decrypt their card data
    const userSalt = CryptoUtils.generateHash(userId + 'card_encryption_salt');
    return CryptoUtils.generateHash(userId + userSalt + process.env.CARD_MASTER_KEY);
  }

  private static generateCardNumber(): string {
    // generate 16-digit card number (simplified)
    const prefix = '4532'; // visa prefix
    let cardNumber = prefix;

    for (let i = 0; i < 12; i++) {
      cardNumber += Math.floor(Math.random() * 10);
    }

    return cardNumber;
  }

  private static generateCVV(): string {
    return Math.floor(Math.random() * 900 + 100).toString();
  }

  private static generateExpiryDate(): { month: number; year: number } {
    const now = new Date();
    const year = now.getFullYear() + 3; // 3 years from now
    const month = Math.floor(Math.random() * 12) + 1;

    return { month, year };
  }

  static async toggleBalanceVisibility(cardId: string, userId: string): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      const card = await Card.findOne({ _id: cardId, user_id: userId });

      if (!card) {
        return {
          success: false,
          message: 'card not found'
        };
      }

      await card.toggleBalanceVisibility();

      logger.info('Card balance visibility toggled', {
        cardId,
        userId,
        newVisibility: card.ui_preferences.show_balance
      });

      return {
        success: true,
        data: {
          cardId,
          balanceVisible: card.ui_preferences.show_balance
        },
        message: 'balance visibility updated successfully'
      };
    } catch (error: any) {
      logger.error('Error toggling balance visibility:', error);
      return {
        success: false,
        message: 'failed to update balance visibility'
      };
    }
  }

  static async getCardDisplayData(cardId: string, userId: string): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      const card = await Card.findOne({ _id: cardId, user_id: userId })
        .populate('wallet_id', 'balance currency')
        .populate('user_id', 'first_name last_name');

      if (!card) {
        return {
          success: false,
          message: 'card not found'
        };
      }

      const displayData = card.getDisplayData();
      const tierBenefits = card.getTierBenefits();
      const walletBalance = (card.wallet_id as any)?.balance || 0;

      return {
        success: true,
        data: {
          ...displayData,
          walletBalance: card.ui_preferences.show_balance ? walletBalance : null,
          currency: (card.wallet_id as any)?.currency || 'USD',
          tierInfo: tierBenefits,
          lastUsed: card.usage_stats.last_transaction_date,
          totalSpent: card.usage_stats.total_spent,
          transactionCount: card.usage_stats.total_transactions
        },
        message: 'card display data retrieved successfully'
      };
    } catch (error: any) {
      logger.error('Error getting card display data:', error);
      return {
        success: false,
        message: 'failed to retrieve card display data'
      };
    }
  }

  static async getCopyableCardData(cardId: string, userId: string, pin: string): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        return {
          success: false,
          message: 'user not found'
        };
      }

      const isPinValid = await CryptoUtils.verifyPassword(pin, user.transaction_pin || '');
      if (!isPinValid) {
        securityLogger.warn('Invalid PIN attempt for card data access', {
          userId,
          cardId,
          timestamp: new Date()
        });

        return {
          success: false,
          message: 'invalid transaction pin'
        };
      }

      const card = await Card.findOne({ _id: cardId, user_id: userId });
      if (!card) {
        return {
          success: false,
          message: 'card not found'
        };
      }

      const copyableData = card.getCopyableData();

      securityLogger.info('Card data accessed for copying', {
        userId,
        cardId,
        timestamp: new Date()
      });

      return {
        success: true,
        data: copyableData,
        message: 'card data retrieved for copying'
      };
    } catch (error: any) {
      logger.error('Error getting copyable card data:', error);
      return {
        success: false,
        message: 'failed to retrieve card data'
      };
    }
  }

  static async updateCardTheme(cardId: string, userId: string, theme: string): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      const validThemes = ['default', 'dark', 'blue', 'green', 'purple', 'gold', 'platinum'];

      if (!validThemes.includes(theme)) {
        return {
          success: false,
          message: 'invalid theme selected'
        };
      }

      const card = await Card.findOne({ _id: cardId, user_id: userId });
      if (!card) {
        return {
          success: false,
          message: 'card not found'
        };
      }

      await card.updateCardTheme(theme);

      return {
        success: true,
        data: {
          cardId,
          theme: card.ui_preferences.card_theme
        },
        message: 'card theme updated successfully'
      };
    } catch (error: any) {
      logger.error('Error updating card theme:', error);
      return {
        success: false,
        message: 'failed to update card theme'
      };
    }
  }

  static async getUserCardsWithDetails(userId: string): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      const cards = await Card.find({ user_id: userId })
        .populate('wallet_id', 'balance currency')
        .sort({ created_at: -1 });

      const formattedCards = cards.map(card => {
        const displayData = card.getDisplayData();
        const tierBenefits = card.getTierBenefits();
        const walletBalance = (card.wallet_id as any)?.balance || 0;

        return {
          ...displayData,
          walletBalance: card.ui_preferences.show_balance ? walletBalance : null,
          currency: (card.wallet_id as any)?.currency || 'USD',
          tierInfo: tierBenefits,
          lastUsed: card.usage_stats.last_transaction_date,
          totalSpent: card.usage_stats.total_spent,
          transactionCount: card.usage_stats.total_transactions,
          limits: card.limits,
          settings: card.settings
        };
      });

      return {
        success: true,
        data: {
          cards: formattedCards,
          totalCards: cards.length,
          activeCards: cards.filter(card => card.status === CardStatus.ACTIVE).length
        },
        message: 'user cards retrieved successfully'
      };
    } catch (error: any) {
      logger.error('Error getting user cards:', error);
      return {
        success: false,
        message: 'failed to retrieve user cards'
      };
    }
  }

  static async updateTierInfo(cardId: string, userId: string, tierLevel: string): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      const validTiers = ['basic', 'standard', 'premium', 'platinum'];

      if (!validTiers.includes(tierLevel)) {
        return {
          success: false,
          message: 'invalid tier level'
        };
      }

      const card = await Card.findOne({ _id: cardId, user_id: userId });
      if (!card) {
        return {
          success: false,
          message: 'card not found'
        };
      }

      card.tier_info.tier_level = tierLevel as any;

      const tierHierarchy = ['basic', 'standard', 'premium', 'platinum'];
      const currentIndex = tierHierarchy.indexOf(tierLevel);

      if (currentIndex < tierHierarchy.length - 1) {
        card.tier_info.next_tier = tierHierarchy[currentIndex + 1] || '';
        card.tier_info.upgrade_eligible = true;
      } else {
        card.tier_info.next_tier = '';
        card.tier_info.upgrade_eligible = false;
      }

      await card.save();

      return {
        success: true,
        data: {
          cardId,
          tierInfo: card.getTierBenefits()
        },
        message: 'card tier updated successfully'
      };
    } catch (error: any) {
      logger.error('Error updating card tier:', error);
      return {
        success: false,
        message: 'failed to update card tier'
      };
    }
  }

  private static async verifyTransactionPin(user: any, pin: string): Promise<boolean> {
    try {
      if (!user.transaction_pin) {
        return false;
      }
      return await CryptoUtils.verifyPassword(pin, user.transaction_pin);
    } catch (error) {
      logger.error('Error verifying transaction PIN:', error);
      return false;
    }
  }

  // Card Export Functionality
  static async exportUserCardData(userId: string, pin: string, format: 'json' | 'csv' = 'json'): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        return {
          success: false,
          message: 'user not found'
        };
      }

      const isPinValid = await this.verifyTransactionPin(user, pin);
      if (!isPinValid) {
        logger.warn('Invalid PIN attempt for card data export', {
          userId,
          timestamp: new Date()
        });

        return {
          success: false,
          message: 'invalid transaction pin'
        };
      }

      const cards = await Card.find({ user_id: userId });

      if (!cards.length) {
        return {
          success: false,
          message: 'no cards found for export'
        };
      }

      // Format card data for export
      const exportData = cards.map((card) => {
        return {
          cardId: card._id.toString(),
          cardType: card.card_type,
          cardBrand: card.card_brand,
          cardHolder: card.card_holder_name,
          maskedNumber: card.masked_number,
          expiryDate: card.expiry_date,
          status: card.status,
          currency: card.currency,
          isExpired: card.is_expired,
          createdAt: card.created_at,
          limits: card.limits,
          tierInfo: card.tier_info,
          encryptedData: {
            fullNumber: CryptoUtils.encrypt(card.card_number),
            cvv: CryptoUtils.encrypt(card.cvv),
            pin: CryptoUtils.encrypt(card.pin)
          }
        };
      });

      const exportResult = {
        userId,
        exportedAt: new Date(),
        totalCards: cards.length,
        cards: exportData,
        securityNote: 'This export contains encrypted sensitive data. Keep secure.'
      };

      if (format === 'csv') {
        const csvData = this.convertToCSV(exportData);
        return {
          success: true,
          data: {
            format: 'csv',
            content: csvData,
            filename: `cards_export_${userId}_${Date.now()}.csv`
          },
          message: 'card data exported successfully as CSV'
        };
      }

      return {
        success: true,
        data: {
          format: 'json',
          content: exportResult,
          filename: `cards_export_${userId}_${Date.now()}.json`
        },
        message: 'card data exported successfully'
      };

    } catch (error: any) {
      logger.error('Error exporting card data:', error);
      return {
        success: false,
        message: 'failed to export card data'
      };
    }
  }



  private static convertToCSV(data: any[]): string {
    if (!data.length) return '';

    const headers = [
      'Card ID', 'Type', 'Brand', 'Holder', 'Masked Number',
      'Expiry', 'Status', 'Currency', 'Is Expired', 'Created At'
    ];

    const rows = data.map(card => [
      card.cardId,
      card.cardType,
      card.cardBrand,
      card.cardHolder,
      card.maskedNumber,
      card.expiryDate,
      card.status,
      card.currency,
      card.isExpired,
      card.createdAt
    ]);

    return [headers, ...rows]
      .map(row => row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(','))
      .join('\n');
  }

  static async getCardExportHistory(userId: string): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      // Get export history from audit logs
      const { AuditLog } = await import('../models/audit-log.model');

      const exports = await AuditLog.find({
        user_id: userId,
        action: 'card_data_export',
        created_at: { $gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) } // Last 90 days
      })
      .sort({ created_at: -1 })
      .limit(50)
      .lean();

      const totalExports = await AuditLog.countDocuments({
        user_id: userId,
        action: 'card_data_export'
      });

      const lastExport = exports.length > 0 ? exports[0]?.created_at : null;

      return {
        success: true,
        data: {
          exports: exports.map(exp => ({
            id: exp._id,
            exportedAt: exp.created_at,
            format: exp.metadata?.format || 'json',
            cardCount: exp.metadata?.cardCount || 0,
            ipAddress: exp.ip_address
          })),
          totalExports,
          lastExport
        },
        message: 'export history retrieved successfully'
      };
    } catch (error: any) {
      logger.error('Error getting export history:', error);
      return {
        success: false,
        message: 'failed to get export history'
      };
    }
  }

  // Utility methods for card operations
  private static detectCardBrand(cardNumber: string): CardBrand | null {
    const cleanNumber = cardNumber.replace(/\s/g, '');

    if (/^4/.test(cleanNumber)) return CardBrand.VISA;
    if (/^5[1-5]/.test(cleanNumber)) return CardBrand.MASTERCARD;
    // Add other brands as needed in enum

    return null;
  }

  private static maskCardNumber(cardNumber: string): string {
    const cleanNumber = cardNumber.replace(/\s/g, '');
    if (cleanNumber.length < 8) return '*'.repeat(cleanNumber.length);

    const firstFour = cleanNumber.slice(0, 4);
    const lastFour = cleanNumber.slice(-4);
    const middle = '*'.repeat(cleanNumber.length - 8);

    return `${firstFour}${middle}${lastFour}`;
  }

  private static determineCardType(_cardNumber: string): CardType {
    // For now, assume all user-added cards are debit cards
    // In production, this could be determined by BIN lookup
    return CardType.VIRTUAL; // Use existing enum value
  }

  private static isCardExpired(month: number, year: number): boolean {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;

    if (year < currentYear) return true;
    if (year === currentYear && month < currentMonth) return true;

    return false;
  }

  private static getTierBenefits(_tier: string): any {
    const benefits = {
      basic: {
        cashback_rate: 0.01,
        monthly_limit: 100000,
        international_transactions: false,
        priority_support: false
      },
      silver: {
        cashback_rate: 0.015,
        monthly_limit: 500000,
        international_transactions: true,
        priority_support: false
      },
      gold: {
        cashback_rate: 0.02,
        monthly_limit: 1000000,
        international_transactions: true,
        priority_support: true
      }
    };

    return benefits[_tier as keyof typeof benefits] || benefits.basic;
  }

  private static getDefaultLimits(cardType: CardType): any {
    const limits = {
      [CardType.VIRTUAL]: {
        daily_spend_limit: 30000,
        daily_withdrawal_limit: 0,
        monthly_spend_limit: 300000,
        single_transaction_limit: 5000
      },
      [CardType.PHYSICAL]: {
        daily_spend_limit: 50000,
        daily_withdrawal_limit: 20000,
        monthly_spend_limit: 500000,
        single_transaction_limit: 10000
      }
    };

    return limits[cardType] || limits[CardType.VIRTUAL];
  }
}
