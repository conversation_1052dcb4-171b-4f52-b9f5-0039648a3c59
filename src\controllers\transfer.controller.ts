import { FastifyRequest, FastifyReply } from 'fastify';
import { AuthenticatedRequest, Platform } from '../types';
import { TransferService } from '../services/transfer.service';
import { TransactionService } from '../services/transaction.service';
import { ValidationService } from '../services/validation.service';
import { ExternalApiService } from '../services/external-api.service';
import { TransactionPinService } from '../services/transaction-pin.service';
import { logger } from '../config/logger';

export const sendMoney = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required',
      error_code: 'AUTH_REQUIRED'
    });
  }

  try {
    const validation = await ValidationService.validateData(request.body, ValidationService.transferSchema);
    
    if (!validation.isValid) {
      return reply.status(400).send({
        success: false,
        message: 'validation failed',
        errors: validation.errors,
        error_code: 'VALIDATION_ERROR'
      });
    }

    const result = await TransferService.sendMoney({
      senderId: request.user.id,
      ...validation.data
    });

    return reply.status(200).send({
      success: true,
      message: 'money sent successfully',
      data: {
        transferDetails: {
          transferId: result.transferId,
          transactionRef: result.transactionRef,
          amount: result.amount,
          fee: result.fee,
          totalAmount: result.totalAmount,
          currency: validation.data.currency,
          status: result.status,
          createdAt: new Date().toISOString(),
          estimatedDelivery: result.estimatedDelivery,
          transferType: "p2p"
        },
        recipientInfo: {
          recipientId: result.recipient.id,
          recipientName: result.recipient.name,
          recipientEmail: result.recipient.email,
          recipientPhone: result.recipient.phone,
          isRegistered: true
        },
        senderInfo: {
          senderId: request.user.id
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        timestamp: new Date().toISOString(),
        processingTimeMs: Date.now() - ((request as any).startTime || Date.now()),
        apiVersion: "v1"
      }
    });
  } catch (error: any) {
    logger.error('send money error:', error);
    
    const errorMessages: Record<string, string> = {
      'sender not found': 'sender account not found',
      'recipient not found': 'recipient not found or not registered',
      'insufficient balance': 'insufficient wallet balance',
      'sender wallet not found': 'sender wallet not available',
      'recipient wallet not found': 'recipient wallet not available'
    };

    const message = errorMessages[error.message] || 'transfer failed';
    
    return reply.status(400).send({
      success: false,
      message,
      error_code: 'TRANSFER_FAILED',
      error_details: {
        reason: error.message,
        timestamp: new Date().toISOString()
      }
    });
  }
};

export const walletToWallet = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { recipientPhone, amount, currency, pin, description } = request.body as any;
    const platform = (request.query as any)?.platform || Platform.WEB;
    const ipAddress = request.ip;

    if (!recipientPhone || !amount || !currency || !pin) {
      return reply.status(400).send({
        success: false,
        message: 'recipient phone, amount, currency and PIN required'
      });
    }

    const pinVerification = await TransactionPinService.verifyPinForTransaction(
      request.user.id,
      pin,
      amount,
      platform,
      ipAddress
    );

    if (!pinVerification.authorized) {
      return reply.status(400).send({
        success: false,
        message: pinVerification.message
      });
    }

    const result = await TransferService.sendMoney({
      senderId: request.user.id,
      recipientPhone,
      amount,
      currency,
      pin,
      description,
      metadata: {
        transferType: 'wallet_to_wallet',
        platform,
        ipAddress,
        transactionId: pinVerification.transactionId
      }
    });

    return reply.status(200).send({
      success: true,
      message: 'wallet transfer completed successfully',
      data: {
        transferId: result.transferId,
        transactionRef: result.transactionRef,
        amount: result.amount,
        fee: result.fee,
        totalAmount: result.totalAmount,
        recipient: result.recipient,
        status: result.status,
        estimatedDelivery: result.estimatedDelivery,
        transferType: 'wallet_to_wallet'
      }
    });

  } catch (error: any) {
    logger.error('wallet to wallet transfer error:', error);

    if (error.message.includes('PIN')) {
      return reply.status(400).send({
        success: false,
        message: error.message
      });
    }

    return reply.status(500).send({
      success: false,
      message: 'wallet transfer failed'
    });
  }
};

export const businessTransfer = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { businessId, amount, currency, pin, description, accountNumber } = request.body as any;
    const platform = (request.query as any)?.platform || Platform.WEB;
    const ipAddress = request.ip;

    if (!businessId || !amount || !currency || !pin) {
      return reply.status(400).send({
        success: false,
        message: 'business ID, amount, currency and PIN required'
      });
    }

    // verify transaction PIN
    const pinVerification = await TransactionPinService.verifyPinForTransaction(
      request.user.id,
      pin,
      amount,
      platform,
      ipAddress
    );

    if (!pinVerification.authorized) {
      return reply.status(400).send({
        success: false,
        message: pinVerification.message
      });
    }

    const result = await TransferService.businessTransfer({
      senderId: request.user.id,
      businessId,
      amount,
      currency,
      pin,
      description,
      accountNumber,
      metadata: {
        transferType: 'business',
        platform,
        ipAddress,
        transactionId: pinVerification.transactionId
      }
    });

    return reply.status(200).send({
      success: true,
      message: 'business transfer completed successfully',
      data: result
    });

  } catch (error: any) {
    logger.error('business transfer error:', error);

    return reply.status(500).send({
      success: false,
      message: 'business transfer failed'
    });
  }
};

export const internationalTransfer = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const {
      recipientCountry,
      recipientName,
      recipientPhone,
      amount,
      sourceCurrency,
      targetCurrency,
      deliveryMethod,
      bankDetails,
      pin,
      description
    } = request.body as any;

    const platform = (request.query as any)?.platform || Platform.WEB;
    const ipAddress = request.ip;

    if (!recipientCountry || !recipientName || !amount || !sourceCurrency || !targetCurrency || !pin) {
      return reply.status(400).send({
        success: false,
        message: 'recipient details, amount, currencies and PIN required'
      });
    }

    // verify transaction PIN
    const pinVerification = await TransactionPinService.verifyPinForTransaction(
      request.user.id,
      pin,
      amount,
      platform,
      ipAddress
    );

    if (!pinVerification.authorized) {
      return reply.status(400).send({
        success: false,
        message: pinVerification.message
      });
    }

    const result = await TransferService.sendRemittance({
      senderId: request.user.id,
      recipientCountry,
      recipientName,
      recipientPhone,
      amount,
      sourceCurrency,
      targetCurrency,
      deliveryMethod: deliveryMethod || 'bank',
      bankDetails,
      description,
      metadata: {
        transferType: 'international',
        platform,
        ipAddress,
        transactionId: pinVerification.transactionId
      }
    });

    return reply.status(200).send({
      success: true,
      message: 'international transfer initiated successfully',
      data: result
    });

  } catch (error: any) {
    logger.error('international transfer error:', error);

    return reply.status(500).send({
      success: false,
      message: 'international transfer failed'
    });
  }
};

export const sendRemittance = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user authentication required'
    });
  }

  try {
    // validate remittance data
    const validation = await ValidationService.validateData(request.body, ValidationService.remittanceSchema);

    if (!validation.isValid) {
      return reply.status(400).send({
        success: false,
        message: 'validation failed',
        errors: validation.errors
      });
    }

    const data = request.body as any;

    const result = await TransferService.sendRemittance({
      senderId: request.user.id,
      ...data
    });

    return reply.status(200).send({
      success: true,
      message: 'remittance initiated successfully',
      data: {
        remittanceTransaction: {
          remittanceId: result.remittanceId,
          referenceNumber: result.referenceNumber,
          trackingCode: result.trackingCode,
          sourceAmount: result.sourceAmount,
          targetAmount: result.targetAmount,
          exchangeRate: result.exchangeRate,
          processingFee: result.fee,
          totalCost: result.totalCost,
          estimatedDelivery: result.estimatedDelivery,
          currentStatus: 'processing',
          initiatedAt: new Date().toISOString(),
          remittanceType: "international"
        },
        recipientDetails: {
          recipientName: result.recipient.name,
          recipientPhone: result.recipient.phone,
          recipientCountry: result.recipient.country,
          deliveryMethod: result.recipient.deliveryMethod
        },
        exchangeDetails: {
          sourceCurrency: data.sourceCurrency,
          targetCurrency: data.targetCurrency,
          currentRate: result.exchangeRate,
          rateTimestamp: new Date().toISOString(),
          rateProvider: "central_bank"
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        processingTimeMs: Date.now() - ((request as any).startTime || Date.now()),
        apiVersion: "v1",
        serviceType: "remittance"
      }
    });
  } catch (error: any) {
    logger.error('remittance error:', error);
    
    return reply.status(400).send({
      success: false,
      message: 'remittance failed',
      error: error.message,
      errorCode: 'REMITTANCE_FAILED'
    });
  }
};

export const payBill = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const data = request.body as any;

    const result = await TransferService.payBill({
      userId: request.user.id,
      ...data
    });

    return reply.status(200).send({
      success: true,
      message: 'bill payment successful',
      data: {
        paymentTransaction: {
          paymentId: result.paymentId,
          paymentReference: result.referenceNumber,
          billAmount: result.amount,
          serviceFee: result.fee,
          totalAmountPaid: result.totalAmount,
          paymentStatus: result.status,
          confirmationCode: result.confirmationCode,
          processedAt: new Date().toISOString(),
          paymentMethod: "wallet"
        },
        billDetails: {
          providerName: result.provider,
          customerAccount: result.accountNumber,
          billCategory: data.billType
        },
        customerInfo: {
          customerId: request.user.id
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        processedAt: new Date().toISOString(),
        serviceType: "bill_payment"
      }
    });
  } catch (error: any) {
    logger.error('bill payment error:', error);
    
    return reply.status(400).send({
      success: false,
      message: 'bill payment failed',
      error: error.message,
      error_code: 'BILL_PAYMENT_FAILED'
    });
  }
};

export const getTransferHistory = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { page = 1, limit = 20, type, status, startDate, endDate } = request.query as any;

    // get actual transfer history from transactions
    const options: any = {
      page: parseInt(page),
      limit: Math.min(parseInt(limit), 100)
    };

    // filter by transaction type if specified
    if (type) {
      const transferTypes = ['p2p_transfer', 'remittance', 'cash_in', 'cash_out'];
      if (transferTypes.includes(type)) {
        options.type = type;
      }
    }

    // filter by status if specified
    if (status) {
      options.status = status;
    }

    // filter by date range if specified
    if (startDate) {
      options.startDate = new Date(startDate);
    }
    if (endDate) {
      options.endDate = new Date(endDate);
    }

    const result = await TransactionService.getUserTransactions(request.user.id, options);

    // format transfers for response
    const transfers = result.transactions.map(transaction => ({
      id: transaction._id.toString(),
      reference: transaction.transaction_ref,
      type: transaction.type,
      amount: transaction.amount,
      fee: transaction.fee,
      currency: transaction.currency,
      status: transaction.status,
      recipient: transaction.recipient_id ? {
        name: (transaction.recipient_id as any)?.first_name + ' ' + (transaction.recipient_id as any)?.last_name,
        email: (transaction.recipient_id as any)?.email
      } : null,
      description: transaction.description,
      createdAt: transaction.created_at,
      completedAt: transaction.processing?.completed_at,
      balanceBefore: transaction.balance_before,
      balanceAfter: transaction.balance_after,
      metadata: {
        walletType: (transaction.wallet_id as any)?.wallet_type,
        exchangeRate: transaction.metadata?.exchange_rate,
        originalAmount: transaction.metadata?.original_amount,
        originalCurrency: transaction.metadata?.original_currency,
        externalReference: transaction.external_reference,
        externalProvider: transaction.external_provider
      }
    }));

    return reply.status(200).send({
      success: true,
      message: 'transfer history retrieved successfully',
      data: {
        transfers,
        pagination: {
          currentPage: result.page,
          itemsPerPage: options.limit,
          totalItems: result.total,
          totalPages: result.pages,
          hasNext: result.page < result.pages,
          hasPrevious: result.page > 1
        },
        summary: {
          totalTransfers: result.total,
          totalAmount: transfers.reduce((sum, t) => sum + t.amount, 0),
          totalFees: transfers.reduce((sum, t) => sum + t.fee, 0),
          completedTransfers: transfers.filter(t => t.status === 'completed').length,
          pendingTransfers: transfers.filter(t => t.status === 'pending').length
        }
      },
      meta: {
        request_id: (request as any).requestId,
        filters_applied: {
          type: type || null,
          status: status || null,
          date_range: {
            start: startDate || null,
            end: endDate || null
          }
        }
      }
    });
  } catch (error: any) {
    logger.error('get transfer history error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve transfer history'
    });
  }
};

export const getExchangeRates = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const { from, to } = request.query as any;

    const ratesData = await ExternalApiService.getExchangeRates(from, to);

    const result = ratesData.rates;

    return reply.status(200).send({
      success: true,
      message: 'exchange rates retrieved',
      data: {
        rates: result,
        last_updated: ratesData.timestamp,
        source: ratesData.source
      },
      meta: {
        request_id: (request as any).requestId,
        cache_ttl: 300,
        api_version: "v1"
      }
    });
  } catch (error: any) {
    logger.error('get exchange rates error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'failed to get exchange rates'
    });
  }
};

export const getBillProviders = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const { country, billType } = request.query as any;

    const providersData = await ExternalApiService.getBillProviders(country, billType);
    const filteredProviders = providersData.providers;

    return reply.status(200).send({
      success: true,
      message: 'bill providers retrieved',
      data: {
        providers: filteredProviders,
        total_count: filteredProviders.length
      },
      meta: {
        filters: {
          country: country || null,
          billType: billType || null
        }
      }
    });
  } catch (error: any) {
    logger.error('get bill providers error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'failed to get bill providers'
    });
  }
};
